import * as React from "react"
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TextProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  foreground: '#0f172a',
  border: '#e2e8f0',
  destructive: '#ef4444',
  destructiveBorder: '#fecaca',
}

export type AlertVariant = 'default' | 'destructive'

export interface AlertProps extends ViewProps {
  variant?: AlertVariant
  children?: React.ReactNode
  style?: ViewStyle
}

export interface AlertTitleProps extends TextProps {
  style?: TextStyle
  children?: React.ReactNode
}

export interface AlertDescriptionProps extends TextProps {
  style?: TextStyle
  children?: React.ReactNode
}

const Alert = React.forwardRef<View, AlertProps>(
  ({ variant = 'default', children, style, ...props }, ref) => {
    const alertStyle = getAlertStyle(variant)

    return (
      <View
        ref={ref}
        style={[styles.alert, alertStyle, style]}
        {...props}
      >
        {children}
      </View>
    )
  }
)

const AlertTitle = React.forwardRef<Text, AlertTitleProps>(
  ({ style, children, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.alertTitle, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

const AlertDescription = React.forwardRef<Text, AlertDescriptionProps>(
  ({ style, children, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.alertDescription, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

Alert.displayName = "Alert"
AlertTitle.displayName = "AlertTitle"
AlertDescription.displayName = "AlertDescription"

const getAlertStyle = (variant: AlertVariant): ViewStyle => {
  switch (variant) {
    case 'destructive':
      return {
        borderColor: colors.destructiveBorder,
        backgroundColor: '#fef2f2',
      }
    default:
      return {
        borderColor: colors.border,
        backgroundColor: colors.background,
      }
  }
}

const styles = StyleSheet.create({
  alert: {
    width: '100%',
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.foreground,
    lineHeight: 20,
    marginBottom: 4,
  },
  alertDescription: {
    fontSize: 14,
    color: colors.foreground,
    lineHeight: 20,
  },
})

export { Alert, AlertTitle, AlertDescription }
