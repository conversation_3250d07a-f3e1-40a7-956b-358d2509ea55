import * as React from "react"
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
  Animated,
} from "react-native"

// Define color constants for React Native
const colors = {
  muted: '#f1f5f9',
  mutedDark: '#e2e8f0',
}

export interface SkeletonProps extends ViewProps {
  style?: ViewStyle
}

const Skeleton = React.forwardRef<View, SkeletonProps>(
  ({ style, ...props }, ref) => {
    const animatedValue = React.useRef(new Animated.Value(0)).current

    React.useEffect(() => {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: false,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: false,
          }),
        ])
      )
      animation.start()

      return () => animation.stop()
    }, [animatedValue])

    const backgroundColor = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [colors.muted, colors.mutedDark],
    })

    return (
      <Animated.View
        ref={ref}
        style={[
          styles.skeleton,
          { backgroundColor },
          style,
        ]}
        {...props}
      />
    )
  }
)

Skeleton.displayName = "Skeleton"

const styles = StyleSheet.create({
  skeleton: {
    borderRadius: 6,
    backgroundColor: colors.muted,
  },
})

export { Skeleton }
