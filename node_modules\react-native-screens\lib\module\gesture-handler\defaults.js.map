{"version": 3, "names": ["ScreenTransition", "DefaultEvent", "absoluteX", "absoluteY", "handlerTag", "numberOfPointers", "state", "translationX", "translationY", "velocityX", "velocityY", "x", "y", "DefaultScreenDimensions", "width", "height", "pageX", "pageY", "AnimationForGesture", "swipeRight", "SwipeRight", "swipeLeft", "SwipeLeft", "swipeDown", "SwipeDown", "swipeUp", "SwipeUp", "horizontalSwipe", "Horizontal", "verticalSwipe", "Vertical", "twoDimensionalSwipe", "TwoDimensional"], "sourceRoot": "../../../src", "sources": ["gesture-handler/defaults.ts"], "mappings": "AAIA,SAASA,gBAAgB,QAAQ,yBAAyB;AAE1D,OAAO,MAAMC,YAA+D,GAAG;EAC7EC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE,CAAC;EACbC,gBAAgB,EAAE,CAAC;EACnBC,KAAK,EAAE,CAAC;EACRC,YAAY,EAAE,CAAC;EACfC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AAED,OAAO,MAAMC,uBAAuB,GAAG;EACrCC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTJ,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJI,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,UAAU,EAAEnB,gBAAgB,CAACoB,UAAU;EACvCC,SAAS,EAAErB,gBAAgB,CAACsB,SAAS;EACrCC,SAAS,EAAEvB,gBAAgB,CAACwB,SAAS;EACrCC,OAAO,EAAEzB,gBAAgB,CAAC0B,OAAO;EACjCC,eAAe,EAAE3B,gBAAgB,CAAC4B,UAAU;EAC5CC,aAAa,EAAE7B,gBAAgB,CAAC8B,QAAQ;EACxCC,mBAAmB,EAAE/B,gBAAgB,CAACgC;AACxC,CAAC"}