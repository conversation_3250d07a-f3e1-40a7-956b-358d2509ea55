
"use client";

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Users2, PlayCircle, Repeat, Heart, BookOpen, HandHelping, CircleDot } from 'lucide-react';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';
import { useLanguage, type Language } from '@/context/LanguageContext';
import React from 'react';

type ZikrStep = {
  step: string;
  title: string;
  description: string;
  recitation?: string; // Arabic script for all languages
  recitation_ro?: string; // Roman Urdu transliteration for RO
  recitation_hi?: string; // Hindi transliteration for HI
  icon: React.ElementType;
};

type HalqaeZikrContent = {
  pageTitle: string;
  pageSubtitle: string;
  backToPractices: string;
  stepsHeading: string;
  zikrSteps: ZikrStep[];
};

const translations: Record<Language, HalqaeZikrContent> = {
  ur: {
    pageTitle: "حلقۂ ذکر",
    pageSubtitle: "اجتماعی ذکر کی فضیلت و طریقہ",
    backToPractices: "روحانی اعمال کی طرف واپس",
    stepsHeading: "حلقہ ذکر کے مراحل",
    zikrSteps: [
      { step: "۱۔", title: "آغاز", description: "باوضو ہو کر، ادب سے حلقہ بنا کر بیٹھیں اور دل میں اللہ کی حضوری کا تصور کریں۔ آغاز درود شریف سے کریں، اس کے بعد \"حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ\" (کم از کم ۳ یا ۱۱ بار) پڑھیں۔", recitation: "حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ", icon: PlayCircle },
      { step: "۲۔", title: "ذکرِ کلمۂِ توحید", description: "پھر کلمۂِ توحید \"لَا اِلٰہَ اِلَّا اللہ\" کا ذکر شروع کریں۔ 'لَا اِلٰہَ' کہتے ہوئے (غیر اللہ کی) نفی کریں اور 'اِلَّا اللہ' کی پُرزور ضرب (اللہ کے اثبات کی) اپنے قلب پر لگائیں۔ یہ ذکر مقررہ تعداد یا وقت تک کریں۔", recitation: "لَا اِلٰہَ اِلَّا اللہ", icon: Repeat },
      { step: "۳۔", title: "ذکرِ اسمِ ذات (اللہ)", description: "اس کے بعد ، پورے دھیان اور قلبی توجہ کے ساتھ \"اَللّٰہُ، اَللّٰہُ، اَللّٰہُ\" کا ورد کریں۔", recitation: "اَللّٰہُ، اَللّٰہُ، اَللّٰہُ", icon: Heart },
      { step: "۴۔", title: "ذکرِ اسمِ ذات (وقف کے ساتھ)", description: "پھر \"اَللہ، اَللہ\" (بغیر پیش کے، یعنی اللہ پر وقف کرتے ہوئے) کا ذکر، ہر دھڑکن اور سانس کے ساتھ، دل کی گہرائیوں سے کریں۔", recitation: "اَللہ، اَللہ", icon: Heart },
      { step: "۵۔", title: "ذکرِ ھُوْ", description: "اس کے بعد \"ھُوْ، ھُوْ، ھُوْ\" کا ذکر کریں۔", recitation: "ھُوْ، ھُوْ، ھُوْ", icon: CircleDot },
      { step: "۶۔", title: "ذکرِ اسمائے الٰہی", description: "ذکرِ اسمائے الٰہی \"یَا حَیُّ یَا قَیُّوْمُ \"، \"یَا وَھَّابُ\"، \"یَا فَتَّاحُ\"", recitation: "یَا حَیُّ یَا قَیُّوْمُ، یَا وَھَّابُ، یَا فَتَّاحُ", icon: Repeat },
      { step: "۷۔", title: "مراقبہ و دعا", description: "ذکر کے بعد چند لمحات کے لیے آنکھیں بند کر کے، نہایت سکون اور یکسوئی سے اپنے قلب کی طرف متوجہ ہو کر مراقبہ کریں اور آخر میں دوبارہ درود شریف پڑھ کر اللہ تعالیٰ کے حضور اجتماعی طور پر ملک و ملت، امت مسلمہ اور تمام انسانیت کی بھلائی کے لیے دعا کریں۔", icon: HandHelping }
    ]
  },
  en: {
    pageTitle: "Halqa-e-Zikr (Circle of Remembrance)",
    pageSubtitle: "Virtues and Method of Congregational Zikr",
    backToPractices: "Back to Spiritual Practices",
    stepsHeading: "Stages of Halqa-e-Zikr",
    zikrSteps: [
      { step: "1.", title: "Commencement", description: "Perform ablution (Wudu), sit respectfully in a circle, and imagine the presence of Allah in your heart. Begin with Durood Sharif, then recite \"Hasbi Rabbi Jallallah, Ma Fi Qalbi Ghayrullah, Noor-e-Muhammad Sallallah, La ilaha illallah\" (at least 3 or 11 times).", recitation: "حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ", icon: PlayCircle },
      { step: "2.", title: "Zikr of Kalima-e-Tawhid", description: "Then begin the Zikr of Kalima-e-Tawhid \"La ilaha illallah\". While saying 'La ilaha' (negate everything other than Allah) and with 'illallah' (affirmation of Allah), strike your heart forcefully. Continue this Zikr for a prescribed number of times or duration.", recitation: "لَا اِلٰہَ اِلَّا اللہ", icon: Repeat },
      { step: "3.", title: "Zikr of Ism-e-Zaat (Allah)", description: "After that, with full attention and heartfelt focus, chant \"Allahu, Allahu, Allahu\".", recitation: "اَللّٰہُ، اَللّٰہُ، اَللّٰہُ", icon: Heart },
      { step: "4.", title: "Zikr of Ism-e-Zaat (with pause)", description: "Then, perform the Zikr of \"Allah, Allah\" (without the vowel sign Damma on 'Allah', i.e., pausing on the word Allah), with every heartbeat and breath, from the depths of your heart.", recitation: "اَللہ، اَللہ", icon: Heart },
      { step: "5.", title: "Zikr of Hu", description: "After that, perform the Zikr of \"Hu, Hu, Hu\".", recitation: "ھُوْ، ھُوْ، ھُوْ", icon: CircleDot },
      { step: "6.", title: "Zikr of Divine Names", description: "Perform Zikr of the Divine Names: \"Ya Hayyu Ya Qayyum\", \"Ya Wahhabu\", \"Ya Fattahu\".", recitation: "یَا حَیُّ یَا قَیُّوْمُ ، یَا وَھَّابُ، یَا فَتَّاحُ", icon: Repeat },
      { step: "7.", title: "Muraqaba (Meditation) & Dua", description: "After the Zikr, close your eyes for a few moments, and with utmost peace and concentration, turn your attention towards your heart and perform Muraqaba (meditation). Finally, recite Durood Sharif again and collectively supplicate to Allah Almighty for the well-being of the country, the nation, the Muslim Ummah, and all of humanity.", icon: HandHelping }
    ]
  },
  ro: {
    pageTitle: "Halqa-e-Zikr",
    pageSubtitle: "Ijtima'i Zikr ki Fazilat o Tareeqa",
    backToPractices: "Roohaani Amaal ki Taraf Wapas",
    stepsHeading: "Halqa-e-Zikr ke Maraahil",
    zikrSteps: [
      { step: "1.", title: "Aaghaaz", description: "Ba-wuzu ho kar, adab se halqa bana kar baithein aur dil mein Allah ki huzoori ka tasawwur karein. Aaghaaz Durood Sharif se karein, iske ba'd \"Hasbi Rabbi Jallallah, Ma Fi Qalbi Ghayrullah, Noor-e-Muhammad Sallallah, La ilaha illallah\" (kam az kam 3 ya 11 baar) parhein.", recitation: "حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ", recitation_ro: "Hasbi Rabbi Jallallah, Ma Fi Qalbi Ghayrullah, Noor-e-Muhammad Sallallah, La ilaha illallah", icon: PlayCircle },
      { step: "2.", title: "Zikr-e-Kalima-e-Tawhid", description: "Phir Kalima-e-Tawhid \"La ilaha illallah\" ka zikr shuru karein. 'La ilaha' kehte hue (ghair Allah ki) nafi karein aur 'illallah' ki pur-zor zarb (Allah ke isbaat ki) apne qalb par lagayein. Yeh zikr muqarrara ta'daad ya waqt tak karein.", recitation: "لَا اِلٰہَ اِلَّا اللہ", recitation_ro: "La ilaha illallah", icon: Repeat },
      { step: "3.", title: "Zikr-e-Ism-e-Zaat (Allah)", description: "Iske ba'd, poore dhyaan aur qalbi tawajjuh ke saath \"Allahu, Allahu, Allahu\" ka wird karein.", recitation: "اَللّٰہُ، اَللّٰہُ، اَللّٰہُ", recitation_ro: "Allahu, Allahu, Allahu", icon: Heart },
      { step: "4.", title: "Zikr-e-Ism-e-Zaat (waqf ke saath)", description: "Phir \"Allah, Allah\" (baghair pesh ke, ya'ni Allah par waqf karte hue) ka zikr, har dhadkan aur saans ke saath, dil ki gehraiyon se karein.", recitation: "اَللہ، اَللہ", recitation_ro: "Allah, Allah", icon: Heart },
      { step: "5.", title: "Zikr-e-Hu", description: "Iske ba'd \"Hu, Hu, Hu\" ka zikr karein.", recitation: "ھُوْ، ھُوْ، ھُوْ", recitation_ro: "Hu, Hu, Hu", icon: CircleDot },
      { step: "6.", title: "Zikr-e-Asma-e-Ilahi", description: "Zikr-e-Asma-e-Ilahi \"Ya Hayyu Ya Qayyum\", \"Ya Wahhabu\", \"Ya Fattahu\".", recitation: "یَا حَیُّ یَا قَیُّوْمُ ، یَا وَھَّابُ، یَا فَتَّاحُ", recitation_ro: "Ya Hayyu Ya Qayyum, Ya Wahhabu, Ya Fattahu", icon: Repeat },
      { step: "7.", title: "Muraqaba wa Dua", description: "Zikr ke ba'd chand lamhaat ke liye aankhein band kar ke, nihaayat sukoon aur yaksooi se apne qalb ki taraf mutawajjah ho kar muraqaba karein aur aakhir mein dobaara Durood Sharif parh kar Allah Ta'ala ke huzoor ijtima'i taur par mulk o millat, Ummat-e-Muslima aur tamaam insaaniyat ki bhalaai ke liye dua karein.", icon: HandHelping }
    ]
  },
  hi: {
    pageTitle: "हल्क़ा-ए-ज़िक्र",
    pageSubtitle: "इज्तिमाई ज़िक्र की फ़ज़ीलत ओ तरीक़ा",
    backToPractices: "रूहानी आमाल की तरफ़ वापस",
    stepsHeading: "हल्क़ा-ए-ज़िक्र के मराहिल",
    zikrSteps: [
      { step: "१.", title: "आग़ाज़", description: "बा-वज़ू हो कर, अदब से हल्क़ा बना कर बैठें और दिल में अल्लाह की हुज़ूरी का तसव्वुर करें। आग़ाज़ दरूद शरीफ़ से करें, इसके बाद \"हस्बी रब्बी जल्लल्लाह, मा फ़ी क़ल्बी ग़ैर्रुल्लाह, नूर-ए-मुहम्मद सल्लल्लाह, ला इलाहा इल्लल्लाह\" (कम अज़ कम ३ या ११ बार) पढ़ें।", recitation: "حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ", recitation_hi: "हस्बी रब्बी जल्लल्लाह, मा फ़ी क़ल्बी ग़ैर्रुल्लाह, नूर-ए-मुहम्मद सल्लल्लाह, ला इलाहा इल्लल्लाह", icon: PlayCircle },
      { step: "२.", title: "ज़िक्र-ए-कलिमा-ए-तौहीद", description: "फिर कलिमा-ए-तौहीद \"ला इलाहा इल्लल्लाह\" का ज़िक्र शुरू करें। 'ला इलाहा' कहते हुए (ग़ैर अल्लाह की) नफ़ी करें और 'इल्लल्लाह' की पुर-ज़ोर ज़र्ब (अल्लाह के इस्बात की) अपने क़ल्ब पर लगाएँ। यह ज़िक्र मुक़र्ररा तादाद या वक़्त तक करें।", recitation: "لَا اِلٰہَ اِلَّا اللہ", recitation_hi: "ला इलाहा इल्लल्लाह", icon: Repeat },
      { step: "३.", title: "ज़िक्र-ए-इस्मे-ज़ात (अल्लाह)", description: "इसके बाद, पूरे ध्यान और क़ल्बी तवज्जो के साथ \"अल्लाहु, अल्लाहु, अल्लाहु\" का विर्द करें।", recitation: "اَللّٰہُ، اَللّٰہُ، اَللّٰہُ", recitation_hi: "अल्लाहु, अल्लाहु, अल्लाहु", icon: Heart },
      { step: "४.", title: "ज़िक्र-ए-इस्मे-ज़ात (वक़्फ़ के साथ)", description: "फिर \"अल्लाह, अल्लाह\" (बग़ैर पेश के, यानी अल्लाह पर वक़्फ़ करते हुए) का ज़िक्र, हर धड़कन और सांस के साथ, दिल की गहराइयों से करें।", recitation: "اَللہ، اَللہ", recitation_hi: "अल्लाह, अल्लाह", icon: Heart },
      { step: "५.", title: "ज़िक्र-ए-हू", description: "इसके बाद \"हू, हू, हू\" का ज़िक्र करें।", recitation: "ھُوْ، ھُوْ، ھُوْ", recitation_hi: "हू, हू, हू", icon: CircleDot },
      { step: "६.", title: "ज़िक्र-ए-अस्मा-ए-इलाही", description: "ज़िक्र-ए-अस्मा-ए-इलाही \"या हय्यु या क़य्यूम\", \"या वह्हाबु\", \"या फ़त्ताहु\"", recitation: "یَا حَیُّ یَا قَیُّوْمُ ، یَا وَھَّابُ، یَا فَتَّاحُ", recitation_hi: "या हय्यु या क़य्यूम, या वह्हाबु, या फ़त्ताहु", icon: Repeat },
      { step: "७.", title: "मुराक़बा व दुआ", description: "ज़िक्र के बाद चंद लम्हात के लिए आँखें बंद कर के, निहायत सुकून और यकसूई से अपने क़ल्ब की तरफ़ मुतवज्जेह हो कर मुराक़बा करें और आख़िर में दोबारा दरूद शरीफ़ पढ़ कर अल्लाह तआला के हुज़ूर इज्तिमाई तौर पर मुल्क ओ मिल्लत, उम्मत-ए-मुस्लिमा और तमाम इंसानियत की भलाई के लिए दुआ करें।", icon: HandHelping }
    ]
  },
  ar: {
    pageTitle: "حلقة الذكر",
    pageSubtitle: "فضل وطريقة الذكر الجماعي",
    backToPractices: "العودة إلى الممارسات الروحية",
    stepsHeading: "مراحل حلقة الذكر",
    zikrSteps: [
      { step: "١.", title: "البداية", description: "توضأ واجلس في حلقة بأدب وتصور حضور الله في قلبك. ابدأ بالصلاة على النبي (درود شريف)، ثم اقرأ \"حَسْبِيْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ\" (٣ أو ١١ مرة على الأقل).", recitation: "حَسْبِیْ رَبِّیْ جَلَّ اللہ، مَا فِیْ قَلْبِیْ غَیْرُ اللہ، نُوْرِ مُحَمَّدٍ صَلَّی اللہ، لَا اِلٰہَ اِلَّا اللہ", icon: PlayCircle },
      { step: "٢.", title: "ذكر كلمة التوحيد", description: "ثم ابدأ بذكر كلمة التوحيد \"لَا اِلٰہَ اِلَّا اللہ\". عند قول 'لَا اِلٰہَ' (انفِ غير الله) ومع 'اِلَّا اللہ' (إثبات الله)، اضرب قلبك بقوة. استمر في هذا الذكر لعدد معين أو لوقت محدد.", recitation: "لَا اِلٰہَ اِلَّا اللہ", icon: Repeat },
      { step: "٣.", title: "ذكر اسم الذات (الله)", description: "بعد ذلك، ردد \"اَللّٰہُ، اَللّٰہُ، اَللّٰہُ\" بانتباه كامل وتركيز قلبي.", recitation: "اَللّٰہُ، اَللّٰہُ، اَللّٰہُ", icon: Heart },
      { step: "٤.", title: "ذكر اسم الذات (مع الوقف)", description: "ثم اذكر \"اَلله، اَلله\" (بدون الضمة على الهاء، أي بالوقف على لفظ الجلالة)، مع كل نبضة قلب ونفس، من أعماق قلبك.", recitation: "اَلله، اَلله", icon: Heart },
      { step: "٥.", title: "ذكر (هُو)", description: "بعد ذلك، اذكر \"ھُوْ، ھُوْ، ھُوْ\".", recitation: "ھُوْ، ھُوْ، ھُوْ", icon: CircleDot },
      { step: "٦.", title: "ذكر الأسماء الإلهية", description: "ذكر الأسماء الإلهية: \"یَا حَیُّ یَا قَیُّوْمُ \"، \"یَا وَھَّابُ\"، \"یَا فَتَّاحُ\".", recitation: "یَا حَیُّ یَا قَیُّوْمُ، یَا وَھَّابُ، یَا فَتَّاحُ", icon: Repeat },
      { step: "٧.", title: "المراقبة والدعاء", description: "بعد الذكر، أغلق عينيك لبضع لحظات، وتوجه إلى قلبك بسكينة وتركيز تام لأداء المراقبة. وفي الختام، اقرأ الصلاة على النبي مرة أخرى وادعوا الله جماعيًا لخير البلاد والملة والأمة الإسلامية والإنسانية جمعاء.", icon: HandHelping }
    ]
  }
};


export default function HalqaeZikrPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  return (
    <div className="space-y-8">
      <div className="px-0">
        <PageTitle 
          title={content.pageTitle} 
          subtitle={content.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

        <>
          <h2 className={cn("text-2xl font-semibold text-center my-6 text-primary", fontClass, textDir === 'rtl' ? 'text-right' : 'text-center')}>
              {content.stepsHeading}
          </h2>
          <div className="space-y-6">
            {content.zikrSteps.map((item, index) => {
              const IconComponent = item.icon;
              const recitationText = item.recitation;
              let transliterationText = "";
              if (language === 'ro' && item.recitation_ro) {
                transliterationText = item.recitation_ro;
              } else if (language === 'hi' && item.recitation_hi) {
                transliterationText = item.recitation_hi;
              }

              return (
                <Card key={index} className="shadow-lg bg-card">
                  <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
                    <h3 className={cn("text-xl md:text-2xl font-semibold text-primary flex items-center", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                      <IconComponent className={cn("h-6 w-6", textDir === 'rtl' ? "ml-2" : "mr-2")} />
                      {item.step} {item.title}
                    </h3>
                  </CardHeader>
                  <CardContent
                    lang={language}
                    dir={textDir}
                    className={cn("space-y-3 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}
                  >
                    <p>{item.description}</p>
                    {recitationText && (
                      <div lang="ar" dir="rtl" className={cn("font-arabic text-xl md:text-2xl my-3 p-3 bg-muted/50 rounded-md text-center shadow-inner leading-loose")}>
                        {recitationText}
                      </div>
                    )}
                    {transliterationText && (
                       <p className="text-md text-muted-foreground mt-1 font-sans text-center italic">({transliterationText})</p>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </>
    </div>
  );
}


    