
"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card'; // Card is not used directly in this component's output anymore
import { RotateCcw } from 'lucide-react';
import { cn } from '@/lib/utils';

type DigitalTasbeehProps = {
  initialCount?: number;
  targetCount?: number;
  storageKey?: string; // Optional key for localStorage
};

export default function DigitalTasbeeh({ initialCount = 0, targetCount, storageKey }: DigitalTasbeehProps) {
  const [count, setCount] = useState(() => {
    if (storageKey && typeof window !== 'undefined') {
      const storedCount = localStorage.getItem(storageKey);
      return storedCount ? parseInt(storedCount, 10) : initialCount;
    }
    return initialCount;
  });

  useEffect(() => {
    if (storageKey && typeof window !== 'undefined') {
      localStorage.setItem(storageKey, String(count));
    }
  }, [count, storageKey]);


  const increment = () => setCount(prev => prev + 1);
  const reset = () => setCount(0);

  return (
    <div className="flex flex-col items-center justify-center space-y-6 py-4">
      {targetCount && (
        <p className="text-sm text-muted-foreground mb-1">Target: {targetCount}</p>
      )}
      
      <Button
        onClick={increment}
        variant="default"
        className={cn(
          "w-40 h-40 sm:w-48 sm:h-48 rounded-full flex items-center justify-center text-center p-0",
          "bg-gradient-to-br from-primary via-primary/80 to-accent text-primary-foreground",
          "shadow-2xl hover:shadow-primary/40 focus:shadow-primary/40",
          "transition-all duration-300 ease-out",
          "hover:scale-105 focus:scale-105 active:scale-95"
        )}
        aria-label="Increment count"
      >
        <span className="text-6xl sm:text-7xl font-bold tabular-nums leading-none">
          {count}
        </span>
      </Button>
      
      <Button onClick={reset} variant="outline" size="icon" className="rounded-full hover:bg-muted/80" aria-label="Reset count">
        <RotateCcw className="h-6 w-6" />
      </Button>

      {targetCount && count >= targetCount && (
        <p className="text-sm text-green-600 dark:text-green-400 font-semibold mt-2">Target reached!</p>
      )}
    </div>
  );
}
