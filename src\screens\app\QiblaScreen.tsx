import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function QiblaScreen() {
  const [qiblaDirection, setQiblaDirection] = useState<number>(45);
  const [loading, setLoading] = useState(false);

  const updateDirection = () => {
    setLoading(true);
    setTimeout(() => {
      const direction = Math.floor(Math.random() * 360);
      setQiblaDirection(direction);
      setLoading(false);
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Qibla Direction</Text>
        <Text style={styles.headerSubtitle}>Find the direction to Kaaba</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.compassContainer}>
          <View style={[styles.compass, { transform: [{ rotate: `${qiblaDirection}deg` }] }]}>
            <Ionicons name="navigate" size={80} color="#14b8a6" />
          </View>
        </View>

        <Text style={styles.directionText}>
          Qibla Direction: {Math.round(qiblaDirection)}°
        </Text>

        <Text style={styles.instructionText}>
          Point your device in the direction of the green arrow
        </Text>

        <TouchableOpacity
          style={styles.refreshButton}
          onPress={updateDirection}
          disabled={loading}
        >
          <Ionicons name="refresh" size={20} color="white" />
          <Text style={styles.refreshButtonText}>
            {loading ? 'Updating...' : 'Update Direction'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc'
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  compassContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  compass: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  directionText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10,
  },
  instructionText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 30,
  },
  refreshButton: {
    flexDirection: 'row',
    backgroundColor: '#14b8a6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
});
