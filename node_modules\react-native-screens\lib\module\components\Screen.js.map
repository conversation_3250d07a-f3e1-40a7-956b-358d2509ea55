{"version": 3, "names": ["React", "Animated", "Platform", "TransitionProgressContext", "DelayedFreeze", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "ScreenNativeComponent", "ModalScreenNativeComponent", "NativeScreen", "AnimatedNativeScreen", "createAnimatedComponent", "AnimatedNativeModalScreen", "InnerScreen", "Component", "ref", "closing", "Value", "progress", "goingForward", "setNativeProps", "props", "setRef", "onComponentRef", "render", "enabled", "freezeOnBlur", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetent", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "stackPresentation", "AnimatedScreen", "OS", "undefined", "active", "activityState", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "console", "warn", "handleRef", "viewConfig", "validAttributes", "style", "display", "_viewConfig", "createElement", "freeze", "_extends", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "value", "View", "ScreenContext", "createContext", "Screen", "contextType", "ScreenWrapper", "context"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAQC,QAAQ,QAAQ,cAAc;AAEvD,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAGnD,SACEC,aAAa,EACbC,yBAAyB,EACzBC,cAAc,QACT,SAAS;;AAEhB;AACA,OAAOC,qBAAqB,MAAM,iCAAiC;AACnE,OAAOC,0BAA0B,MAAM,sCAAsC;AAE7E,OAAO,MAAMC,YAA8C,GACzDF,qBAA4B;AAC9B,MAAMG,oBAAoB,GAAGV,QAAQ,CAACW,uBAAuB,CAACF,YAAY,CAAC;AAC3E,MAAMG,yBAAyB,GAAGZ,QAAQ,CAACW,uBAAuB,CAChEH,0BACF,CAAC;;AAED;AACA;AAkBA,OAAO,MAAMK,WAAW,SAASd,KAAK,CAACe,SAAS,CAAc;EACpDC,GAAG,GAAyC,IAAI;EAChDC,OAAO,GAAG,IAAIhB,QAAQ,CAACiB,KAAK,CAAC,CAAC,CAAC;EAC/BC,QAAQ,GAAG,IAAIlB,QAAQ,CAACiB,KAAK,CAAC,CAAC,CAAC;EAChCE,YAAY,GAAG,IAAInB,QAAQ,CAACiB,KAAK,CAAC,CAAC,CAAC;EAE5CG,cAAcA,CAACC,KAAkB,EAAQ;IACvC,IAAI,CAACN,GAAG,EAAEK,cAAc,CAACC,KAAK,CAAC;EACjC;EAEAC,MAAM,GAAIP,GAAyC,IAAW;IAC5D,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,KAAK,CAACE,cAAc,GAAGR,GAAG,CAAC;EAClC,CAAC;EAEDS,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,OAAO,GAAGnB,cAAc,CAAC,CAAC;MAC1BoB,YAAY,GAAGtB,aAAa,CAAC,CAAC;MAC9B,GAAGuB;IACL,CAAC,GAAG,IAAI,CAACN,KAAK;;IAEd;IACA;IACA,MAAM;MACJO,mBAAmB,GAAG,OAAO;MAC7BC,0BAA0B,GAAG,KAAK;MAClCC,mBAAmB,GAAG,KAAK;MAC3BC,iBAAiB,GAAG,CAAC,GAAG;MACxBC,8BAA8B,GAAG,IAAI;MACrCC;IACF,CAAC,GAAGN,IAAI;IAER,IAAIF,OAAO,IAAIpB,yBAAyB,EAAE;MACxC;MACA,MAAM6B,cAAc,GAClBjC,QAAQ,CAACkC,EAAE,KAAK,SAAS,IACzBF,iBAAiB,KAAKG,SAAS,IAC/BH,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,GAC7CvB,oBAAoB,GACpBE,yBAAyB;MAE/B,IAAI;QACF;QACA;QACA;QACAyB,MAAM;QACNC,aAAa;QACbC,QAAQ;QACRC,aAAa;QACbC,uBAAuB;QACvBC,eAAe;QACf,GAAGrB;MACL,CAAC,GAAGM,IAAI;MAER,IAAIU,MAAM,KAAKD,SAAS,IAAIE,aAAa,KAAKF,SAAS,EAAE;QACvDO,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;QACDN,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MAEA,MAAMQ,SAAS,GAAI9B,GAAe,IAAK;QACrC,IAAIA,GAAG,EAAE+B,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAE;UAC3CjC,GAAG,CAAC+B,UAAU,CAACC,eAAe,CAACC,KAAK,GAAG;YACrC,GAAGjC,GAAG,CAAC+B,UAAU,CAACC,eAAe,CAACC,KAAK;YACvCC,OAAO,EAAE;UACX,CAAC;UACD,IAAI,CAAC3B,MAAM,CAACP,GAAG,CAAC;QAClB,CAAC,MAAM,IAAIA,GAAG,EAAEmC,WAAW,EAAEH,eAAe,EAAEC,KAAK,EAAE;UACnDjC,GAAG,CAACmC,WAAW,CAACH,eAAe,CAACC,KAAK,GAAG;YACtC,GAAGjC,GAAG,CAACmC,WAAW,CAACH,eAAe,CAACC,KAAK;YACxCC,OAAO,EAAE;UACX,CAAC;UACD,IAAI,CAAC3B,MAAM,CAACP,GAAG,CAAC;QAClB;MACF,CAAC;MAED,oBACEhB,KAAA,CAAAoD,aAAA,CAAChD,aAAa;QAACiD,MAAM,EAAE1B,YAAY,IAAIY,aAAa,KAAK;MAAE,gBACzDvC,KAAA,CAAAoD,aAAA,CAACjB,cAAc,EAAAmB,QAAA,KACThC,KAAK;QACTiB,aAAa,EAAEA,aAAc;QAC7BV,mBAAmB,EAAEA,mBAAoB;QACzCC,0BAA0B,EAAEA,0BAA2B;QACvDC,mBAAmB,EAAEA,mBAAoB;QACzCC,iBAAiB,EAAEA,iBAAkB;QACrCC,8BAA8B,EAAEA,8BAA+B;QAC/DS,uBAAuB,EAAE;UACvBa,KAAK,EAAEb,uBAAuB,EAAEa,KAAK,IAAI,CAAC,CAAC;UAC3CC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;UACvCC,GAAG,EAAEf,uBAAuB,EAAEe,GAAG,IAAI,CAAC,CAAC;UACvCC,MAAM,EAAEhB,uBAAuB,EAAEgB,MAAM,IAAI,CAAC;QAC9C;QACA;QACA;QAAA;QACA1C,GAAG,EAAE8B,SAAU;QACfa,oBAAoB,EAClB,CAAClB,aAAa,GACVJ,SAAS,GACTpC,QAAQ,CAAC2D,KAAK,CACZ,CACE;UACEC,WAAW,EAAE;YACX1C,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBF,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBG,YAAY,EAAE,IAAI,CAACA;UACrB;QACF,CAAC,CACF,EACD;UAAE0C,eAAe,EAAE;QAAK,CAC1B,CACL;QACDnB,eAAe,EACbA,eAAe,KACd,MAAM;UACL;QAAA,CACD;MACF,IACA,CAACF,aAAa;MAAK;MAClBD,QAAQ,gBAERxC,KAAA,CAAAoD,aAAA,CAACjD,yBAAyB,CAAC4D,QAAQ;QACjCC,KAAK,EAAE;UACL7C,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBF,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBG,YAAY,EAAE,IAAI,CAACA;QACrB;MAAE,GACDoB,QACiC,CAExB,CACH,CAAC;IAEpB,CAAC,MAAM;MACL;MACA,IAAI;QACFF,MAAM;QACNC,aAAa;QACbU,KAAK;QACL;QACAzB,cAAc;QACd,GAAGF;MACL,CAAC,GAAGM,IAAI;MAER,IAAIU,MAAM,KAAKD,SAAS,IAAIE,aAAa,KAAKF,SAAS,EAAE;QACvDE,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MACtC;MACA,oBACEtC,KAAA,CAAAoD,aAAA,CAACnD,QAAQ,CAACgE,IAAI,EAAAX,QAAA;QACZL,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEC,OAAO,EAAEX,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC,CAAE;QACnEvB,GAAG,EAAE,IAAI,CAACO;MAAO,GACbD,KAAK,CACV,CAAC;IAEN;EACF;AACF;;AAEA;AACA;AACA,OAAO,MAAM4C,aAAa,gBAAGlE,KAAK,CAACmE,aAAa,CAACrD,WAAW,CAAC;AAE7D,MAAMsD,MAAM,SAASpE,KAAK,CAACe,SAAS,CAAc;EAChD,OAAOsD,WAAW,GAAGH,aAAa;EAElCzC,MAAMA,CAAA,EAAG;IACP,MAAM6C,aAAa,GAAI,IAAI,CAACC,OAAO,IAAIzD,WAAiC;IACxE,oBAAOd,KAAA,CAAAoD,aAAA,CAACkB,aAAa,EAAK,IAAI,CAAChD,KAAQ,CAAC;EAC1C;AACF;AAEA,eAAe8C,MAAM"}