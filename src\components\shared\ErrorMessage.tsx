
import { AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

type ErrorMessageProps = {
  message: string;
  className?: string;
};

export default function ErrorMessage({ message, className }: ErrorMessageProps) {
  if (!message) return null;

  return (
    <div
      role="alert"
      className={cn(
        'flex items-center gap-2 rounded-md border border-destructive/50 bg-destructive/10 p-3 text-sm text-destructive',
        className
      )}
    >
      <AlertCircle className="h-5 w-5 flex-shrink-0" />
      <span>{message}</span>
    </div>
  );
}
