'use server';
/**
 * @fileOverview Generates reflection prompts based on a theme or source text.
 *
 * - generateReflectionPrompts - A function that returns reflection prompts.
 * - GenerateReflectionPromptsInput - The input type for the function.
 * - GenerateReflectionPromptsOutput - The return type for the function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'zod';

const GenerateReflectionPromptsInputSchema = z.object({
  theme: z.string().optional().describe('A general theme for reflection (e.g., Gratitude, Patience).'),
  sourceText: z.string().optional().describe('Specific text from a teaching or Dua to base reflections on.'),
}).refine(data => data.theme || data.sourceText, {
  message: "Either a theme or sourceText must be provided.",
});
export type GenerateReflectionPromptsInput = z.infer<typeof GenerateReflectionPromptsInputSchema>;

const GenerateReflectionPromptsOutputSchema = z.object({
  prompts: z.array(z.string()).length(3).describe('An array of 2-3 thought-provoking reflection questions or points.'),
});
export type GenerateReflectionPromptsOutput = z.infer<typeof GenerateReflectionPromptsOutputSchema>;

export async function generateReflectionPrompts(input: GenerateReflectionPromptsInput): Promise<GenerateReflectionPromptsOutput> {
  return generateReflectionPromptsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateReflectionPromptsPrompt',
  input: { schema: GenerateReflectionPromptsInputSchema },
  output: { schema: GenerateReflectionPromptsOutputSchema },
  prompt: `You are a spiritual guide for the Sabiriya app, specializing in creating thought-provoking reflection prompts.
Your goal is to generate exactly 3 distinct questions or reflection points that encourage deep contemplation related to Sufi teachings and spiritual growth.

{{#if sourceText}}
Base the reflection prompts on the following text:
---
{{{sourceText}}}
---
{{else if theme}}
Base the reflection prompts on the theme of: "{{{theme}}}".
{{/if}}

The prompts should be:
- Open-ended to encourage personal insight.
- Rooted in spiritual principles like those found in Sufism (e.g., self-awareness, connection with the Divine, virtues).
- Actionable or leading to introspection.
- Concise and clear.

Generate exactly 3 prompts.
`,
});

const generateReflectionPromptsFlow = ai.defineFlow(
  {
    name: 'generateReflectionPromptsFlow',
    inputSchema: GenerateReflectionPromptsInputSchema,
    outputSchema: GenerateReflectionPromptsOutputSchema,
  },
  async (input) => {
    const {output} = await prompt(input);
    if (!output || output.prompts.length !== 3) {
        throw new Error(`Failed to generate valid reflection prompts for input: ${JSON.stringify(input)}`);
    }
    return output;
  }
);
