import * as React from "react"
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TouchableOpacityProps,
  TextProps,
} from "react-native"
import { Ionicons } from '@expo/vector-icons'

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  border: '#e2e8f0',
  primary: '#14b8a6',
  foreground: '#0f172a',
  mutedForeground: '#64748b',
  accent: '#f8fafc',
  accentForeground: '#0f172a',
  popover: '#ffffff',
  popoverForeground: '#0f172a',
}

export interface SelectProps extends ViewProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  children?: React.ReactNode
  style?: ViewStyle
  disabled?: boolean
}

export interface SelectTriggerProps extends TouchableOpacityProps {
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

export interface SelectContentProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface SelectItemProps extends TouchableOpacityProps {
  value: string
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

export interface SelectLabelProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

export interface SelectSeparatorProps extends ViewProps {
  style?: ViewStyle
}

const SelectContext = React.createContext<{
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  items: Array<{ value: string; label: string }>
  setItems: (items: Array<{ value: string; label: string }>) => void
}>({
  isOpen: false,
  setIsOpen: () => {},
  items: [],
  setItems: () => {},
})

const Select = React.forwardRef<View, SelectProps>(
  ({ value, onValueChange, placeholder, children, style, disabled = false, ...props }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    const [items, setItems] = React.useState<Array<{ value: string; label: string }>>([])

    return (
      <SelectContext.Provider value={{
        value,
        onValueChange,
        placeholder,
        isOpen,
        setIsOpen,
        items,
        setItems
      }}>
        <View ref={ref} style={[style]} {...props}>
          {children}
        </View>
      </SelectContext.Provider>
    )
  }
)

const SelectTrigger = React.forwardRef<TouchableOpacity, SelectTriggerProps>(
  ({ children, style, textStyle, ...props }, ref) => {
    const { value, placeholder, isOpen, setIsOpen, items } = React.useContext(SelectContext)

    const selectedItem = items.find(item => item.value === value)
    const displayText = selectedItem?.label || placeholder || 'Select...'

    return (
      <TouchableOpacity
        ref={ref}
        style={[styles.selectTrigger, style]}
        onPress={() => setIsOpen(!isOpen)}
        activeOpacity={0.7}
        {...props}
      >
        <Text style={[styles.selectTriggerText, textStyle]} numberOfLines={1}>
          {displayText}
        </Text>
        <Ionicons
          name={isOpen ? "chevron-up" : "chevron-down"}
          size={16}
          color={colors.mutedForeground}
        />
      </TouchableOpacity>
    )
  }
)

const SelectContent = React.forwardRef<View, SelectContentProps>(
  ({ children, style, ...props }, ref) => {
    const { isOpen, setIsOpen } = React.useContext(SelectContext)

    return (
      <Modal
        visible={isOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setIsOpen(false)}
        >
          <View style={[styles.selectContent, style]} {...props}>
            {children}
          </View>
        </TouchableOpacity>
      </Modal>
    )
  }
)

const SelectItem = React.forwardRef<TouchableOpacity, SelectItemProps>(
  ({ value, children, style, textStyle, ...props }, ref) => {
    const { value: selectedValue, onValueChange, setIsOpen, setItems } = React.useContext(SelectContext)
    const isSelected = selectedValue === value

    React.useEffect(() => {
      setItems(prev => {
        const exists = prev.find(item => item.value === value)
        if (!exists) {
          return [...prev, { value, label: children?.toString() || value }]
        }
        return prev
      })
    }, [value, children, setItems])

    const handlePress = () => {
      onValueChange?.(value)
      setIsOpen(false)
    }

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.selectItem,
          isSelected && styles.selectItemSelected,
          style,
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
        {...props}
      >
        {isSelected && (
          <Ionicons
            name="checkmark"
            size={16}
            color={colors.primary}
            style={styles.selectItemIcon}
          />
        )}
        <Text style={[
          styles.selectItemText,
          isSelected && styles.selectItemTextSelected,
          textStyle,
        ]}>
          {children}
        </Text>
      </TouchableOpacity>
    )
  }
)

const SelectLabel = React.forwardRef<Text, SelectLabelProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.selectLabel, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

const SelectSeparator = React.forwardRef<View, SelectSeparatorProps>(
  ({ style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.selectSeparator, style]}
      {...props}
    />
  )
)

// Simplified components for compatibility
const SelectGroup = View
const SelectValue = ({ children }: { children?: React.ReactNode }) => <>{children}</>

Select.displayName = "Select"
SelectTrigger.displayName = "SelectTrigger"
SelectContent.displayName = "SelectContent"
SelectItem.displayName = "SelectItem"
SelectLabel.displayName = "SelectLabel"
SelectSeparator.displayName = "SelectSeparator"

const styles = StyleSheet.create({
  selectTrigger: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 40,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 6,
    backgroundColor: colors.background,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  selectTriggerText: {
    flex: 1,
    fontSize: 14,
    color: colors.foreground,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectContent: {
    backgroundColor: colors.popover,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    maxHeight: 300,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    padding: 4,
  },
  selectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  selectItemSelected: {
    backgroundColor: colors.accent,
  },
  selectItemIcon: {
    marginRight: 8,
  },
  selectItemText: {
    fontSize: 14,
    color: colors.popoverForeground,
  },
  selectItemTextSelected: {
    color: colors.accentForeground,
  },
  selectLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.foreground,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  selectSeparator: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 4,
    marginHorizontal: -4,
  },
})

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
}
