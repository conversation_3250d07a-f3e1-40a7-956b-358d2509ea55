
"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Languages } from "lucide-react";
import { useLanguage, type Language } from "@/context/LanguageContext";
import { cn } from "@/lib/utils";

export function LanguageSelector({ buttonClassName }: { buttonClassName?: string }) {
  const { language, setLanguage, availableLanguages } = useLanguage();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Render a placeholder or null to avoid SSR hydration mismatch issues
    // and ensure the button width is consistent before hydration.
    return <div className={cn("h-10 w-10", buttonClassName?.includes("w-full") && "w-full")} />;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          aria-label="Select language"
          className={cn("hover:shadow-[0_0_15px_hsl(var(--primary))]", buttonClassName)}
        >
          <Languages className="h-5 w-5" />
          {buttonClassName?.includes("w-full") && <span className="ml-2">Select Language</span>}
          <span className="sr-only">Select Language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Select Language</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup
          value={language}
          onValueChange={(value) => setLanguage(value as Language)}
        >
          {availableLanguages.map((lang) => (
            <DropdownMenuRadioItem 
              key={lang.code} 
              value={lang.code}
              disabled={lang.disabled}
            >
              {lang.name} {lang.disabled ? "(Soon)" : ""}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
