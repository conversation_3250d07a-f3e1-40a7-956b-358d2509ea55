
import * as admin from 'firebase-admin';

// Ensure this path points to your service account key JSO<PERSON> file
// It's recommended to use environment variables for this path in production.
// For example: process.env.GOOGLE_APPLICATION_CREDENTIALS
// Ensure the file is NOT committed to your repository.
const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;

if (!admin.apps.length) {
  if (serviceAccountPath) {
    try {
      const serviceAccount = require(serviceAccountPath); // Dynamically require based on path
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        // databaseURL: `https://<YOUR_PROJECT_ID>.firebaseio.com` // If using Realtime Database
      });
      console.log('Firebase Admin SDK initialized with service account.');
    } catch (error) {
      console.error('Error initializing Firebase Admin SDK with service account:', error);
      // Fallback to default initialization if service account loading fails
      // This might work in Google Cloud environments (e.g., Cloud Functions, Cloud Run)
      // where Application Default Credentials are automatically available.
      admin.initializeApp();
      console.log('Firebase Admin SDK initialized with Application Default Credentials (fallback).');
    }
  } else {
    // Initialize without explicit credentials (relies on GOOGLE_APPLICATION_CREDENTIALS env var
    // or Application Default Credentials in Google Cloud environments)
    admin.initializeApp();
    console.log('Firebase Admin SDK initialized (likely via GOOGLE_APPLICATION_CREDENTIALS or ADC).');
  }
}

export default admin;
