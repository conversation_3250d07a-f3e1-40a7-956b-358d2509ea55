
"use client";

import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import * as React from 'react';
import { useLanguage, type Language } from '@/context/LanguageContext'; // Import useLanguage
import { LanguageSelector } from '@/components/shared/LanguageSelector'; // Import LanguageSelector
import { cn } from '@/lib/utils';

// Arabic Shajra Text
const shajraTextArrayAr = [
  "اللَّهُمَّ لَكَ الْحَمْدُ كَمَا يَنْبَغِي لِجَلَالِ وَجْهِكَ وَعَظِيمِ سُلْطَانِكَ، وَصَلِّ وَسَلِّمْ وَبَارِكْ عَلَىٰ سَيِّدِنَا وَمَوْلَانَا مُحَمَّدٍ الْمُصْطَفَىٰ، نُورِ الْأَنْوَارِ، وَسَيِّدِ الْأَخْيَارِ، خَاتَمِ الرُّسُلِ الْأَطْهَارِ، شَفِيعِ الْمُذْنِبِينَ الْأَبْرَارِ، رَحْمَةِ الْعَالَمِينَ الْأَغْيَارِ، سَيِّدِ الْكَوْنَيْنِ وَفَخْرِ الْمَوْجُودَاتِ، أَحْمَدَ الْمُجْتَبَىٰ وَمُحَمَّدٍ الْمُخْتَارِ، وَعَلَىٰ آلِهِ الطَّيِّبِينَ وَأَصْحَابِهِ الْمُنْتَجَبِينَ.",
  "وَبِجَاهِهِ الْعَظِيمِ، ارْزُقْنَا صَادِقَ مَحَبَّتِهِ وَوَافِرَ شَفَاعَتِهِ يَوْمَ الْجَزَاءِ، وَاجْعَلْنَا مِنْ أَهْلِ قُرْبِهِ وَرِضَاهُ.",
  "اللَّهُمَّ صَلِّ وَسَلِّمْ عَلَى سَيِّدِنَا مُحَمَّدٍ",
  "وَعَلَى سَيِّدِنَا عَلِيٍّ المُرْتَضَى",
  "أَسَدِ اللهِ الغَالِبِ وَبَابِ مَدِينَةِ العِلْمِ",
  "وَارْزُقْنَا وَلَاءَهُ الْخَالِصَ، وَمَحَبَّتَهُ الصَّادِقَةَ، وَاجْعَلْنَا مَعَهُ فِي دَرَجَاتِ الْمُقَرَّبِينَ",
  "اللَّهُمَّ وَبِجَاهِ سَيِّدِنَا مُحَمَّدٍ وَآلِهِ، أَفِضْ سَحَائِبَ رَحْمَتِكَ وَبَرَكَاتِكَ عَلَىٰ أَرْوَاحِ سَادَاتِنَا الْكِرَامِ، وَمَشَايِخِنَا الْعِظَامِ، فِي هَذِهِ السِّلْسِلَةِ النُّورَانِيَّةِ، أَرْبَابِ الْهِدَايَةِ وَالْفُتُوَّةِ",
  "الْخَوَاجَةِ الْحَسَنِ الْبَصْرِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ عَبْدِ الْوَاحِدِ بْنِ زَيْدٍ، رضي الله عنه.",
  "الْخَوَاجَةِ فُضَيْلِ بْنِ عِيَاضٍ، رضي الله عنه.",
  "الْخَوَاجَةِ إِبْرَاهِيمَ بْنِ أَدْهَمَ، رضي الله عنه.",
  "الْخَوَاجَةِ حُذَيْفَةَ الْمَرْعَشِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ أَمِينِ الدِّينِ أَبِي هُبَيْرَةَ الْبَصْرِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ مَمْشَادٍ عَلِيٍّ الدِّينَوَرِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ أَبِي إِسْحَاقَ الشَّامِيِّ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ أَبِي أَحْمَدَ الْأَبْدَالِ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ أَبِي مُحَمَّدٍ نَاصِرِ الدِّينِ مُحْتَرَمٍ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ أَبِي يُوسُفَ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ مَوْدُودِ الْحَقِّ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ حَاجِي شَرِيفٍ زَنْدَنِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ عُثْمَانَ هَارُونِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ غَرِيبِ نَوَازٍ مُعِينِ الدِّينِ الْچِشْتِيِّ الْأَجْمِيرِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ قُطْبِ الدِّينِ بَخْتِيَارٍ كَاكِيِّ الْچِشْتِيِّ، رضي الله عنه.",
  "الْخَوَاجَةِ فَرِيدِ الدِّينِ گَنْجِ شَكَرٍ، رضي الله عنه.",
  "السَّيِّدِ مَخْدُومِ عَلَاءِ الدِّينِ عَلِيٍّ أَحْمَدَ الصَّابِرِ، رضي الله عنه.",
  "الشَّيْخِ شَمْسِ الدِّينِ التُّرْكِ، رضي الله عنه.",
  "الشَّيْخِ جَلَالِ الدِّينِ، رضي الله عنه.",
  "الشَّيْخِ عَبْدِ الْحَقِّ الرَّدْوَلَوِيِّ، رضي الله عنه.",
  "الشَّيْخِ مُحَمَّدِ بْنِ عَارِفٍ، رضي الله عنه.",
  "الشَّيْخِ عَبْدِ الْقُدُّوسِ الْگَنْگُوهِيِّ، رضي الله عنه.",
  "الشَّيْخِ جَلَالِ الدِّينِ، رضي الله عنه.",
  "الشَّيْخِ نِظَامِ الدِّينِ الْبَلْخِيِّ، رضي الله عنه.",
  "الشَّيْخِ أَبِي سَعِيدٍ، رضي الله عنه.",
  "الشَّيْخِ شَاهِ مُحَمَّدٍ صَادِقٍ، رضي الله عنه.",
  "الشَّاهِ شَيْخِ مُحَمَّدٍ دَاوُدَ جِي، رضي الله عنه.",
  "السَّيِّدِ شَاهِ أَبِي الْمَعَالِي الْأَنْبَيْثَوِيِّ، رضي الله عنه.",
  "مِيرَانْ جِي السَّيِّدِ شَاهِ بِهِيكَ، رضي الله عنه.",
  "السَّيِّدِ شَاهِ سَالِمٍ التِّرْمِذِيِّ الرُّوپَرْوِيِّ، رضي الله عنه.",
  "السَّيِّدِ شَاهِ أَعْظَمَ الرُّوپَرْوِيِّ، رضي الله عنه.",
  "الْپِيرِ حَافِظِ مُحَمَّدِ مُوسَىٰ مَانَكْ پُورِيِّ، رضي الله عنه.",
  "السَّيِّدِ مُعِينِ الدِّينِ شَاهِ خَامُوشٍ، رضي الله عنه.",
  "السَّيِّدِ مَظْهَرِ عَلِيٍّ شَاهٍ، رضي الله عنه.",
  "السَّيِّدِ مَنْظُورِ عَلِيٍّ شَاهٍ، رضي الله عنه.",
  "الْحَاجِي عَبْدِ الصَّمَدِ شَاهٍ صَاحِبٍ، رضي الله عنه.",
  "الْعَلَّامَةِ الْحَاجِي عَبْدِ الْغَفَّارِ صَاحِبٍ، رضي الله عنه.",
  "السَّيِّدِ شَيْخِ مِيرِ مَنْصُورِ أَحْمَدَ الْجِيلَانِيِّ الدِّهْلَوِيِّ، مَدَّ ظِلُّهُ النُّورَانِيُّ.",
  "فَقِيرِ الصَّابِرِيَّةِ، حَضْرَةِ مُحَمَّدِ مَسْعُودِ الْحَسَنِ الْحُسَيْنِيِّ الْچِشْتِيِّ الْقَادِرِيِّ، مَدَّ ظِلُّهُ النُّورَانِيُّ.",
  "اللَّهُمَّ بِحَقِّهِمْ عَلَيْكَ، وَبِمَا لَهُمْ مِنْ مَكَانَةٍ رَفِيعَةٍ لَدَيْكَ، ارْزُقْنَا بِهِمْ صِدْقَ الْمَحَبَّةِ، وَقُوَّةَ الْيَقِينِ، وَاسْتِقَامَةَ الْقَلْبِ، وَالتَّوْفِيقَ لِمَا تُحِبُّ وَتَرْضَىٰ، وَاجْعَلْنَا مِنْ أَتْبَاعِهِمْ بِإِحْسَانٍ، وَاحْشُرْنَا فِي زُمْرَتِهِمْ يَوْمَ الْقِيَامَةِ، بِرَحْمَتِكَ يَا أَكْرَمَ الْأَكْرَمِينَ.",
  "وَصَلَّى اللهُ عَلَىٰ سَيِّدِنَا مُحَمَّدٍ وَعَلَىٰ آلِهِ وَصَحْبِهِ أَجْمَعِينَ، وَالْحَمْدُ لِلهِ رَبِّ الْعَالَمِينَ."
];
const arabicNamesStartIndex = 7;
const arabicNamesEndIndex = 46;

// Urdu Shajra Text
const shajraTextArrayUr = [
  "الہٰی! بحرمت راز و نیاز سید المرسلین، خاتم النبیین، شفیع المذنبین، رحمۃ اللعالمین، سرور کائنات، فخر موجودات، احمد مجتبیٰ، محمد مصطفیٰ صلی اللہ علیہ وسلم۔",
  "الہٰی! بحرمت راز و نیاز سید الغربا، امام الاولیا، اسد الله الغالب، مطلوب کل طالب، حضرت علی ابن ابی طالب کرم اللہ وجہہ۔",
  "الہٰی! بحرمت راز و نیاز امام الاولیا، شیخ المشائخ، حضرت خواجہ حسن بصری رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز تاج الاولیا، قمر المشائخ، حضرت خواجہ شیخ عبدالواحد بن زید رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز فخر المشائخ، جمال اولیا، حضرت خواجہ فضیل بن عیاض رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز شوکت الاولیا، حشمت المشائخ، حضرت خواجہ سلطان ابراہیم بن ادہم رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز زبدۃ الاولیا، شیخ المشائخ، حضرت خواجہ سید بدرالدین حذیفہ المرعشی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز امین الاولیا، شمس العارفین، حضرت خواجہ امین الدین ببیرہ البصری رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز نیر الاولیا، قمر العارفین، حضرت خواجہ ممشاد علی دینوری رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز تاجدار ولایت، امام العارفین، حضرت خواجہ ابو اسحاق شامی چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز ابدال دوراں، قطب العارفین، حضرت خواجہ قدوۃ الدین ابو احمد ابدال چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز برہان الاولیا، واقفِ اسرار جلی و خفی، حضرت خواجہ ابو محمد ناصر الدین محترم چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز ناصر الاولیا، نصرۃ العارفین، حضرت خواجہ ابو یوسف چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز قلب ربانی، دلیل العارفین، حضرت خواجہ مودود حق چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز اشرف الاولیا، کبیر العارفین، حضرت خواجہ حاجی شریف زندنی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز تاجدار الاولیا، امداد العارفین، حضرت خواجہ عثمان ہارونی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز معشوق ربانی، معین الاولیا، قبلۃ العارفین، فخر الاولین والآخرین، مخدوم عالم، غوث زمن، سلطان الہند، حضرت خواجۂ خواجگان خواجہ غریب نواز معین الدین چشتی اجمیری رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز قطب العارفین، غوث السالکین، شہید محبت الٰہی، مقتدائے اولیا بہشتی، حضرت خواجہ قطب الدین بختیار کاکی چشتی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز سلطان الزاہدین، شیخ الاسلام، حضرت خواجہ فرید الدین گنج شکر رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید الفقرا، مخدوم دو جہاں، علاؤالدین علی احمد صابر رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت شمس الدین ترک رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت جلال الدین کبیر الاولیا رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت شیخ عبدالحق ردولوی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت شیخ محمد بن عارف رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت قطب عالم شیخ عبدالقدوس گنگوہی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت جلال الدین رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت نظام الدین بلخی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت ابو سعید رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت شیخ شاہ محمد صادق رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت شاہ شیخ محمد داؤد جی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید شاہ ابوالمعالی امبیٹھوی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت میران جی سید شاہ بھیک رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید شاہ سالم ترمذی روپڑوی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید شاہ اعظم روپڑوی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت پیر حافظ محمد موسیٰ مانک پوری رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید معین الدین شاہ خاموش رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت سید مظہر علی شاہ رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت پیر سید منظور علی شاہ رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت حاجی عبدالصمد شاہ صاحب رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز حضرت علامہ حاجی عبدالغفار صاحب صمدی رضی اللہ عنہ۔",
  "الہٰی! بحرمت راز و نیاز شیخ المشائخ حضور پیر سید شیخ میر منصور احمد گیلانی ثمہ دہلوی مدظلہ النورانی۔",
  "الہٰی! بحرمت راز و نیاز فقیر صابری حضرت مخدوم محمد مسعود الحسن حسینی چشتی قادری مدظلہ النورانی۔"
];

// Hindi Shajra Text
const shajraTextArrayHi = [
  "अल्लाहुम्म ल-क अल-हम्दु कमा यम्बग़ी लि-जलालि वज्हि-क व अज़ीमि सुल्तानि-क,",
  "व सल्लि व सल्लिम व बारिक अला सय्यिदिना व मौलाना मुहम्मदिनिल मुस्तफ़ा, नूरिल अनवारि, व सय्यिदिल अख़्यारि, ख़ातिमिर रुसुलिल अत्हारि, शफ़ीइल मुज़्निबीनल अबरारि, रहमतिल आ-लमीनल अग़्यारि, सय्यिदिल कौनैनि व फ़ख़रिल मौजूदाति, अहमदल मुज्तबा व मुहम्मदिनिल मुख़्तारि,",
  "व अला आलिहित तय्यिबी-न व असहाबिहिल मुन्त-जबीन.",
  "व बि-जाहिहिल अज़ीमि, र्ज़ुक्ना सादिक़-म-हब्बतिही व वाफ़ि-र शफ़ाअतिही यौमल जज़ाइ, वज्अल्ना मिन अहलि क़ुर्बिही व रिदाहु.",
  "अल्लाहुम्म सल्लि व सल्लिम अला सय्यिदिना मुहम्मदिन व अला सय्यिदिना अलिय्यिनिल मुर्तदा",
  "असदिल्लाहिल ग़ालिब व बाब-ए-मदीनतिल इल्मि",
  "वर्ज़ुक्ना वला-अहुल ख़ालिस, व महब्बतहुस सादिक़त, वज्अल्ना म-अहू फ़ी दरजातिल मुक़र्रबीन",
  "अल्लाहुम्म व बि-जाहि सय्यिदिना मुहम्मदिन व आलिही, अफ़िज़ सहाइ-ब रहमति-क व बरकाति-क अला अरवाहि सादातिनल किराम, व मशाइख़िनल इज़ाम, फ़ी हाज़िहिस-सिलसिलातिन नूरानिय्यति, अरबाबिल हिदायति वल फ़ुतूव्वति",
  "अल-ख़्वाजा अल-हसन अल-बसरी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अब्द अल-वाहिद इब्न ज़ैद, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा फ़ुदैल इब्न इयाद, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा इब्राहीम इब्न अदहम, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा हुज़ैफ़ह अल-मरअशी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अमीन अद-दीन अबी हुबैरह अल-बसरी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा ममशाद अलिय्यिन दीनवरी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अबी इस्हाक़ अश-शामी अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अबी अहमद अल-अब्दाल अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अबी मुहम्मद नासिर अद-दीन मुहतरम अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा अबी यूसुफ़ अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा मौदूद अल-हक़्क़ अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा हाजी शरीफ़ ज़न्दनी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा उस्मान हारूनी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा ग़रीब नवाज़ मुईनुद्दीन अल-चिश्ती अल-अजमेरी, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा क़ुत्बुद्दीन बख़्तियार काकी अल-चिश्ती, रदियल्लाहु अन्हु।",
  "अल-ख़्वाजा फ़रीदुद्दीन गंज-ए-शकर, रदियल्लाहु अन्हु।",
  "अस-सय्यिद मख़दूम अलाउद्दीन अलिय्यिन अहमद अस-साबिर, रदियल्लाहु अन्हु।",
  "अश-शैख़ शम्सुद्दीन अत-तुर्क, रदियल्लाहु अन्हु।",
  "अश-शैख़ जलालुद्दीन, रदियल्लाहु अन्हु।",
  "अश-शैख़ अब्द अल-हक़्क़ अर-रदौलवी, रदियल्लाहु अन्हु।",
  "अश-शैख़ मुहम्मद इब्न आरिफ़, रदियल्लाहु अन्हु।",
  "अश-शैख़ अब्द अल-क़ुद्दूस अल-गंगोही, रदियल्लाहु अन्हु।",
  "अश-शैख़ जलालुद्दीन, रदियल्लाहु अन्हु।",
  "अश-शैख़ निज़ामुद्दीन अल-बल्ख़ी, रदियल्लाहु अन्हु।",
  "अश-शैख़ अबी सईद, रदियल्लाहु अन्हु।",
  "अश-शैख़ शाह मुहम्मद सादिक़, रदियल्लाहु अन्हु।",
  "अश-शाह शैख़ मुहम्मद दाऊद जी, रदियल्लाहु अन्हु।",
  "अस-सय्यिद शाह अबिल मआली अल-अम्बैथवी, रदियल्लाहु अन्हु।",
  "मीरान जी अस-सय्यिद शाह भीक, रदियल्लाहु अन्हु।",
  "अस-सय्यिद शाह सालिम अत-तिरमिज़ी अर-रूपरवी, रदियल्लाहु अन्हु।",
  "अस-सय्यिद शाह आज़म अर-रूपरवी, रदियल्लाहु अन्हु।",
  "अल-पीर हाफ़िज़ मुहम्मद मूसा मानकपुरी, रदियल्लाहु अन्हु।",
  "अस-सय्यिद मुईनुद्दीन शाह ख़ामोश, रदियल्लाहु अन्हु।",
  "अस-सय्यिद मज़हर अली शाह, रदियल्लाहु अन्हु।",
  "अस-सय्यिद मंज़ूर अली शाह, रदियल्लाहु अन्हु।",
  "अल-हाजी अब्द अस-समद शाह साहिब, रदियल्लाहु अन्हु।",
  "अल-अल्लामह अल-हाजी अब्द अल-ग़फ़्फ़ार साहिब, रदियल्लाहु अन्हु।",
  "अस-सय्यिद शैख़ मीर मंसूर अहमद अल-जीलानी अद-देहलवी, मद्द ज़िल्लुहु अन-नूरानिय्यु।",
  "फ़क़ीर अस-साबिरिय्यह, हज़रत मुहम्मद मसऊद अल-हसन अल-हुसैनी अल-चिश्ती अल-क़ादिरी, मद्द ज़िल्लुहु अन-नूरानिय्यु।",
  "अल्लाहुम्म बि-हक़्क़िहिम अलै-क, व बिमा लहुम मिन मका-नतिन रफ़ीअतिन लदै-क, र्ज़ुक्ना बिहिम सिद्क़ल महब्बति, व क़ुव्वतल यक़ीनि, वस्तिक़ामतल क़ल्बि, वत-तौफ़ीक़-लिमा तुहिब्बु व तरदा, वज्अल्ना मिन अतबाइहिम बि-इहसानिन, वहशुर्ना फ़ी ज़ुमरतिहिम यौमल क़ियामति, बि-रहमति-क या अकरमल अकरमीन.",
  "व सल्लल्लाहु अला सय्यिदिना मुहम्मदिन व अला आलिही व सह्बिही अजमईन, वलहम्दुलिल्लाहि रब्बिल आलमीन."
];
const hindiNamesStartIndex = 8;
const hindiNamesEndIndex = 47;


// Roman Urdu Shajra Text
const shajraTextArrayRo = [
  "Allahumma lakal-hamdu kama yanbaghi li-jalaali wajhika wa 'azeemi sultaanika,",
  "Wa salli wa sallim wa baarik 'alaa Sayyidina wa Mawlana Muhammadinil-Mustafa, Nooril-Anwaar, wa Sayyidil-Akhyaar, Khaatimir-Rusulil-Athaar, Shafee'il-Muznibeelal-Abraar, Rahmatil-'Aalameenal-Aghyaar, Sayyidil-Kawnaini wa Fakhril-Mawjoodaat, Ahmadal-Mujtaba wa Muhammadinil-Mukhtaar,",
  "Wa 'alaa Aalihit-Tayyibeena wa As'haabihil-Muntajabeen.",
  "Wa bi-jaahihil-'azeem, arzuqna saadiqa mahabbatihi wa waafira shafaa'atihi Yawmal-Jazaa', waj'alna min ahli qurbihi wa ridaahu.",
  "Allahumma salli wa sallim 'alaa Sayyidina Muhammadin wa 'alaa Sayyidina 'Aliyyinil-Murtada",
  "Asadillahil-Ghaalib wa Baabi Madeenatil-'Ilm",
  "Warzuqna walaa'ahul-khaalisa, wa mahabbatahus-saadiqata, waj'alna ma'ahu fi darajaatil-muqarrabeen.",
  "Allahumma wa bi-jaahi Sayyidina Muhammadin wa Aalihi, afid sahaa'iba rahmatika wa barakaatika 'alaa arwaahi saadaatinal-kiraam, wa mashaayikhinal-'izaam, fi haazihis-Silsilatin-Nooraaniyyati, Arbaabil-Hidaayati wal-Futuwwati.",
  "Al-Khwaja Al-Hasan Al-Basri, Radiallahu anhu.",
  "Al-Khwaja 'Abd Al-Waahid ibn Zaid, Radiallahu anhu.",
  "Al-Khwaja Fudayl ibn 'Iyaad, Radiallahu anhu.",
  "Al-Khwaja Ibraahim ibn Adham, Radiallahu anhu.",
  "Al-Khwaja Huzayfah Al-Mar'ashi, Radiallahu anhu.",
  "Al-Khwaja Ameenid-Deen Abi Hubayrah Al-Basri, Radiallahu anhu.",
  "Al-Khwaja Mamshaad 'Aliyyin Ad-Deenawari, Radiallahu anhu.",
  "Al-Khwaja Abi Ishaaq Ash-Shaami Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Abi Ahmad Al-Abdaal Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Abi Muhammad Naasirid-Deen Muhtaram Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Abi Yusuf Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Mawdoodil-Haqq Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Haaji Shareef Zandani, Radiallahu anhu.",
  "Al-Khwaja 'Uthmaan Haarooni, Radiallahu anhu.",
  "Al-Khwaja Ghareeb Nawaaz Mu'eenid-Deen Al-Chishti Al-Ajmeri, Radiallahu anhu.",
  "Al-Khwaja Qutbid-Deen Bakhtiyaar Kaaki Al-Chishti, Radiallahu anhu.",
  "Al-Khwaja Fareedid-Deen Ganj-e-Shakar, Radiallahu anhu.",
  "As-Sayyid Makhdoom 'Alaa'id-Deen 'Aliyyin Ahmad As-Saabir, Radiallahu anhu.",
  "Ash-Shaikh Shamsid-Deen At-Turk, Radiallahu anhu.",
  "Ash-Shaikh Jalaalid-Deen, Radiallahu anhu.",
  "Ash-Shaikh 'Abdul-Haqq Ar-Radaulawi, Radiallahu anhu.",
  "Ash-Shaikh Muhammad ibn 'Aarif, Radiallahu anhu.",
  "Ash-Shaikh 'Abdul-Quddoos Al-Gangohi, Radiallahu anhu.",
  "Ash-Shaikh Jalaalid-Deen, Radiallahu anhu.",
  "Ash-Shaikh Nizaamid-Deen Al-Balkhi, Radiallahu anhu.",
  "Ash-Shaikh Abi Sa'eed, Radiallahu anhu.",
  "Ash-Shaikh Shah Muhammad Saadiq, Radiallahu anhu.",
  "Ash-Shah Shaikh Muhammad Daa'ood Ji, Radiallahu anhu.",
  "As-Sayyid Shah Abil-Ma'aali Al-Anbaithawi, Radiallahu anhu.",
  "Meeran Ji As-Sayyid Shah Bheek, Radiallahu anhu.",
  "As-Sayyid Shah Saalim At-Tirmizi Ar-Roparwi, Radiallahu anhu.",
  "As-Sayyid Shah A'zam Ar-Roparwi, Radiallahu anhu.",
  "Al-Peer Haafiz Muhammad Moosa Maanakpuri, Radiallahu anhu.",
  "As-Sayyid Mu'eenid-Deen Shah Khaamosh, Radiallahu anhu.",
  "As-Sayyid Mazhar 'Ali Shah, Radiallahu anhu.",
  "As-Sayyid Manzoor 'Ali Shah, Radiallahu anhu.",
  "Al-Haaji 'Abdus-Samad Shah Sahib, Radiallahu anhu.",
  "Al-'Allaamah Al-Haaji 'Abdul-Ghaffaar Sahib, Radiallahu anhu.",
  "As-Sayyid Shaikh Meer Mansoor Ahmad Al-Jeelaani Ad-Dehlawi, Madda Zilluhun-Nooraaniyyu.",
  "Faqeeris-Saabiriyyah, Hadrat Muhammad Mas'ood Al-Hasan Al-Husaini Al-Chishti Al-Qaadiri, Madda Zilluhun-Nooraaniyyu.",
  "Allahumma bi-haqqihim 'alayka, wa bima lahum min makaanatin rafee'atin ladayka, arzuqna bihim sidqal-mahabbati, wa quwwatal-yaqeeni, wastiqaamatal-qalbi, wat-tawfeeqa lima tuhibbu wa tarda, waj'alna min atbaa'ihim bi-ihsaanin, wahshurna fi zumratihim Yawmal-Qiyaamati, bi-rahmatika Ya Akramal-Akrameen.",
  "Wa Sallallahu 'alaa Sayyidina Muhammadin wa 'alaa Aalihi wa Sahbihi Ajma'een, Walhamdulillahi Rabbil-'aalameen."
];
const roNamesStartIndex = 8;
const roNamesEndIndex = 47;

// English Shajra Text
const shajraTextArrayEn = [
  "O Allah, to You is all praise as befits the Majesty of Your Countenance and the Greatness of Your Dominion.",
  "And send prayers, peace, and blessings upon our Master and our Liege Lord Muhammad al-Mustafa, the Light of Lights, the Master of the Chosen, the Seal of the Pure Messengers, the Intercessor for the virtuous sinners, the Mercy to all the worlds, the Master of the Two Universes and the Pride of all creation, Ahmad al-Mujtaba and Muhammad al-Mukhtar.",
  "And upon his pure family and his elect companions.",
  "And by his immense station, grant us true love for him and abundant intercession on the Day of Recompense, and make us among the people of his closeness and his pleasure.",
  "O Allah, send prayers and peace upon our Master Muhammad and upon our Master Ali al-Murtada,",
  "The Triumphant Lion of Allah and the Gate to the City of Knowledge.",
  "And grant us pure allegiance to him, and sincere love for him, and place us with him in the ranks of those brought near.",
  "O Allah, and by the station of our Master Muhammad and his family, pour forth the clouds of Your mercy and Your blessings upon the souls of our noble masters and our great Sheikhs in this luminous chain, the possessors of guidance and spiritual chivalry:",
  "Khwaja Hasan al-Basri, May Allah be pleased with him.",
  "Khwaja 'Abd al-Wahid ibn Zayd, May Allah be pleased with him.",
  "Khwaja Fudayl ibn 'Iyad, May Allah be pleased with him.",
  "Khwaja Ibrahim ibn Adham, May Allah be pleased with him.",
  "Khwaja Hudhayfah al-Mar'ashi, May Allah be pleased with him.",
  "Khwaja Amin ad-Din Abi Hubayrah al-Basri, May Allah be pleased with him.",
  "Khwaja Mamshad 'Aliyy ad-Dinawari, May Allah be pleased with him.",
  "Khwaja Abi Ishaq ash-Shami al-Chishti, May Allah be pleased with him.",
  "Khwaja Abi Ahmad al-Abdal al-Chishti, May Allah be pleased with him.",
  "Khwaja Abi Muhammad Nasir ad-Din Muhtaram al-Chishti, May Allah be pleased with him.",
  "Khwaja Abi Yusuf al-Chishti, May Allah be pleased with him.",
  "Khwaja Mawdud al-Haqq al-Chishti, May Allah be pleased with him.",
  "Khwaja Haji Sharif Zandani, May Allah be pleased with him.",
  "Khwaja 'Uthman Haruni, May Allah be pleased with him.",
  "Khwaja Gharib Nawaz Mu'in ad-Din al-Chishti al-Ajmeri, May Allah be pleased with him.",
  "Khwaja Qutb ad-Din Bakhtiyar Kaki al-Chishti, May Allah be pleased with him.",
  "Khwaja Farid ad-Din Ganj-e-Shakar, May Allah be pleased with him.",
  "Sayyid Makhdum 'Ala ad-Din 'Ali Ahmad as-Sabir, May Allah be pleased with him.",
  "Sheikh Shams ad-Din at-Turk, May Allah be pleased with him.",
  "Sheikh Jalal ad-Din, May Allah be pleased with him.",
  "Sheikh 'Abd al-Haqq ar-Radaulawi, May Allah be pleased with him.",
  "Sheikh Muhammad ibn 'Arif, May Allah be pleased with him.",
  "Sheikh 'Abd al-Quddus al-Gangohi, May Allah be pleased with him.",
  "Sheikh Jalal ad-Din, May Allah be pleased with him.",
  "Sheikh Nizam ad-Din al-Balkhi, May Allah be pleased with him.",
  "Sheikh Abi Sa'id, May Allah be pleased with him.",
  "Sheikh Shah Muhammad Sadiq, May Allah be pleased with him.",
  "Shah Sheikh Muhammad Da'ud Ji, May Allah be pleased with him.",
  "Sayyid Shah Abil Ma'ali al-Anbaithawi, May Allah be pleased with him.",
  "Meeran Ji Sayyid Shah Bheek, May Allah be pleased with him.",
  "Sayyid Shah Salim at-Tirmizi ar-Roparwi, May Allah be pleased with him.",
  "Sayyid Shah A'zam ar-Roparwi, May Allah be pleased with him.",
  "Peer Hafiz Muhammad Musa Manakpuri, May Allah be pleased with him.",
  "Sayyid Mu'in ad-Din Shah Khamush, May Allah be pleased with him.",
  "Sayyid Mazhar 'Ali Shah, May Allah be pleased with him.",
  "Sayyid Manzoor 'Ali Shah, May Allah be pleased with him.",
  "Haji 'Abdus-Samad Shah Sahib, May Allah be pleased with him.",
  "Allamah Haji 'Abdul-Ghaffar Sahib, May Allah be pleased with him.",
  "Sayyid Sheikh Meer Mansoor Ahmad al-Jilani ad-Dehlawi, May Allah prolong his luminous shade.",
  "Faqir of the Sabiriyya, Hadrat Muhammad Mas'ood al-Hasan al-Husaini al-Chishti al-Qadiri, May Allah prolong his luminous shade.",
  "O Allah, by their right over You, and by the lofty status they have with You, grant us through them sincerity of love, strength of certainty, steadfastness of heart, and success in what You love and are pleased with. Make us among their followers in excellence, and gather us in their company on the Day of Resurrection, by Your mercy, O Most Generous of the generous.",
  "And may Allah send prayers upon our Master Muhammad and upon his family and all his companions. And praise be to Allah, Lord of the worlds."
];
const enNamesStartIndex = 8;
const enNamesEndIndex = 47;


const parseLine = (line: string, lang: Language) => {
  let mainName = line;
  let honorific = "";
  let prefix = "";

  if (lang === 'ur') {
    const urduPrefixStr = "الہٰی! بحرمت راز و نیاز ";
    if (line.startsWith(urduPrefixStr)) {
      prefix = urduPrefixStr;
      mainName = line.substring(urduPrefixStr.length);
    }
    const urduHonorifics = ["رضی اللہ عنہ۔", "مدظلہ النورانی۔", "صلی اللہ علیہ وسلم۔", "کرم اللہ وجہہ۔"];
    for (const h of urduHonorifics) {
      if (mainName.endsWith(h)) {
        honorific = h.replace(/۔$/, ""); // Remove trailing period for display
        mainName = mainName.substring(0, mainName.length - h.length).trim();
        break;
      }
    }
  } else if (lang === 'ar') {
     const honorificsWithComma = ["، رضي الله عنه", "، مَدَّ ظِلُّهُ النُّورَانِيُّ"];
     const plainHonorifics = ["رضي الله عنه.", "مَدَّ ظِلُّهُ النُّورَانِيُّ."]; // Order matters: check with period first
      
      for(const h of plainHonorifics) {
        if(line.endsWith(h)) {
            mainName = line.substring(0, line.length - h.length).trim();
            honorific = h.replace(/\.$/, "").trim();
            return { prefix, mainName, honorific };
        }
      }
      for(const hc of honorificsWithComma) {
          if (line.includes(hc + ".")) { 
              const parts = line.split(hc + ".");
              if (parts.length > 1) {
                  mainName = parts[0].trim();
                  honorific = hc.substring(2).trim(); // Remove "، "
                  return { prefix, mainName, honorific };
              }
          } else if (line.includes(hc)) { 
               const parts = line.split(hc);
               if (parts.length > 1) {
                  mainName = parts[0].trim();
                  honorific = hc.substring(2).trim(); // Remove "، "
                  return { prefix, mainName, honorific };
              }
          }
      }
  } else if (lang === 'hi' || lang === 'ro' || lang === 'en') {
    const patterns = [
      // Order matters: check for longer patterns (with comma) first
      { regex: /,\s*(Radiallahu anhu|रदियल्लाहु अन्हु|Madda Zilluhun-Nooraaniyyu|मद्द ज़िल्लुहु अन-नूरानिय्यु|May Allah be pleased with him|May Allah prolong his luminous shade)\.?\s*$/i, honorificIndex: 1 },
      { regex: /\s+(Radiallahu anhu|रदियल्लाहु अन्हु|Madda Zilluhun-Nooraaniyyu|मद्द ज़िल्लुहु अन-नूरानिय्यु|May Allah be pleased with him|May Allah prolong his luminous shade)\.?\s*$/i, honorificIndex: 1 }
    ];

    for (const p of patterns) {
        const match = line.match(p.regex);
        if (match) {
            honorific = match[p.honorificIndex].trim();
            // Remove trailing period from honorific if present
            if (honorific.endsWith('.')) {
                honorific = honorific.slice(0, -1);
            }
            mainName = line.substring(0, match.index).trim();
            // Remove trailing comma from mainName if present after splitting
            if (mainName.endsWith(',')) {
                mainName = mainName.slice(0, -1).trim();
            }
            break;
        }
    }
  }
  return { prefix, mainName, honorific };
};


export default function ShajraPage() {
  const { language } = useLanguage();

  const getPageTitleText = () => {
    switch (language) {
      case 'ur':
        return "شجرہ طیبہ";
      case 'hi':
        return "शजरह तय्यिबह";
      case 'ro':
      case 'en':
        return "Shajarah Tayyibah";
      case 'ar':
      default:
        return "شجرة الطيبة";
    }
  };
  
  const isRtl = language === 'ar' || language === 'ur';

  const getTextArray = () => {
    switch (language) {
      case 'ur': return shajraTextArrayUr;
      case 'hi': return shajraTextArrayHi;
      case 'ro': return shajraTextArrayRo;
      case 'en': return shajraTextArrayEn;
      case 'ar':
      default: return shajraTextArrayAr;
    }
  };

  const getNamesStartIndex = () => {
    switch (language) {
      case 'ur': return 2; // Urdu starts names from the 3rd line (index 2)
      case 'hi': return hindiNamesStartIndex;
      case 'ro': return roNamesStartIndex;
      case 'en': return enNamesStartIndex;
      case 'ar':
      default: return arabicNamesStartIndex;
    }
  };
  
  const getNamesEndIndex = () => {
     switch (language) {
      case 'ur': return shajraTextArrayUr.length - 1; // Urdu has no separate concluding prayer
      case 'hi': return hindiNamesEndIndex;
      case 'ro': return roNamesEndIndex;
      case 'en': return enNamesEndIndex;
      case 'ar':
      default: return arabicNamesEndIndex;
    }
  };

  const currentTextArray = getTextArray();
  const namesStartIdx = getNamesStartIndex();
  const namesEndIdx = getNamesEndIndex();

  const introLines = currentTextArray.slice(0, namesStartIdx);
  const nameLines = currentTextArray.slice(namesStartIdx, namesEndIdx + 1);
  const conclusionLines = currentTextArray.slice(namesEndIdx + 1);


  return (
    <div className="shajra-page-container flex flex-col h-full relative p-4 sm:p-6">
      
      <PageTitle title={getPageTitleText()} className={cn("text-center mb-6", isRtl ? "font-arabic" : "font-sans")} />
      
      <Card className="shadow-lg flex-grow overflow-hidden">
        <CardContent className="p-0 h-full">
          <ScrollArea className="h-full p-4 sm:p-6">
            <div lang={isRtl ? language : undefined} dir={isRtl ? "rtl" : "ltr"} className={cn("space-y-4 text-foreground", isRtl ? "font-arabic text-right" : "font-sans text-left")}>
                {/* Intro */}
                {introLines.map((line, index) => {
                    const isAllahumma = line.toLowerCase().startsWith("allahumma") || line.startsWith("अल्लाहुम्म") || line.startsWith("اللَّهُمَّ");
                    return (
                        <p key={`intro-${language}-${index}`} className={cn("mb-3 leading-relaxed text-lg md:text-xl", isAllahumma && "font-semibold text-primary text-xl md:text-2xl mt-4")}>{line}</p>
                    );
                })}

                {nameLines.length > 0 && <Separator className="my-6 bg-border/70" />}

                {/* Names List */}
                <div className="space-y-3">
                  {nameLines.map((line, index) => {
                    const { prefix, mainName, honorific } = parseLine(line, language);
                    return (
                      <div key={`name-${language}-${index}`} className="py-2 my-1">
                         <p className="text-lg md:text-xl leading-relaxed">
                            {prefix && <span className={cn("text-muted-foreground/80", isRtl ? "ml-1" : "mr-1")}>{prefix}</span>}
                            <strong className="font-semibold text-xl md:text-2xl text-primary">{mainName}</strong>
                            {honorific && (
                                <>
                                    {language === 'ur' ? <>{' '}<span className="text-base md:text-lg text-muted-foreground">{honorific}۔</span></>
                                    : language === 'ar' ? <>{' '}<span className="text-base md:text-lg text-muted-foreground">{honorific}.</span></>
                                    : <>{', '}<span className="text-base md:text-lg text-muted-foreground">{honorific}{language === 'hi' ? '।' : '.'}</span></>
                                    }
                                </>
                            )}
                        </p>
                      </div>
                    );
                  })}
                </div>

                {conclusionLines.length > 0 && <Separator className="my-6 bg-border/70" />}
                
                {/* Conclusion */}
                {conclusionLines.map((line, index) => {
                     const isAllahumma = line.toLowerCase().startsWith("allahumma") || line.startsWith("अल्लाहुम्म") || line.startsWith("اللَّهُمَّ");
                    return (
                        <p key={`conclusion-${language}-${index}`} className={cn("mt-3 leading-relaxed text-lg md:text-xl", isAllahumma && "font-semibold text-primary text-xl md:text-2xl mt-4")}>{line}</p>
                    );
                })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
    
  

    