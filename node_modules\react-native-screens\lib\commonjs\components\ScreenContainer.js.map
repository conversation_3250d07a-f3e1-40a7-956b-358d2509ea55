{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireDefault", "_core", "_ScreenContainerNativeComponent", "_ScreenNavigationContainerNativeComponent", "obj", "__esModule", "default", "NativeScreenContainer", "exports", "Platform", "OS", "ScreenContainerNativeComponent", "View", "NativeScreenNavigationContainer", "ScreenNavigationContainerNativeComponent", "ScreenContainer", "props", "enabled", "screensEnabled", "hasTwoStates", "rest", "isNativePlatformSupported", "ScreenNavigationContainer", "createElement", "_default"], "sourceRoot": "../../../src", "sources": ["components/ScreenContainer.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAGA,IAAAI,+BAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,yCAAA,GAAAH,sBAAA,CAAAF,OAAA;AAA0G,SAAAE,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAF1G;;AAIO,MAAMG,qBAAgE,GAAAC,OAAA,CAAAD,qBAAA,GAC3EE,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIC,uCAA8B,GAAWC,iBAAI;AACjE,MAAMC,+BAA0E,GAAAL,OAAA,CAAAK,+BAAA,GACrFJ,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAChBI,iDAAwC,GACzCF,iBAAI;AAEV,SAASG,eAAeA,CAACC,KAA2B,EAAE;EACpD,MAAM;IAAEC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAAEC,YAAY;IAAE,GAAGC;EAAK,CAAC,GAAGJ,KAAK;EAEnE,IAAIC,OAAO,IAAII,+BAAyB,EAAE;IACxC,IAAIF,YAAY,EAAE;MAChB,MAAMG,yBAAyB,GAC7Bb,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACjBG,+BAA+B,GAC/BN,qBAAqB;MAC3B,oBAAOR,MAAA,CAAAO,OAAA,CAAAiB,aAAA,CAACD,yBAAyB,EAAKF,IAAO,CAAC;IAChD;IACA,oBAAOrB,MAAA,CAAAO,OAAA,CAAAiB,aAAA,CAAChB,qBAAqB,EAAKa,IAAO,CAAC;EAC5C;EACA,oBAAOrB,MAAA,CAAAO,OAAA,CAAAiB,aAAA,CAAC1B,YAAA,CAAAe,IAAI,EAAKQ,IAAO,CAAC;AAC3B;AAAC,IAAAI,QAAA,GAAAhB,OAAA,CAAAF,OAAA,GAEcS,eAAe"}