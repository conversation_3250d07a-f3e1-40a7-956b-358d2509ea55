"use client";

import { useState } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, Wand2 } from 'lucide-react';
import { getPersonalizedPracticeRecommendations } from '@/ai/flows/personalized-practice-recommendations';
import type { PersonalizedPracticeRecommendationsInput, PersonalizedPracticeRecommendationsOutput } from '@/ai/flows/personalized-practice-recommendations';

const FormSchema = z.object({
  preferences: z.string().min(10, { message: "Please describe your preferences in at least 10 characters." }).max(500),
  progress: z.string().min(10, { message: "Please describe your progress in at least 10 characters." }).max(500),
});

type FormData = z.infer<typeof FormSchema>;

export default function PersonalizedPracticeForm() {
  const [recommendations, setRecommendations] = useState<PersonalizedPracticeRecommendationsOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      preferences: "",
      progress: "",
    },
  });

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    setIsLoading(true);
    setError(null);
    setRecommendations(null);
    try {
      const inputData: PersonalizedPracticeRecommendationsInput = {
        preferences: data.preferences,
        progress: data.progress,
      };
      const result = await getPersonalizedPracticeRecommendations(inputData);
      setRecommendations(result);
    } catch (e) {
      console.error(e);
      setError("Failed to get recommendations. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center"><Wand2 className="h-6 w-6 mr-2 text-primary" /> Get Personalized Recommendations</CardTitle>
        <CardDescription>
          Share your spiritual preferences and current progress to receive AI-powered practice suggestions based on the Silsila Aaliya Chishtiya Sabiriya.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="preferences"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Preferences</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., I prefer silent Zikr, looking for practices to increase focus, interested in morning routines."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="progress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Current Progress</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="e.g., I perform daily prayers, struggle with consistency in Paas Anfaas, have read some basic Sufi texts."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isLoading} className="w-full sm:w-auto">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Getting Recommendations...
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  Suggest Practices
                </>
              )}
            </Button>
          </form>
        </Form>

        {error && (
          <div className="mt-6 p-4 bg-destructive/10 text-destructive border border-destructive/20 rounded-md">
            <p>{error}</p>
          </div>
        )}

        {recommendations && (
          <div className="mt-8 pt-6 border-t">
            <h3 className="text-xl font-semibold mb-3 text-foreground">Your Personalized Recommendations:</h3>
            <div className="p-4 bg-muted/50 rounded-md whitespace-pre-wrap text-foreground">
              {recommendations.recommendations}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
