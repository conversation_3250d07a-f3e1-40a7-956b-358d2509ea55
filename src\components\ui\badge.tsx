import * as React from "react"
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  primaryForeground: '#ffffff',
  secondary: '#f1f5f9',
  secondaryForeground: '#0f172a',
  destructive: '#ef4444',
  destructiveForeground: '#ffffff',
  foreground: '#0f172a',
  border: '#e2e8f0',
}

export type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline'

export interface BadgeProps extends ViewProps {
  variant?: BadgeVariant
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

const Badge = React.forwardRef<View, BadgeProps>(
  ({ variant = 'default', children, style, textStyle, ...props }, ref) => {
    const badgeStyle = getBadgeStyle(variant)
    const badgeTextStyle = getBadgeTextStyle(variant)

    return (
      <View
        ref={ref}
        style={[styles.base, badgeStyle, style]}
        {...props}
      >
        <Text style={[badgeTextStyle, textStyle]}>
          {children}
        </Text>
      </View>
    )
  }
)

Badge.displayName = "Badge"

const getBadgeStyle = (variant: BadgeVariant): ViewStyle => {
  switch (variant) {
    case 'default':
      return {
        backgroundColor: colors.primary,
        borderWidth: 0,
      }
    case 'secondary':
      return {
        backgroundColor: colors.secondary,
        borderWidth: 0,
      }
    case 'destructive':
      return {
        backgroundColor: colors.destructive,
        borderWidth: 0,
      }
    case 'outline':
      return {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: colors.border,
      }
    default:
      return {
        backgroundColor: colors.primary,
        borderWidth: 0,
      }
  }
}

const getBadgeTextStyle = (variant: BadgeVariant): TextStyle => {
  switch (variant) {
    case 'default':
      return {
        color: colors.primaryForeground,
      }
    case 'secondary':
      return {
        color: colors.secondaryForeground,
      }
    case 'destructive':
      return {
        color: colors.destructiveForeground,
      }
    case 'outline':
      return {
        color: colors.foreground,
      }
    default:
      return {
        color: colors.primaryForeground,
      }
  }
}

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 2,
    fontSize: 12,
    fontWeight: '600',
    minHeight: 20,
  },
})

export { Badge }
