import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage, availableLanguagesList } from '@/context/LanguageContext';

export default function LanguageSelectionScreen() {
  const { language, setLanguage } = useLanguage();
  const navigation = useNavigation();

  const handleLanguageSelect = async (languageCode: string) => {
    await setLanguage(languageCode as any);
    navigation.navigate('Login' as never);
  };

  const renderLanguageItem = ({ item }: { item: typeof availableLanguagesList[0] }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        language === item.code && styles.selectedLanguageItem
      ]}
      onPress={() => handleLanguageSelect(item.code)}
    >
      <Text style={[
        styles.languageText,
        language === item.code && styles.selectedLanguageText
      ]}>
        {item.name}
      </Text>
      {language === item.code && (
        <Ionicons name="checkmark" size={24} color="#14b8a6" />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Choose Your Language</Text>
          <Text style={styles.subtitle}>
            Select your preferred language to continue
          </Text>
        </View>

        <FlatList
          data={availableLanguagesList}
          renderItem={renderLanguageItem}
          keyExtractor={(item) => item.code}
          contentContainerStyle={styles.languageList}
          showsVerticalScrollIndicator={false}
        />

        <TouchableOpacity
          style={styles.continueButton}
          onPress={() => navigation.navigate('Login' as never)}
        >
          <Text style={styles.continueButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  languageList: {
    paddingBottom: 20,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedLanguageItem: {
    borderWidth: 2,
    borderColor: '#14b8a6',
    backgroundColor: '#f0fdfa',
  },
  languageText: {
    fontSize: 16,
    color: '#1f2937',
    fontWeight: '500',
  },
  selectedLanguageText: {
    color: '#14b8a6',
    fontWeight: '600',
  },
  continueButton: {
    backgroundColor: '#14b8a6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
