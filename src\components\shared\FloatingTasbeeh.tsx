
"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RotateCcw, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

type FloatingTasbeehProps = {
  initialCount?: number;
  storageKey?: string;
  className?: string;
};

export default function FloatingTasbeeh({ 
  initialCount = 0, 
  storageKey = "floatingTasbeehCount",
  className 
}: FloatingTasbeehProps) {
  const [count, setCount] = useState(() => {
    if (typeof window !== 'undefined') {
      const storedCount = localStorage.getItem(storageKey);
      return storedCount ? parseInt(storedCount, 10) : initialCount;
    }
    return initialCount;
  });

  const [isVisible, setIsVisible] = useState(true); // To allow hiding if needed in future

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, String(count));
    }
  }, [count, storageKey]);

  const increment = () => setCount(prev => prev + 1);
  const reset = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent increment if reset is part of the main button
    setCount(0);
  };

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed bottom-6 right-6 z-50 flex flex-col items-center group",
        className
      )}
    >
      <Button
        onClick={increment}
        variant="default"
        className={cn(
          "rounded-full w-20 h-20 p-0 flex flex-col items-center justify-center shadow-2xl",
          "bg-gradient-to-br from-primary via-primary/80 to-accent text-primary-foreground",
          "hover:scale-110 hover:shadow-primary/50 transition-all duration-300 ease-out"
        )}
        aria-label="Increment Tasbeeh Count"
      >
        <span className="text-3xl font-bold tabular-nums block leading-none">
          {count}
        </span>
        <Plus className="w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity" />
      </Button>
      <Button
        onClick={reset}
        variant="ghost"
        size="icon"
        className="mt-2 rounded-full w-8 h-8 bg-card/80 text-muted-foreground backdrop-blur-sm hover:bg-card hover:text-foreground transition-opacity opacity-50 group-hover:opacity-100"
        aria-label="Reset Tasbeeh Count"
      >
        <RotateCcw className="h-4 w-4" />
      </Button>
    </div>
  );
}
