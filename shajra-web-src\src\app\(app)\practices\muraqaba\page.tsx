
"use client";

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Brain, Heart, ListChecks, Watch, BookOpen } from 'lucide-react';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';
import { useLanguage, type Language } from '@/context/LanguageContext';
import React from 'react';

type MuraqabaSection = {
  heading: string;
  text?: string;
  points?: string[];
  conclusion?: string;
};

type MuraqabaPageContent = {
  pageTitle: string;
  pageSubtitle: string;
  backToPractices: string;
  intro: MuraqabaSection;
  whatIsMuraqaba: MuraqabaSection;
  foundations: MuraqabaSection & { stagesIntro: string; levelsIntro?: string; levels?: string[] };
  inAllSituations: MuraqabaSection;
  timeAndMuraqaba: MuraqabaSection;
  fruits: MuraqabaSection;
};


const translations: Record<Language, MuraqabaPageContent> = {
  ur: {
    pageTitle: "مراقبہ",
    pageSubtitle: "اللہ کے قرب کا راستہ",
    backToPractices: "روحانی اعمال کی طرف واپس",
    intro: {
      heading: "تعارف",
      text: "میرے عزیز۔ اللہ رب العزت کا بے پایاں کرم و احسان ہے کہ اُس نے ہمیں اپنی یاد اور معرفت کی راہ سُجھائی۔ دیکھو! دنیا ایک مسافر خانہ ہے اور ہم سب ایک سفر پر ہیں جس کا اصل زادِ راہ اللہ کی یاد، محبت اور اس کا قرب ہے۔ مراقبہ اسی قرب کو پانے کا ایک نہایت مؤثر اور آزمودہ ذریعہ ہے۔"
    },
    whatIsMuraqaba: {
      heading: "مراقبہ کیا ہے؟",
      text: "مراقبہ صرف چند الفاظ دہرانے کا نام نہیں، بلکہ یہ دل کی ایک گہری کیفیت اور روح کا ایک احساس ہے۔ یہ اس بات کا پختہ یقین ہے کہ ہمارا خالق و مالک اللہ تعالیٰ ہمیں ہر لمحہ دیکھ رہا ہے۔ یہی احساس انسان کو گناہوں سے بچاتا، نیکی کی طرف مائل کرتا اور دل میں اللہ کی محبت پیدا کرتا ہے۔ مراقبے کی اصل روح یہ ہے کہ بندہ ہر لمحہ اپنے رب کا دھیان رکھے اور دل کی پوری توجہ اسی کی طرف ہو۔"
    },
    foundations: {
      heading: "مراقبے کی بنیادیں اور درجات",
      stagesIntro: "مراقبے کی منزل پانے سے پہلے \"محاسبہ\" یعنی اپنے نفس کا حساب لینا ضروری ہے۔ اس کے چار اہم مراحل ہیں:",
      points: [
        "۱۔ شرائط (معاہدہ کرنا) : اپنے نفس سے اللہ کے احکامات پر عمل کرنے اور اپنے اعضاء کو گناہوں سے بچانے کا پختہ عہد کرنا۔",
        "۲۔ نگہبانی (پاسداری) : ہر لمحہ اپنے نفس کی نگرانی کرنا کہ وہ اپنے عہد پر قائم ہے یا نہیں۔",
        "۳۔ حساب (جانچ پڑتال) : دن کے اختتام پر اپنے اعمال کا جائزہ لینا کہ کیا کھویا اور کیا پایا۔",
        "۴۔ عتاب و عقوبت (سزا و تادیب) : اگر نفس سے کوئی کوتاہی یا گناہ سرزد ہو تو اسے ایسی سزا دینا جو اس پر گراں گزرے تاکہ آئندہ وہ محتاط رہے۔"
      ],
      levelsIntro: "جب دل ان مراحل سے گزرتا ہے تو مراقبے کی کیفیت پیدا ہوتی ہے، جس کے دو درجے ہیں۔",
      levels: [
        "۱۔صدیقین کا مراقبہ:  اللہ کی عظمت و جلال کے مشاہدے میں اس قدر ڈوب جانا کہ دل پر ہیبت طاری ہو جائے اور کسی دوسری طرف توجہ کی گنجائش نہ رہے۔",
        "۲۔اصحاب الیمین (پرہیزگاروں) کا مراقبہ: ان کے دلوں پر یہ یقین غالب ہوتا ہے کہ اللہ ان کے ظاہر و باطن سے پوری طرح آگاہ ہے۔ یہ اللہ سے حیا انہیں غلط کاموں سے روکتی ہے۔"
      ]
    },
    inAllSituations: {
      heading: "مراقبہ ہر حال میں",
      text: "مراقبہ صرف مخصوص اوقات یا عبادات تک محدود نہیں، بلکہ ہر عمل سے پہلے (نیت کا جائزہ لینا)، عمل کے دوران (اخلاص اور آداب کا خیال رکھنا) اور یہاں تک کہ روزمرہ کے مباح کاموں (کھانا، پینا، سونا) میں بھی اللہ کا شکر اور عبادت پر قوت حاصل کرنے کی نیت سے اس کا لحاظ رکھنا ضروری ہے۔"
    },
    timeAndMuraqaba: {
      heading: "وقت اور مراقبہ",
      text: "گزرےہوئے وقت پر اختیار نہیں  اور آنے والے وقت کا علم نہیں۔ لہٰذا،   حال میں رہ ۔ ماضِی  ، مستقبل سےنکل کر لمحے موجود میں حاضر  رہنا   مراقبہ کا اہم رکن ہے۔"
    },
    fruits: {
      heading: "مراقبے کے ثمرات",
      text: "خلوص دل سے مراقبہ کرنے والے پر اللہ اپنے فضل و کرم کے دروازے کھول دیتا ہے۔ اس کا دل نورِ معرفت سے روشن ہوتا ہے، گناہوں سے نفرت اور نیکیوں سے محبت پیدا ہوتی ہے۔ وہ اللہ کی محبت میں سرشار ہو کر ہر لمحہ اس کی رضا کا طالب رہتا ہے، جس سے زندگی میں حقیقی سکون اور اطمینان حاصل ہوتا ہے۔"
    }
  },
  en: {
    pageTitle: "Muraqaba (Contemplation)",
    pageSubtitle: "The Path to Closeness with Allah",
    backToPractices: "Back to Spiritual Practices",
    intro: {
      heading: "Introduction",
      text: "My dear one. It is the boundless grace and favor of Allah, the Lord of Honor, that He has shown us the way to His remembrance and gnosis. Behold! The world is a traveler's inn, and we are all on a journey whose true provision is the remembrance of Allah, His love, and His closeness. Muraqaba is an exceedingly effective and time-tested means of attaining this closeness."
    },
    whatIsMuraqaba: {
      heading: "What is Muraqaba?",
      text: "Muraqaba is not merely the repetition of a few words; rather, it is a profound state of the heart and a deep awareness of the soul. It is the firm conviction that our Creator and Master, Allah Almighty, is watching us at every moment. This very awareness protects a person from sins, inclines them towards righteousness, and cultivates the love of Allah in the heart. The true spirit of Muraqaba is that the servant remains mindful of their Lord at every moment and directs the heart's full attention towards Him."
    },
    foundations: {
      heading: "Foundations and Levels of Muraqaba",
      stagesIntro: "Before reaching the station of Muraqaba, it is necessary to perform 'Muhasaba,' i.e., to take account of one's self. It has four important stages:",
      points: [
        "1. Conditions (Making a Covenant): To make a firm pledge with one's self to act upon Allah's commands and to protect one's limbs from sins.",
        "2. Vigilance (Guarding): To monitor one's self at every moment to see if it is steadfast upon its pledge or not.",
        "3. Accountability (Scrutiny): At the end of the day, to review one's actions to see what was lost and what was gained.",
        "4. Reproach and Chastisement (Punishment and Admonition): If any shortcoming or sin is committed by the self, to give it such a punishment that is burdensome for it, so that it may be cautious in the future."
      ],
      levelsIntro: "When the heart passes through these stages, the state of Muraqaba is developed, which has two levels:",
      levels: [
        "1. Muraqaba of the Siddiqin (The Truthful): To be so engrossed in the observation of Allah's majesty and glory that awe overcomes the heart, leaving no room for attention to anything else.",
        "2. Muraqaba of the Ashab al-Yamin (The Righteous/People of the Right Hand): The conviction that Allah is fully aware of their outward and inward states predominates their hearts. This shyness (Haya) from Allah restrains them from wrongdoings."
      ]
    },
    inAllSituations: {
      heading: "Muraqaba in All Situations",
      text: "Muraqaba is not limited to specific times or acts of worship. Rather, it is essential to be mindful of Allah before every action (by examining the intention), during the action (by observing sincerity and etiquette), and even in everyday permissible acts (like eating, drinking, sleeping) with the intention of gratitude to Allah and gaining strength for worship."
    },
    timeAndMuraqaba: {
      heading: "Time and Muraqaba",
      text: "There is no control over time that has passed, and no knowledge of time that is yet to come. Therefore, live in the present. To emerge from the past and future and be present in the current moment is an important pillar of Muraqaba."
    },
    fruits: {
      heading: "Fruits of Muraqaba",
      text: "Allah opens the doors of His grace and bounty for the one who performs Muraqaba with a sincere heart. Their heart is illuminated with the light of gnosis (Ma'rifat), aversion to sins and love for good deeds is cultivated. Immersed in the love of Allah, they constantly seek His pleasure, thereby attaining true peace and contentment in life."
    }
  },
  ro: {
    pageTitle: "Muraqaba",
    pageSubtitle: "Allah ke qurb ka raasta",
    backToPractices: "Roohaani Amaal ki Taraf Wapas",
    intro: {
      heading: "Ta'aruf",
      text: "Mere 'azeez. Allah Rabb-ul-'Izzat ka be-paayaan karam o ehsaan hai keh Usne hamein Apni yaad aur ma'rifat ki raah sujhaa'i. Dekho! Dunya ek musaafir-khaana hai aur hum sab ek safar par hain jiska asl zaad-e-raah Allah ki yaad, muhabbat aur Uska qurb hai. Muraqaba isi qurb ko paane ka ek nihaayat mu'assir aur aazmooda zariya hai."
    },
    whatIsMuraqaba: {
      heading: "Muraqaba Kya Hai?",
      text: "Muraqaba sirf chand alfaaz dohraane ka naam nahin, balke yeh dil ki ek gehri kaifiyat aur rooh ka ek ehsaas hai. Yeh is baat ka pukhta yaqeen hai keh hamara Khaaliq o Maalik Allah Ta'ala hamein har lamha dekh raha hai. Yahi ehsaas insaan ko gunaahon se bachaata, neki ki taraf maa'il karta aur dil mein Allah ki muhabbat paida karta hai. Muraqabe ki asl rooh yeh hai keh banda har lamha apne Rabb ka dhyaan rakhe aur dil ki poori tawajjuh Usi ki taraf ho."
    },
    foundations: {
      heading: "Muraqabe ki Buniyaadein aur Darajaat",
      stagesIntro: "Muraqabe ki manzil paane se pehle \"Muhasaba\" ya'ni apne nafs ka hisaab lena zaroori hai. Iske chaar aham maraahil hain:",
      points: [
        "1. Sharaa'it (Mu'aahida Karna): Apne nafs se Allah ke ehkaamaat par 'amal karne aur apne a'zaa ko gunaahon se bachaane ka pukhta 'ahd karna.",
        "2. Nigahbaani (Paasdaari): Har lamha apne nafs ki nigraani karna keh woh apne 'ahd par qaayam hai ya nahin.",
        "3. Hisaab (Jaanch Partaal): Din ke ikhtitaam par apne a'maal ka jaa'iza lena keh kya khoya aur kya paaya.",
        "4. 'Itaab o 'Uqoobat (Saza o Taadeeb): Agar nafs se koi kotaahi ya gunaah sarzad ho toh use aisi saza dena jo us par giraan guzre taakeh aa'inda woh muhtaat rahe."
      ],
      levelsIntro: "Jab dil in maraahil se guzarta hai toh muraqabe ki kaifiyat paida hoti hai, jiske do darje hain:",
      levels: [
        "1. Siddiqeen ka Muraqaba: Allah ki 'azmat o jalaal ke mushaahade mein is qadar doob jaana keh dil par haibat taari ho jaaye aur kisi doosri taraf tawajjuh ki gunjaa'ish na rahe.",
        "2. Ashaab-ul-Yameen (Parhezgaaron) ka Muraqaba: Unke dilon par yeh yaqeen ghaalib hota hai keh Allah unke zaahir o baatin se poori tarah aagaah hai. Yeh Allah se hayaa unhein ghalat kaamon se rokti hai."
      ]
    },
    inAllSituations: {
      heading: "Muraqaba Har Haal Mein",
      text: "Muraqaba sirf makhsoos auqaat ya 'ibaadaat tak mahdood nahin, balke har 'amal se pehle (niyyat ka jaa'iza lena), 'amal ke dauraan (ikhlaas aur aadaab ka khayaal rakhna) aur yahan tak keh rozmarra ke mubaah kaamon (khaana, peena, sona) mein bhi Allah ka shukr aur 'ibaadat par quwwat haasil karne ki niyyat se Uska lihaaz rakhna zaroori hai."
    },
    timeAndMuraqaba: {
      heading: "Waqt aur Muraqaba",
      text: "Guzre hue waqt par ikhtiyaar nahin aur aane waale waqt ka 'ilm nahin. Lihaza, haal mein reh. Maazi, mustaqbil se nikal kar lamha-e-maujood mein haazir rehna muraqabe ka aham rukn hai."
    },
    fruits: {
      heading: "Muraqabe ke Samaraat",
      text: "Khuloos-e-dil se muraqaba karne waale par Allah apne fazl o karam ke darwaaze khol deta hai. Uska dil noor-e-ma'rifat se raushan hota hai, gunaahon se nafrat aur nekiyon se muhabbat paida hoti hai. Woh Allah ki muhabbat mein sarshaar ho kar har lamha Uski raza ka taalib rehta hai, jisse zindagi mein haqeeqi sukoon aur itminaan haasil hota hai."
    }
  },
  hi: {
    pageTitle: "मुराक़बा",
    pageSubtitle: "अल्लाह के क़ुर्ब का रास्ता",
    backToPractices: "रूहानी आमाल की तरफ़ वापस",
    intro: {
      heading: "तआरुफ़",
      text: "मेरे अज़ीज़। अल्लाह रब्ब-उल-इज्ज़त का बे-पायाँ करम ओ एहसान है कि उसने हमें अपनी याद और मारिफ़त की राह सुझाई। देखो! दुनिया एक मुसाफ़िर-ख़ाना है और हम सब एक सफ़र पर हैं जिसका अस्ल ज़ाद-ए-राह अल्लाह की याद, मुहब्बत और उसका क़ुर्ब है। मुराक़बा इसी क़ुर्ब को पाने का एक निहायत मुअस्सिर और आज़मूदा ज़रिया है।"
    },
    whatIsMuraqaba: {
      heading: "मुराक़बा क्या है?",
      text: "मुराक़बा सिर्फ़ चंद अल्फ़ाज़ दोहराने का नाम नहीं, बल्कि यह दिल की एक गहरी कैफ़ियत और रूह का एक एहसास है। यह इस बात का पुख़्ता यक़ीन है कि हमारा ख़ालिक़ ओ मालिक अल्लाह तआला हमें हर लम्हा देख रहा है। यही एहसास इंसान को गुनाहों से बचाता, नेकी की तरफ़ माइल करता और दिल में अल्लाह की मुहब्बत पैदा करता है। मुराक़बे की अस्ल रूह यह है कि बंदा हर लम्हा अपने रब्ब का ध्यान रखे और दिल की पूरी तवज्जोह उसी की तरफ़ हो।"
    },
    foundations: {
      heading: "मुराक़बे की बुनियादें और दरजात",
      stagesIntro: "मुराक़बे की मंज़िल पाने से पहले \"मुहासबा\" यानी अपने नफ़्स का हिसाब लेना ज़रूरी है। इसके चार अहम मराहिल हैं:",
      points: [
        "१. शराइत (मुआहिदा करना): अपने नफ़्स से अल्लाह के एहका मात पर अमल करने और अपने आज़ा को गुनाहों से बचाने का पुख़्ता अहद करना।",
        "२. निगहबानी (पासदारी): हर लम्हा अपने नफ़्स की निगरानी करना कि वह अपने अहद पर क़ायम है या नहीं।",
        "३. हिसाब (जाँच पड़ताल): दिन के इख़्तिताम पर अपने आमाल का जायज़ा लेना कि क्या खोया और क्या पाया।",
        "४. इताब ओ उक़ूबत (सज़ा ओ तादीब): अगर नफ़्स से कोई कोताही या गुनाह सरज़द हो तो उसे ऐसी सज़ा देना जो उस पर गिराँ गुज़रे ताकि आइंदा वह मुहतात रहे।"
      ],
      levelsIntro: "जब दिल इन मराहिल से गुज़रता है तो मुराक़बे की कैफ़ियत पैदा होती है, जिसके दो दरजे हैं:",
      levels: [
        "१. सिद्दीक़ीन का मुराक़बा: अल्लाह की अज़मत ओ जलाल के मुशाहदे में इस क़दर डूब जाना कि दिल पर हैबत तारी हो जाए और किसी दूसरी तरफ़ तवज्जोह की गुंजाइश न रहे।",
        "२. असहाब-उल-यमीन (परहेज़गारों) का मुराक़बा: उनके दिलों पर यह यक़ीन ग़ालिब होता है कि अल्लाह उनके ज़ाहिर ओ बातिन से पूरी तरह आगाह है। यह अल्लाह से हया उन्हें ग़लत कामों से रोकती है।"
      ]
    },
    inAllSituations: {
      heading: "मुराक़बा हर हाल में",
      text: "मुराक़बा सिर्फ़ मख़सूस औक़ात या इबादात तक महदूद नहीं, बल्कि हर अमल से पहले (नीयत का जायज़ा लेना), अमल के दौरान (इख़लास और आदाब का ख़याल रखना) और यहाँ तक कि रोज़मर्रा के मुबाह कामों (खाना, पीना, सोना) में भी अल्लाह का शुक्र और इबादत पर क़ुव्वत हासिल करने की नीयत से उसका लिहाज़ रखना ज़रूरी है।"
    },
    timeAndMuraqaba: {
      heading: "वक़्त और मुराक़बा",
      text: "गुज़रे हुए वक़्त पर इख़्तियार नहीं और आने वाले वक़्त का इल्म नहीं। लिहाज़ा, हाल में रह। माज़ी, मुस्तक़बिल से निकल कर लम्हा-ए-मौजूद में हाज़िर रहना मुराक़बे का अहम रुक्न है।"
    },
    fruits: {
      heading: "मुराक़बे के समरात",
      text: "ख़ुलूस-ए-दिल से मुराक़बा करने वाले पर अल्लाह अपने फ़ज़्ल ओ करम के दरवाज़े खोल देता है। उसका दिल नूर-ए-मारिफ़त से रौशन होता है, गुनाहों से नफ़रत और नेकियों से मुहब्बत पैदा होती है। वह अल्लाह की मुहब्बत में सरशार हो कर हर लम्हा उसकी रज़ा का तालिब रहता है, जिससे ज़िंदगी में हक़ीक़ी सुकून और इत्मीनान हासिल होता है।"
    }
  },
  ar: {
    pageTitle: "المراقبة",
    pageSubtitle: "طريق القرب إلى الله",
    backToPractices: "العودة إلى الممارسات الروحية",
    intro: {
      heading: "مقدمة",
      text: "يا عزيزي. إن من فضل الله العظيم وإحسانه أن أرانا طريق ذكره ومعرفته. اعلم! الدنيا دار ممر، وكلنا في سفر زاده الحقيقي ذكر الله ومحبته وقربه. والمراقبة وسيلة فعالة ومجربة لبلوغ هذا القرب."
    },
    whatIsMuraqaba: {
      heading: "ما هي المراقبة؟",
      text: "المراقبة ليست مجرد ترديد كلمات، بل هي حالة قلبية عميقة وإحساس روحي. هي اليقين التام بأن خالقنا ومالكنا الله تعالى يرانا في كل لحظة. هذا الإحساس هو ما يقي الإنسان من الذنوب، ويميله إلى الخير، ويزرع محبة الله في القلب. وروح المراقبة أن يظل العبد ذاكراً لربه في كل حين، وأن يكون توجه القلب كله إليه."
    },
    foundations: {
      heading: "أسس المراقبة ومراتبها",
      stagesIntro: "قبل بلوغ منزلة المراقبة، لا بد من \"المحاسبة\"، أي محاسبة النفس. ولها أربع مراحل مهمة:",
      points: [
        "١. المشارطة (المعاهدة): أن يعاهد نفسه على امتثال أوامر الله واجتناب نواهيه وحفظ جوارحه عن المعاصي.",
        "٢. المراقبة (الحراسة): أن يراقب نفسه في كل لحظة ليرى هل هو قائم على عهده أم لا.",
        "٣. المحاسبة (التدقيق): أن يحاسب نفسه في آخر النهار على ما قدم من عمل، فينظر ما الذي ربح وما الذي خسر.",
        "٤. المعاتبة والمعاقبة (التأديب): إذا صدر من النفس تقصير أو ذنب، عاقبها بما يشق عليها حتى تحذر في المستقبل."
      ],
      levelsIntro: "فإذا مر القلب بهذه المراحل، حصلت له كيفية المراقبة، وهي على درجتين:",
      levels: [
        "١. مراقبة الصديقين: هي الاستغراق في مشاهدة عظمة الله وجلاله حتى يغلب على القلب الهيبة فلا يلتفت إلى غيره.",
        "٢. مراقبة أصحاب اليمين (المتقين): يغلب على قلوبهم اليقين بأن الله مطلع على ظاهرهم وباطنهم، وهذا الحياء من الله يصدهم عن القبائح."
      ]
    },
    inAllSituations: {
      heading: "المراقبة في كل حال",
      text: "المراقبة لا تقتصر على أوقات أو عبادات مخصوصة، بل لا بد منها قبل كل عمل (بمحاسبة النية)، وأثناء العمل (بمراعاة الإخلاص والآداب)، وحتى في الأمور المباحة اليومية (كالأكل والشرب والنوم) بنية الشكر لله والتقوي على العبادة."
    },
    timeAndMuraqaba: {
      heading: "الوقت والمراقبة",
      text: "لا سلطان على ما فات من الوقت، ولا علم بما هو آت. فكن في الحال. الخروج من الماضي والمستقبل والحضور في اللحظة الراهنة ركن مهم في المراقبة."
    },
    fruits: {
      heading: "ثمرات المراقبة",
      text: "من راقب الله بقلب مخلص، فتح الله عليه أبواب فضله وكرمه. يستنير قلبه بنور المعرفة، وتتولد فيه نفرة من الذنوب ومحبة للطاعات. ويظل غارقًا في محبة الله، طالبًا رضاه في كل لحظة، مما يحقق له السكينة والطمأنينة الحقيقية في الحياة."
    }
  }
};

export default function MuraqabaPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  const sections = [
    { id: "intro", data: content.intro, icon: BookOpen },
    { id: "whatIs", data: content.whatIsMuraqaba, icon: Heart },
    { id: "foundations", data: content.foundations, icon: ListChecks },
    { id: "inAllSituations", data: content.inAllSituations, icon: Brain },
    { id: "time", data: content.timeAndMuraqaba, icon: Watch },
    { id: "fruits", data: content.fruits, icon: ListChecks }, // Re-using ListChecks for fruits/benefits
  ];

  return (
    <div className="space-y-8">
      <div className="px-0">
        <PageTitle 
          title={content.pageTitle} 
          subtitle={content.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      <div className="space-y-6">
        {sections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Card key={section.id} className="shadow-xl bg-card">
              <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
                <h2 className={cn("text-xl md:text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                  <IconComponent className={cn("h-6 w-6", textDir === 'rtl' ? "ml-3" : "mr-3")} />
                  {section.data.heading}
                </h2>
              </CardHeader>
              <CardContent 
                lang={language} 
                dir={textDir} 
                className={cn("space-y-4 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}
              >
                {section.data.text && <p>{section.data.text}</p>}
                {(section.id === "foundations" && (section.data as MuraqabaPageContent['foundations']).stagesIntro) && (
                  <p>{(section.data as MuraqabaPageContent['foundations']).stagesIntro}</p>
                )}
                {section.data.points && (
                  <ul className={cn("list-inside space-y-2", isRtl ? "list-decimal pr-4" : "list-disc pl-4", (language === 'en' || language === 'hi' || language === 'ro') && "list-decimal pl-4", language === 'ar' && "list-decimal pr-4")}>
                    {section.data.points.map((point, index) => (
                      <li key={`${section.id}-point-${index}`}>{point}</li>
                    ))}
                  </ul>
                )}
                 {(section.id === "foundations" && (section.data as MuraqabaPageContent['foundations']).levelsIntro) && (
                  <p className="mt-4">{(section.data as MuraqabaPageContent['foundations']).levelsIntro}</p>
                )}
                {(section.id === "foundations" && (section.data as MuraqabaPageContent['foundations']).levels) && (
                  <ul className={cn("list-inside space-y-2", isRtl ? "list-decimal pr-4" : "list-disc pl-4", (language === 'en' || language === 'hi' || language === 'ro') && "list-decimal pl-4", language === 'ar' && "list-decimal pr-4")}>
                    {(section.data as MuraqabaPageContent['foundations']).levels!.map((level, index) => (
                      <li key={`${section.id}-level-${index}`}>{level}</li>
                    ))}
                  </ul>
                )}
                {section.data.conclusion && <p className="mt-4">{section.data.conclusion}</p>}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}


    