{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNativeScreens", "_DelayedFreeze", "_ScreenStackNativeComponent", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "NativeScreenStack", "ScreenStackNativeComponent", "ScreenStack", "props", "children", "gestureDetectorBridge", "rest", "ref", "React", "useRef", "size", "Children", "count", "childrenWithFreeze", "map", "child", "index", "descriptor", "descriptors", "isFreezeEnabled", "options", "freezeOnBlur", "freezeEnabled", "createElement", "freeze", "useEffect", "current", "stackUseEffectCallback", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AAGA,IAAAG,2BAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA8E,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA,KAD9E;AAEA,MAAMQ,iBAAwD,GAC5DC,mCAAiC;AAEnC,SAASC,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IAAEC,QAAQ;IAAEC,qBAAqB;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EAC1D,MAAMI,GAAG,GAAGC,cAAK,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,IAAI,GAAGF,cAAK,CAACG,QAAQ,CAACC,KAAK,CAACR,QAAQ,CAAC;EAC3C;EACA,MAAMS,kBAAkB,GAAGL,cAAK,CAACG,QAAQ,CAACG,GAAG,CAACV,QAAQ,EAAE,CAACW,KAAK,EAAEC,KAAK,KAAK;IACxE;IACA,MAAM;MAAEb,KAAK;MAAER;IAAI,CAAC,GAAGoB,KAAK;IAC5B,MAAME,UAAU,GAAGd,KAAK,EAAEc,UAAU,IAAId,KAAK,EAAEe,WAAW,GAAGvB,GAAG,CAAC;IACjE,MAAMwB,eAAe,GACnBF,UAAU,EAAEG,OAAO,EAAEC,YAAY,IAAI,IAAAC,iCAAa,EAAC,CAAC;IAEtD,oBACE7C,MAAA,CAAAQ,OAAA,CAAAsC,aAAA,CAAC1C,cAAA,CAAAI,OAAa;MAACuC,MAAM,EAAEL,eAAe,IAAIT,IAAI,GAAGM,KAAK,GAAG;IAAE,GACxDD,KACY,CAAC;EAEpB,CAAC,CAAC;EAEFP,cAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAIpB,qBAAqB,EAAE;MACzBA,qBAAqB,CAACqB,OAAO,CAACC,sBAAsB,CAACpB,GAAG,CAAC;IAC3D;EACF,CAAC,CAAC;EACF,oBACE9B,MAAA,CAAAQ,OAAA,CAAAsC,aAAA,CAACvB,iBAAiB,EAAAd,QAAA,KAAKoB,IAAI;IAAEC,GAAG,EAAEA;EAAI,IACnCM,kBACgB,CAAC;AAExB;AAAC,IAAAe,QAAA,GAAAC,OAAA,CAAA5C,OAAA,GAEciB,WAAW"}