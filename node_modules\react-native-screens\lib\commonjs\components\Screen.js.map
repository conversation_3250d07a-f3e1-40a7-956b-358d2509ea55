{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_TransitionProgressContext", "_DelayedFreeze", "_core", "_ScreenNativeComponent", "_ModalScreenNativeComponent", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "NativeScreen", "exports", "ScreenNativeComponent", "AnimatedNativeScreen", "Animated", "createAnimatedComponent", "AnimatedNativeModalScreen", "ModalScreenNativeComponent", "InnerScreen", "React", "Component", "ref", "closing", "Value", "progress", "goingForward", "setNativeProps", "props", "setRef", "onComponentRef", "render", "enabled", "screensEnabled", "freezeOnBlur", "freezeEnabled", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetent", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "stackPresentation", "isNativePlatformSupported", "AnimatedScreen", "Platform", "OS", "undefined", "active", "activityState", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "console", "warn", "handleRef", "viewConfig", "validAttributes", "style", "display", "_viewConfig", "createElement", "freeze", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "value", "View", "ScreenContext", "createContext", "Screen", "contextType", "ScreenWrapper", "context", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAOA,IAAAK,sBAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,2BAAA,GAAAP,sBAAA,CAAAC,OAAA;AAA8E,SAAAD,uBAAAQ,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA,KAhB9E,wDAcA;AAIO,MAAMQ,YAA8C,GAAAC,OAAA,CAAAD,YAAA,GACzDE,8BAA4B;AAC9B,MAAMC,oBAAoB,GAAGC,qBAAQ,CAACC,uBAAuB,CAACL,YAAY,CAAC;AAC3E,MAAMM,yBAAyB,GAAGF,qBAAQ,CAACC,uBAAuB,CAChEE,mCACF,CAAC;;AAED;AACA;AAkBO,MAAMC,WAAW,SAASC,cAAK,CAACC,SAAS,CAAc;EACpDC,GAAG,GAAyC,IAAI;EAChDC,OAAO,GAAG,IAAIR,qBAAQ,CAACS,KAAK,CAAC,CAAC,CAAC;EAC/BC,QAAQ,GAAG,IAAIV,qBAAQ,CAACS,KAAK,CAAC,CAAC,CAAC;EAChCE,YAAY,GAAG,IAAIX,qBAAQ,CAACS,KAAK,CAAC,CAAC,CAAC;EAE5CG,cAAcA,CAACC,KAAkB,EAAQ;IACvC,IAAI,CAACN,GAAG,EAAEK,cAAc,CAACC,KAAK,CAAC;EACjC;EAEAC,MAAM,GAAIP,GAAyC,IAAW;IAC5D,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,KAAK,CAACE,cAAc,GAAGR,GAAG,CAAC;EAClC,CAAC;EAEDS,MAAMA,CAAA,EAAG;IACP,MAAM;MACJC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;MAC1BC,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;MAC9B,GAAGC;IACL,CAAC,GAAG,IAAI,CAACR,KAAK;;IAEd;IACA;IACA,MAAM;MACJS,mBAAmB,GAAG,OAAO;MAC7BC,0BAA0B,GAAG,KAAK;MAClCC,mBAAmB,GAAG,KAAK;MAC3BC,iBAAiB,GAAG,CAAC,GAAG;MACxBC,8BAA8B,GAAG,IAAI;MACrCC;IACF,CAAC,GAAGN,IAAI;IAER,IAAIJ,OAAO,IAAIW,+BAAyB,EAAE;MACxC;MACA,MAAMC,cAAc,GAClBC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBJ,iBAAiB,KAAKK,SAAS,IAC/BL,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,GAC7C5B,oBAAoB,GACpBG,yBAAyB;MAE/B,IAAI;QACF;QACA;QACA;QACA+B,MAAM;QACNC,aAAa;QACbC,QAAQ;QACRC,aAAa;QACbC,uBAAuB;QACvBC,eAAe;QACf,GAAGzB;MACL,CAAC,GAAGQ,IAAI;MAER,IAAIY,MAAM,KAAKD,SAAS,IAAIE,aAAa,KAAKF,SAAS,EAAE;QACvDO,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;QACDN,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MAEA,MAAMQ,SAAS,GAAIlC,GAAe,IAAK;QACrC,IAAIA,GAAG,EAAEmC,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAE;UAC3CrC,GAAG,CAACmC,UAAU,CAACC,eAAe,CAACC,KAAK,GAAG;YACrC,GAAGrC,GAAG,CAACmC,UAAU,CAACC,eAAe,CAACC,KAAK;YACvCC,OAAO,EAAE;UACX,CAAC;UACD,IAAI,CAAC/B,MAAM,CAACP,GAAG,CAAC;QAClB,CAAC,MAAM,IAAIA,GAAG,EAAEuC,WAAW,EAAEH,eAAe,EAAEC,KAAK,EAAE;UACnDrC,GAAG,CAACuC,WAAW,CAACH,eAAe,CAACC,KAAK,GAAG;YACtC,GAAGrC,GAAG,CAACuC,WAAW,CAACH,eAAe,CAACC,KAAK;YACxCC,OAAO,EAAE;UACX,CAAC;UACD,IAAI,CAAC/B,MAAM,CAACP,GAAG,CAAC;QAClB;MACF,CAAC;MAED,oBACErC,MAAA,CAAAW,OAAA,CAAAkE,aAAA,CAACxE,cAAA,CAAAM,OAAa;QAACmE,MAAM,EAAE7B,YAAY,IAAIe,aAAa,KAAK;MAAE,gBACzDhE,MAAA,CAAAW,OAAA,CAAAkE,aAAA,CAAClB,cAAc,EAAA/C,QAAA,KACT+B,KAAK;QACTqB,aAAa,EAAEA,aAAc;QAC7BZ,mBAAmB,EAAEA,mBAAoB;QACzCC,0BAA0B,EAAEA,0BAA2B;QACvDC,mBAAmB,EAAEA,mBAAoB;QACzCC,iBAAiB,EAAEA,iBAAkB;QACrCC,8BAA8B,EAAEA,8BAA+B;QAC/DW,uBAAuB,EAAE;UACvBY,KAAK,EAAEZ,uBAAuB,EAAEY,KAAK,IAAI,CAAC,CAAC;UAC3CC,GAAG,EAAEb,uBAAuB,EAAEa,GAAG,IAAI,CAAC,CAAC;UACvCC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;UACvCC,MAAM,EAAEf,uBAAuB,EAAEe,MAAM,IAAI,CAAC;QAC9C;QACA;QACA;QAAA;QACA7C,GAAG,EAAEkC,SAAU;QACfY,oBAAoB,EAClB,CAACjB,aAAa,GACVJ,SAAS,GACThC,qBAAQ,CAACsD,KAAK,CACZ,CACE;UACEC,WAAW,EAAE;YACX7C,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvBF,OAAO,EAAE,IAAI,CAACA,OAAO;YACrBG,YAAY,EAAE,IAAI,CAACA;UACrB;QACF,CAAC,CACF,EACD;UAAE6C,eAAe,EAAE;QAAK,CAC1B,CACL;QACDlB,eAAe,EACbA,eAAe,KACd,MAAM;UACL;QAAA,CACD;MACF,IACA,CAACF,aAAa;MAAK;MAClBD,QAAQ,gBAERjE,MAAA,CAAAW,OAAA,CAAAkE,aAAA,CAACzE,0BAAA,CAAAO,OAAyB,CAAC4E,QAAQ;QACjCC,KAAK,EAAE;UACLhD,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBF,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBG,YAAY,EAAE,IAAI,CAACA;QACrB;MAAE,GACDwB,QACiC,CAExB,CACH,CAAC;IAEpB,CAAC,MAAM;MACL;MACA,IAAI;QACFF,MAAM;QACNC,aAAa;QACbU,KAAK;QACL;QACA7B,cAAc;QACd,GAAGF;MACL,CAAC,GAAGQ,IAAI;MAER,IAAIY,MAAM,KAAKD,SAAS,IAAIE,aAAa,KAAKF,SAAS,EAAE;QACvDE,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MACtC;MACA,oBACE/D,MAAA,CAAAW,OAAA,CAAAkE,aAAA,CAAC1E,YAAA,CAAA2B,QAAQ,CAAC2D,IAAI,EAAA7E,QAAA;QACZ8D,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEC,OAAO,EAAEX,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC,CAAE;QACnE3B,GAAG,EAAE,IAAI,CAACO;MAAO,GACbD,KAAK,CACV,CAAC;IAEN;EACF;AACF;;AAEA;AACA;AAAAhB,OAAA,CAAAO,WAAA,GAAAA,WAAA;AACO,MAAMwD,aAAa,GAAA/D,OAAA,CAAA+D,aAAA,gBAAGvD,cAAK,CAACwD,aAAa,CAACzD,WAAW,CAAC;AAE7D,MAAM0D,MAAM,SAASzD,cAAK,CAACC,SAAS,CAAc;EAChD,OAAOyD,WAAW,GAAGH,aAAa;EAElC5C,MAAMA,CAAA,EAAG;IACP,MAAMgD,aAAa,GAAI,IAAI,CAACC,OAAO,IAAI7D,WAAiC;IACxE,oBAAOlC,MAAA,CAAAW,OAAA,CAAAkE,aAAA,CAACiB,aAAa,EAAK,IAAI,CAACnD,KAAQ,CAAC;EAC1C;AACF;AAAC,IAAAqD,QAAA,GAAArE,OAAA,CAAAhB,OAAA,GAEciF,MAAM"}