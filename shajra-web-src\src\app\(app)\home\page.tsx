
"use client"; // Required for useState and framer-motion client-side features

import * as React from 'react'; // Added React import
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import PageTitle from '@/components/shared/PageTitle';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { ArrowRight, BookOpen, PlayCircle, ListChecks, Users, Lightbulb, Brain, UserRound, Eye, Users2, BookText, BookMarked, Repeat, Hand, MousePointerSquare, Phone, CheckSquare, ClipboardList, Megaphone } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react'; // Import useState and useEffect
import { useToast } from "@/hooks/use-toast";
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useLanguage, type Language } from '@/context/LanguageContext';


const translations = {
  pageTitle: {
    ar: "ٱلسَّلَامُ عَلَيْكُمْ",
    ur: "ٱلسَّلَامُ عَلَيْكُمْ",
    ro: "Assalamu Alaikum",
    en: "Assalamu Alaikum",
    hi: "अस्सलामु अलैकुम",
  },
  pageSubtitle: {
    ar: "مرحباً بك في رفيقك الروحي.",
    ur: "آپ کے روحانی ساتھی میں خوش آمدید۔",
    ro: "Aapke roohani saathi mein khush amdeed.",
    en: "Welcome to your spiritual companion.",
    hi: "आपके रूहानी साथी में खुश आमदीद।",
  },
  callingKhanqahButton: {
    en: "Call Khanqah",
    ur: "خانقاہ پر کال کریں",
    ro: "Khanqah par Call Karein",
    hi: "खानक़ाह पर कॉल करें",
    ar: "اتصل بالخانقاه",
  },
  hazriLagwayenButton: {
    mainText: {
      en: "Request Attendance",
      ur: "حاضری لگوائیں",
      ro: "Hazri Lagwayein",
      hi: "हाज़िरी लगवाएँ",
      ar: "طلب الحضور",
    },
    requestSent: {
      en: "Assalamu Alaikum. Your request is being sent to the Khanqah Shareef, please wait a moment...",
      ur: "السلام علیکم۔ خانقاہ شریف میں خبر بھیجی جا رہی ہے، تھوڑا انتظار کریں۔",
      ro: "Assalamu Alaikum. Khanqah Shareef mein khabar bheji ja rahi hai, thoda intezaar karein...",
      hi: "अस्सलामु अलैकुम। खानक़ाह शरीफ़ में ख़बर भेजी जा रही है, थोड़ा इंतज़ार करें...",
      ar: "السلام عليكم. يتم إرسال طلبكم إلى الخانقاه الشريف. يرجى الانتظار قليلاً...",
    },
  },
  announcementsButton: {
    en: "Announcements",
    ur: "اعلانات",
    ro: "Aelanaat",
    hi: "ऐलानात",
    ar: "الإعلانات",
  },
  quickLinks: {
    basicGuidelines: { en: "Basic Guidelines", ur: "بنیادی ہدایات", ro: "Buniyadi Hidayaat", hi: "बुनियादी हिदायात", ar: "إرشادات أساسية" },
    panjsoora: { en: "Panj Soorah", ur: "پنج سورہ", ro: "Panj Soorah", hi: "पंज सूरह", ar: "السور الخمس" },
    shajra: { en: "Noble Lineage (Shajra)", ur: "شجرہ شریف", ro: "Shajra Shareef", hi: "शजरह शरीफ़", ar: "الشجرة الشريفة" },
    saatNaseehatain: { en: "Seven Advices", ur: "سات نصیحتیں", ro: "Saat Naseehatain", hi: "सात नसीहतें", ar: "النصائح السبع" },
    shagleNoori: { en: "Practice of Light (Shagle Noori)", ur: "شغلِ نوری", ro: "Shaghl-e-Noori", hi: "शग़्ल-ए-नूरी", ar: "الشغل النوري" },
    muhasaba: { en: "Self-Accountability", ur: "محاسبہ", ro: "Muhasaba", hi: "मुहासबा", ar: "المحاسبة" },
    paasAnfaas: { en: "Awareness of Breath", ur: "پاسِ انفاس", ro: "Paas Anfaas", hi: "पास-ए-अनफ़ास", ar: "مراقبة الأنفاس" },
    muraqaba: { en: "Meditation", ur: "مراقبہ", ro: "Muraqaba", hi: "मुराक़बा", ar: "المراقبة" },
    tasawwureShaikh: { en: "Visualization of the Guide", ur: "تصورِ شیخ", ro: "Tasawwur-e-Shaikh", hi: "तसव्वुर-ए-शैख", ar: "تصور الشيخ" },
    tasawwureIsmeZaat: { en: "Visualization of the Divine Name", ur: "تصورِ اسمِ ذات", ro: "Tasawwur-e-Ism-e-Zaat", hi: "तसव्वुर-ए-इस्मे-ज़ात", ar: "تصور اسم الذات" },
    halqaeZikr: { en: "Circle of Remembrance", ur: "حلقۂ ذکر", ro: "Halqa-e-Zikr", hi: "हल्क़ा-ए-ज़िक्र", ar: "حلقة الذكر" },
    duaWazaif: { en: "Dua and Wazaif", ur: "دعا و وظائف", ro: "Dua and Wazaif", hi: "दुआ और वज़ाइफ़", ar: "الدعاء والوظائف" },
  },
  tasbeeh: { en: "Tasbeeh Counter", ur: "تسبیح کاؤنٹر", ro: "Tasbeeh Counter", hi: "तस्बीह काउंटर", ar: "عداد التسبيح" },
};


export default function HomePage() {
  const { language } = useLanguage();
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useAuth();

  const pageTitleText = translations.pageTitle[language] || translations.pageTitle.en;
  const pageSubtitleText = translations.pageSubtitle[language] || translations.pageSubtitle.en;
  const callingKhanqahButtonText = translations.callingKhanqahButton[language] || translations.callingKhanqahButton.en;
  const hazriLagwayenButtonText = translations.hazriLagwayenButton.mainText[language] || translations.hazriLagwayenButton.mainText.en;
  const announcementsButtonText = translations.announcementsButton[language] || translations.announcementsButton.en;

  const handleHazriLagwayen = () => {
    const userInfo = user ? `User: ${user.email}` : "An anonymous user";
    console.log(`Hazri Request (Simulated): ${userInfo} is requesting attendance. Admin would be notified here.`);
    
    const requestSentMessage = translations.hazriLagwayenButton.requestSent[language] || translations.hazriLagwayenButton.requestSent.en;
    
    toast({
      description: <span className={cn("font-medium", (language === 'ur' || language === 'ar') ? 'font-arabic' : 'font-sans')}>{requestSentMessage}</span>,
      duration: 3000,
    });

    setTimeout(() => {
      router.push('/hazri-pending');
    }, 1000); 
  };
  
  const isRtl = language === 'ur' || language === 'ar';

    const baseQuickLinks = [
      { id: 'basicGuidelines', href: '/teachings/basic-guidelines', iconComponent: BookText, iconColorClass: "text-emerald-600 dark:text-emerald-400" },
      { id: 'shajra', href: '/shajra', iconComponent: Users, iconColorClass: "text-sky-600 dark:text-sky-400" },
      { id: 'saatNaseehatain', href: '/teachings/saat-naseehatain', iconComponent: ListChecks, iconColorClass: "text-rose-600 dark:text-rose-400" },
      { id: 'shagleNoori', href: '/practices/shughl-noori', iconComponent: Lightbulb, iconColorClass: "text-amber-500 dark:text-amber-400" },
      { id: 'muhasaba', href: '/practices/muhasaba', iconComponent: ClipboardList, iconColorClass: "text-pink-600 dark:text-pink-400", isHighlighted: true },
      { id: 'paasAnfaas', href: '/practices/paas-anfaas', iconComponent: PlayCircle, iconColorClass: "text-violet-600 dark:text-violet-400" },
      { id: 'muraqaba', href: '/practices/muraqaba', iconComponent: Brain, iconColorClass: "text-indigo-600 dark:text-indigo-400" },
      { id: 'tasawwureShaikh', href: '/practices/tasawwure-shaikh', iconComponent: UserRound, iconColorClass: "text-pink-600 dark:text-pink-400" },
      { id: 'tasawwureIsmeZaat', href: '/practices/tasawwure-isme-zaat', iconComponent: Eye, iconColorClass: "text-fuchsia-600 dark:text-fuchsia-400" },
      { id: 'halqaeZikr', href: '/practices/halqae-zikr', iconComponent: Users2, iconColorClass: "text-cyan-600 dark:text-cyan-400" },
      { id: 'panjsoora', href: '/panjsoora', iconComponent: BookMarked, iconColorClass: "text-sky-600 dark:text-sky-400" },
      { id: 'duaWazaif', href: '/duas', iconComponent: BookOpen, iconColorClass: "text-lime-600 dark:text-lime-400" },
    ];


  const localizedQuickLinks = React.useMemo(() => {
    return baseQuickLinks.map(link => ({
      ...link,
      title: translations.quickLinks[link.id as keyof typeof translations.quickLinks]?.[language] || translations.quickLinks[link.id as keyof typeof translations.quickLinks]?.['en'],
    }));
  }, [language]); // Removed baseQuickLinks from dependency array as it's stable


  return (
    <>
    <div className="space-y-8 perspective-1000 transform-style-preserve-3d pb-24">
      <Card className={cn("shadow-xl", "glass-effect", "relative text-center")}>
        <CardContent className="p-6">
          <PageTitle
            title={pageTitleText}
            subtitle={pageSubtitleText}
            className={cn(
              "text-center mb-0 text-primary",
              (language === 'ur' || language === 'ar') && "font-arabic",
              language === 'hi' && "font-sans" 
            )}
          />
        </CardContent>
         <Link href="/announcements" passHref 
            className={cn(
              "absolute bottom-2 right-2 z-10 p-0",
               (language === 'ur' || language === 'ar') ? 'font-arabic' : 'font-sans'
            )}
            aria-label={announcementsButtonText}
          >
          <Button 
            variant="default" 
            size="icon" 
            className={cn(
              "bg-amber-500 hover:bg-amber-600 text-white dark:bg-amber-400 dark:hover:bg-amber-500 dark:text-amber-950",
              "shadow-md hover:shadow-lg"
            )}
          >
            <Megaphone className="h-5 w-5" />
          </Button>
        </Link>
      </Card>

      <div className={cn("flex flex-row items-start justify-center gap-4 sm:gap-6 w-full mx-auto pt-2", (language === 'ur' || language === 'ar') && "font-arabic", language === 'hi' && "font-sans")}>
          <div className="flex flex-col items-center gap-1 text-center">
            <Button
              onClick={handleHazriLagwayen}
              aria-label={hazriLagwayenButtonText}
              className={cn(
                "rounded-full w-20 h-20 sm:w-24 sm:h-24 p-0 flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110 active:scale-95",
                "bg-green-700 hover:bg-green-800 dark:bg-green-600 dark:hover:bg-green-700 text-white",
                "shadow-xl hover:shadow-lg hover:shadow-green-500/50 dark:hover:shadow-green-400/40"
              )}
            >
              <CheckSquare className="h-8 w-8 sm:h-10 sm:w-10" />
            </Button>
            <p className={cn("text-sm text-muted-foreground mt-1", (language === 'ur' || language === 'ar') ? 'font-arabic' : 'font-sans')}>
              {hazriLagwayenButtonText}
            </p>
          </div>

          <div className="flex flex-col items-center gap-1 text-center">
            <a href="tel:+918937832941" aria-label={callingKhanqahButtonText}>
              <Button
                aria-label={callingKhanqahButtonText} 
                className={cn(
                  "rounded-full w-20 h-20 sm:w-24 sm:h-24 p-0 flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110 active:scale-95",
                  "bg-teal-600 hover:bg-teal-700 dark:bg-teal-500 dark:hover:bg-teal-600 text-white",
                  "shadow-xl hover:shadow-lg hover:shadow-teal-500/40 dark:hover:shadow-teal-400/30 "
                )}
              >
                <Phone className="h-8 w-8 sm:h-10 sm:w-10" />
              </Button>
            </a>
            <p className={cn("text-sm text-muted-foreground mt-1", (language === 'ur' || language === 'ar') ? 'font-arabic' : 'font-sans')}>
              {callingKhanqahButtonText}
            </p>
          </div>
        </div>


        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
            {localizedQuickLinks.map((link) => {
              const IconComponent = link.iconComponent;
              return (
                <motion.div
                  key={link.id}
                  whileHover={link.isHighlighted ? { y: -5, rotateY: 3 } : { y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Link href={link.href} passHref>
                    <Button
                      className={cn(
                          "w-full h-auto py-4 justify-start text-left flex items-center whitespace-normal transition-all duration-300 ease-in-out", // Added whitespace-normal
                          "bg-card/80 hover:bg-card border border-border/50",
                          "shadow-md hover:shadow-lg transform active:translate-y-0",
                           link.isHighlighted && "hover:bg-pink-500/10 dark:hover:bg-pink-400/10 hover:shadow-pink-500/30 dark:hover:shadow-pink-400/30",
                           "hover:-translate-y-1 active:translate-y-0.5" 
                      )}
                    >
                      <span className={cn((language === 'ur' || language === 'ar') ? "ml-3" : "mr-3")}>
                        <IconComponent className={cn("h-5 w-5", link.iconColorClass)} />
                      </span>
                      <span className={cn("text-sm sm:text-md font-medium text-foreground flex items-center flex-grow min-w-0", (language === 'ur' || language === 'ar') ? 'font-arabic text-right w-full' : 'font-sans text-left w-full')}> {/* Added min-w-0 */}
                        {link.title}
                        {link.id === 'muhasaba' && link.isHighlighted && 
                         <ClipboardList 
                           className={cn("h-4 w-4 text-pink-600 dark:text-pink-400", 
                                        (language === 'ur' || language === 'ar') ? "mr-auto ml-0" : "ml-auto mr-0" )} />}
                      </span>
                      <ArrowRight className={cn("h-4 w-4 text-muted-foreground", (language === 'ur' || language === 'ar') ? "mr-auto ml-0 transform rotate-180" : "ml-auto mr-0")}/>
                    </Button>
                  </Link>
                </motion.div>
              );
            })}
          </div>
    </div>
      <Link href="/tasbeeh" passHref>
        <Button
          variant="default"
          className={cn(
            "fixed bottom-6 right-6 z-40 rounded-full w-16 h-16 p-0 flex flex-col items-center justify-center shadow-2xl",
            "bg-gradient-to-br from-primary via-primary/80 to-accent text-primary-foreground",
            "hover:scale-110 hover:shadow-primary/50 focus:scale-110 focus:shadow-primary/50 transition-all duration-300 ease-out"
          )}
          aria-label={translations.tasbeeh?.[language] || "Tasbeeh Counter"}
        >
          <Repeat className="w-7 h-7" />
          <span className="sr-only">{translations.tasbeeh?.[language] || "Tasbeeh Counter"}</span>
        </Button>
      </Link>
    </>
  );
}

