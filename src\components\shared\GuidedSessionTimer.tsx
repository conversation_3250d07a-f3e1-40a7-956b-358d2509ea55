
"use client";

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Play, Pause, RotateCcw, TimerIcon, X } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface GuidedSessionTimerProps {
  durationInMinutes: number;
  onSessionEnd?: () => void;
  onClose?: () => void;
  prompts?: { inhale: string; exhale: string };
  practiceName?: string;
  autoStart?: boolean;
  buttonTexts?: {
    start: string;
    pause: string;
    resume: string;
    reset: string;
  };
}

const defaultButtonTexts = {
  start: "Start",
  pause: "Pause",
  resume: "Resume",
  reset: "Reset",
};

const GuidedSessionTimer: React.FC<GuidedSessionTimerProps> = ({
  durationInMinutes,
  onSessionEnd,
  onClose,
  prompts,
  practiceName,
  autoStart = false,
  buttonTexts: customButtonTexts,
}) => {
  const totalSeconds = durationInMinutes * 60;
  const [timeLeft, setTimeLeft] = useState(totalSeconds);
  const [isActive, setIsActive] = useState(autoStart);
  const [isPaused, setIsPaused] = useState(!autoStart);

  const buttonTexts = { ...defaultButtonTexts, ...customButtonTexts };

  useEffect(() => {
    if (autoStart && !isActive && timeLeft === totalSeconds) {
      setIsActive(true);
      setIsPaused(false);
    }
  }, [autoStart, isActive, timeLeft, totalSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && !isPaused && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false); // Stop the timer logically
      onSessionEnd?.();
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, isPaused, timeLeft, onSessionEnd]);

  const handleStartPauseResume = () => {
    if (!isActive && timeLeft === totalSeconds) { // Start fresh
      setIsActive(true);
      setIsPaused(false);
    } else if (!isActive && timeLeft > 0 && timeLeft < totalSeconds) { // Resume an unfinished session that was reset or hasn't completed
      setIsActive(true);
      setIsPaused(false);
    }
     else if (isActive && !isPaused) { // Pause
      setIsPaused(true);
    } else if (isActive && isPaused) { // Resume
      setIsPaused(false);
    } else if (timeLeft === 0) { // Restart completed session
      setTimeLeft(totalSeconds);
      setIsActive(true);
      setIsPaused(false);
    }
  };
  
  const handleReset = () => {
    setIsActive(autoStart); 
    setIsPaused(!autoStart); 
    setTimeLeft(totalSeconds);
    
    if (autoStart) {
      setIsActive(true);
      setIsPaused(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  };

  const progressPercentage = totalSeconds > 0 ? ((totalSeconds - timeLeft) / totalSeconds) * 100 : 0;

  let mainButtonText = buttonTexts.start;
  let mainButtonIcon = <Play className="h-5 w-5" />;
  let isMainButtonIconOnly = false;

  if (isActive && !isPaused) { // Paused state
    mainButtonText = buttonTexts.pause;
    mainButtonIcon = <Pause className="h-5 w-5" />;
    isMainButtonIconOnly = true;
  } else if (isActive && isPaused) { // Resume state
    mainButtonText = buttonTexts.resume;
    mainButtonIcon = <Play className="h-5 w-5" />;
    isMainButtonIconOnly = true;
  } else if (timeLeft === 0 && !isActive) { // Completed and ready to restart
    mainButtonText = buttonTexts.start; // Or could be a specific "Restart" text
    mainButtonIcon = <Play className="h-5 w-5 mr-2" />;
  } else { // Initial Start state
     mainButtonIcon = <Play className="h-5 w-5 mr-2" />;
  }


  const renderPrompt = (promptText: string | undefined, isExhale: boolean = false) => {
    if (!promptText) return null;
    const parts = promptText.split(': ');
    if (parts.length === 2) {
      return (
        <>
          {parts[0]}:{' '}
          <span className={cn(
            "font-semibold",
            isExhale ? "text-accent-foreground" : "text-primary"
          )}>
            {parts[1]}
          </span>
        </>
      );
    }
    return promptText;
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-xl my-6 glass-effect relative">
      {onClose && (
        <Button variant="ghost" size="icon" onClick={onClose} className="absolute top-2 right-2 text-muted-foreground hover:text-foreground z-10">
          <X className="h-5 w-5" />
          <span className="sr-only">Close Timer</span>
        </Button>
      )}
      <CardHeader className={cn("pt-8", !onClose && "pt-6")}>
        <CardTitle className="flex items-center justify-center text-primary">
          <TimerIcon className="h-6 w-6 mr-2" />
          {practiceName || "Guided Session"}
        </CardTitle>
      </CardHeader>
      <CardContent className="text-center space-y-6">
        <div className="text-6xl font-bold text-foreground tabular-nums my-4">
          {formatTime(timeLeft)}
        </div>
        
        <Progress value={progressPercentage} className="w-full h-2.5 rounded-full" />

        {prompts && prompts.inhale && prompts.exhale && (
           <div className={cn("text-lg min-h-[3rem] flex flex-col justify-center items-center", isActive && !isPaused ? "text-foreground" : "text-muted-foreground")}>
            {prompts.inhale && <p>{renderPrompt(prompts.inhale, false)}</p>}
            {prompts.exhale && <p>{renderPrompt(prompts.exhale, true)}</p>}
          </div>
        )}
        
        <div className="h-6">
            {!isActive && timeLeft === totalSeconds && !autoStart && (
                <p className="text-sm text-muted-foreground">Press {buttonTexts.start} to begin.</p>
            )}
            {timeLeft === 0 && isActive === false && ( 
                <p className="text-sm text-green-500 font-semibold">Session Complete!</p>
            )}
            {isActive && isPaused && (
                <p className="text-sm text-muted-foreground">Session Paused.</p>
            )}
        </div>

        <div className="flex justify-center gap-3 pt-2">
          <Button
            onClick={handleStartPauseResume}
            size={isMainButtonIconOnly ? "icon" : "lg"}
            className={cn(
              !isMainButtonIconOnly ? "flex-1" : "",
              isActive && !isPaused ? "bg-orange-500 hover:bg-orange-600" : "bg-green-600 hover:bg-green-700",
              "text-white"
            )}
            aria-label={mainButtonText}
          >
            {mainButtonIcon}
            {!isMainButtonIconOnly && mainButtonText}
          </Button>
          <Button
            onClick={handleReset}
            variant="outline"
            size="icon"
            aria-label={buttonTexts.reset}
          >
            <RotateCcw className="h-5 w-5" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default GuidedSessionTimer;

