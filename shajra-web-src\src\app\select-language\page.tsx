
"use client";

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useLanguage, type Language, availableLanguagesList } from '@/context/LanguageContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import MosqueSilhouetteIcon from '@/components/icons/MosqueSilhouetteIcon'; // Removed
import Image from 'next/image'; // Added
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function SelectLanguagePage() {
  const router = useRouter();
  const { language, setLanguage } = useLanguage();

  const handleLanguageSelect = (selectedLang: Language) => {
    setLanguage(selectedLang);
    localStorage.setItem('sabiriya-app-language', selectedLang); // Ensure it's saved

    const onboardingComplete = localStorage.getItem('sabiriyaOnboardingComplete');
    if (onboardingComplete === 'true') {
      router.replace('/home');
    } else {
      router.replace('/onboarding/1'); // Onboarding not done, start onboarding
    }
  };

  return (
    <Card className="w-full max-w-md shadow-xl glass-effect">
      <CardHeader className="text-center items-center">
        <Image
          src="/app-icon.png" // Path to your new image
          alt="Sabiriya App Icon"
          width={64} // Adjust as needed (e.g., 64 for w-16)
          height={64} // Adjust as needed (e.g., 64 for h-16)
          className="mb-4"
          data-ai-hint="app icon dome calligraphy"
        />
        <CardTitle className="text-2xl md:text-3xl">Select Your Language</CardTitle>
        <CardDescription>Choose your preferred language for the app.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {availableLanguagesList.map((lang) => (
          <Button
            key={lang.code}
            variant={language === lang.code ? "default" : "outline"}
            className={cn("w-full justify-between text-lg py-6", lang.disabled && "opacity-50 cursor-not-allowed")}
            onClick={() => !lang.disabled && handleLanguageSelect(lang.code)}
            disabled={lang.disabled}
          >
            <span>{lang.name} {lang.disabled ? "(Soon)" : ""}</span>
            {language === lang.code && <Check className="h-5 w-5" />}
          </Button>
        ))}
      </CardContent>
    </Card>
  );
}
