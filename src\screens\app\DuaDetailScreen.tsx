import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { duasData } from '@/data/duas';

export default function DuaDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { duaSlug } = route.params as { duaSlug: string };

  const dua = duasData.find(d => d.slug === duaSlug);

  if (!dua) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Dua not found</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.backText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#14b8a6" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>{dua.title}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <View style={styles.duaCard}>
          <Text style={styles.duaTitle}>{dua.title}</Text>
          
          <View style={styles.arabicContainer}>
            <Text style={styles.arabicText}>{dua.arabicText}</Text>
          </View>

          <View style={styles.translationContainer}>
            <Text style={styles.translationLabel}>Urdu Translation:</Text>
            <Text style={styles.translationText}>{dua.urduTranslation}</Text>
          </View>

          {dua.transliteration && (
            <View style={styles.transliterationContainer}>
              <Text style={styles.transliterationLabel}>Transliteration:</Text>
              <Text style={styles.transliterationText}>{dua.transliteration}</Text>
            </View>
          )}

          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsLabel}>Instructions:</Text>
            <Text style={styles.instructionsText}>{dua.instructions}</Text>
          </View>

          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsLabel}>Benefits:</Text>
            <Text style={styles.benefitsText}>{dua.benefits}</Text>
          </View>

          {dua.hasTasbeeh && (
            <TouchableOpacity style={styles.tasbeehButton}>
              <Ionicons name="repeat" size={20} color="white" />
              <Text style={styles.tasbeehButtonText}>Digital Tasbeeh</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  duaCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  duaTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 20,
    textAlign: 'center',
  },
  arabicContainer: {
    backgroundColor: '#f8fafc',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  arabicText: {
    fontSize: 18,
    lineHeight: 32,
    textAlign: 'right',
    color: '#1f2937',
  },
  translationContainer: {
    marginBottom: 16,
  },
  translationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#14b8a6',
    marginBottom: 8,
  },
  translationText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
    textAlign: 'right',
  },
  transliterationContainer: {
    marginBottom: 16,
  },
  transliterationLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#14b8a6',
    marginBottom: 8,
  },
  transliterationText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
    fontStyle: 'italic',
  },
  instructionsContainer: {
    marginBottom: 16,
  },
  instructionsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#14b8a6',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
  },
  benefitsContainer: {
    marginBottom: 20,
  },
  benefitsLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#14b8a6',
    marginBottom: 8,
  },
  benefitsText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
  },
  tasbeehButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#14b8a6',
    padding: 16,
    borderRadius: 12,
  },
  tasbeehButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    color: '#ef4444',
    marginBottom: 16,
  },
  backText: {
    fontSize: 16,
    color: '#14b8a6',
    fontWeight: '600',
  },
});
