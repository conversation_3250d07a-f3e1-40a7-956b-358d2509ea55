'use server';
/**
 * @fileOverview Provides a daily spiritual insight.
 *
 * - getDailySpiritualInsight - A function that returns a daily spiritual insight.
 * - DailySpiritualInsightOutput - The return type for the getDailySpiritualInsight function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

// No input needed for this flow
const DailySpiritualInsightInputSchema = z.object({});
export type DailySpiritualInsightInput = z.infer<typeof DailySpiritualInsightInputSchema>;


const DailySpiritualInsightOutputSchema = z.object({
  insight: z.string().describe('A short, uplifting spiritual insight or reflection related to Sufi teachings.'),
});
export type DailySpiritualInsightOutput = z.infer<typeof DailySpiritualInsightOutputSchema>;

export async function getDailySpiritualInsight(): Promise<DailySpiritualInsightOutput> {
  return dailyInsightFlow({}); // Pass empty object for no input
}

const prompt = ai.definePrompt({
  name: 'dailyInsightPrompt',
  input: { schema: DailySpiritualInsightInputSchema },
  output: { schema: DailySpiritualInsightOutputSchema },
  prompt: `Generate a short, uplifting spiritual insight or reflection (around 1-2 sentences) related to Sufi teachings. Focus on topics like patience, gratitude, remembrance of God (Zikr), love, humility, or the journey of the soul. The insight should be inspiring and easy to understand.`,
});

const dailyInsightFlow = ai.defineFlow(
  {
    name: 'dailyInsightFlow',
    inputSchema: DailySpiritualInsightInputSchema,
    outputSchema: DailySpiritualInsightOutputSchema,
  },
  async (_input) => {
    const {output} = await prompt({});
    if (!output) {
        throw new Error("Failed to generate daily insight.");
    }
    return output;
  }
);
