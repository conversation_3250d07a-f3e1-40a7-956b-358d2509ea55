
import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type FontSize = 'sm' | 'base' | 'lg';

interface FontSizeContextType {
  fontSize: FontSize;
  setFontSize: (fontSize: FontSize) => void;
}

const FONT_SIZE_STORAGE_KEY = 'sabiriya-app-font-size';
const DEFAULT_FONT_SIZE: FontSize = 'base';

const FontSizeContext = createContext<FontSizeContextType | undefined>(undefined);

export const FontSizeProvider = ({ children }: { children: ReactNode }) => {
  const [fontSize, _setFontSize] = useState<FontSize>(DEFAULT_FONT_SIZE);

  const setFontSize = useCallback(async (newSize: FontSize) => {
    _setFontSize(newSize);
    try {
      await AsyncStorage.setItem(FONT_SIZE_STORAGE_KEY, newSize);
    } catch (error) {
      console.error('Error saving font size to AsyncStorage:', error);
    }
  }, []);

  useEffect(() => {
    // Load font size from AsyncStorage on app start
    const loadFontSize = async () => {
      try {
        const storedFontSize = await AsyncStorage.getItem(FONT_SIZE_STORAGE_KEY) as FontSize | null;
        if (storedFontSize && ['sm', 'base', 'lg'].includes(storedFontSize)) {
          _setFontSize(storedFontSize);
        }
      } catch (error) {
        console.error('Error loading font size from AsyncStorage:', error);
      }
    };

    loadFontSize();
  }, []);

  return (
    <FontSizeContext.Provider value={{ fontSize, setFontSize }}>
      {children}
    </FontSizeContext.Provider>
  );
};

export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  if (context === undefined) {
    throw new Error('useFontSize must be used within a FontSizeProvider');
  }
  return context;
};
