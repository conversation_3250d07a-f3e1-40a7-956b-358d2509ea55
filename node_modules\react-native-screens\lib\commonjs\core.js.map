{"version": 3, "names": ["_reactNative", "require", "shouldUseActivityState", "exports", "isNativePlatformSupported", "Platform", "OS", "ENABLE_SCREENS", "enableScreens", "shouldEnableScreens", "arguments", "length", "undefined", "UIManager", "getViewManagerConfig", "console", "error", "ENABLE_FREEZE", "enableFreeze", "shouldEnableReactFreeze", "screensEnabled", "freezeEnabled"], "sourceRoot": "../../src", "sources": ["core.ts"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA;AACO,MAAMC,sBAAsB,GAAAC,OAAA,CAAAD,sBAAA,GAAG,IAAI;AAEnC,MAAME,yBAAyB,GAAAD,OAAA,CAAAC,yBAAA,GACpCC,qBAAQ,CAACC,EAAE,KAAK,KAAK,IACrBD,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACC,EAAE,KAAK,SAAS;AAE3B,IAAIC,cAAc,GAAGH,yBAAyB;AAEvC,SAASI,aAAaA,CAAA,EAA6B;EAAA,IAA5BC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACtDH,cAAc,GAAGE,mBAAmB;EAEpC,IAAI,CAACL,yBAAyB,EAAE;IAC9B;EACF;EAEA,IAAIG,cAAc,IAAI,CAACM,sBAAS,CAACC,oBAAoB,CAAC,WAAW,CAAC,EAAE;IAClEC,OAAO,CAACC,KAAK,CACV,wGACH,CAAC;EACH;AACF;AAEA,IAAIC,aAAa,GAAG,KAAK;AAElB,SAASC,YAAYA,CAAA,EAAiC;EAAA,IAAhCC,uBAAuB,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACzD,IAAI,CAACN,yBAAyB,EAAE;IAC9B;EACF;EAEAa,aAAa,GAAGE,uBAAuB;AACzC;AAEO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOb,cAAc;AACvB;AAEO,SAASc,aAAaA,CAAA,EAAG;EAC9B,OAAOJ,aAAa;AACtB"}