
"use client";

import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Lightbulb, Info, ListChecks, ShieldCheck, BookText, Repeat, ArrowRight } from 'lucide-react'; 
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';
import { useLanguage, type Language } from '@/context/LanguageContext';
import React from 'react';

// Shared Arabic Recitations
const durood_ahlulbayt_ar = "اَللّٰھُمَّ صَلِّ عَلٰی سَیِّدِنَا وَ مَوْلَانَا مُحَمَّدٍ وَّ عَلٰی سَیِّدِنَا عَلِیٍّ وَّ عَلٰی سَیِّدَتِنَا فَاطِمَةَ وَ عَلٰی سَیِّدَتِنَا زَیْنَبَ وَ عَلٰی سَیِّدِنَا حَسَنٍ وَّ عَلٰی سَیِّدِنَا حُسَیْنٍ وَّ عَلٰی اٰلِہٖ وَ صَحْبِہٖ وَ بَارِکْ وَ سَلِّمْ۔";
const dua_e_noori_ar = "اَللّٰھُمَّ اجْعَلْ لِّیْ نُوْرًا فِیْ قَلْبِیْ، وَ نُوْرًا فِیْ قَبْرِیْ، وَ نُوْرًا فِیْ سَمْعِیْ، وَ نُوْرًا فِیْ بَصَرِیْ، وَ نُوْرًا فِیْ شَعْرِیْ، وَ نُوْرًا فِیْ بَشَرِیْ، وَ نُوْرًا فِیْ لَحْمِیْ، وَ نُوْرًا فِیْ دَمِیْ، وَ نُوْرًا فِیْ عِظَامِیْ، وَ نُوْرًا مِّنْۢ بَیْنِ یَدَیَّ، وَ نُوْرًا مِّنْ خَلْفِیْ، وَ نُوْرًا عَنْ یَّمِیْنِیْ، وَ نُوْرًا عَنْ شِمَالِیْ، وَ نُوْرًا مِّنْ فَوْقِیْ، وَ نُوْرًا مِّنْ تَحْتِیْ، وَاجْعَلْنِیْ نُوْرًا، وَ سَلِّمْنِیْ حَقًّا۔";
const zikrWords_ar = "يَا هُوَ، يَا مَنْ هُوَ، يَا مَنْ لَيْسَ لَهُ إِلَّا هُوَ، حَقّ، حَقّ، حَقّ";

// Roman Urdu Transliterations
const durood_ahlulbayt_ro = "Allaahumma Salli 'Alaa Sayyidinaa Wa Maulaanaa Muhammadinw Wa 'Alaa Sayyidinaa 'Aliyyinw Wa 'Alaa Sayyidatinaa Faatimata Wa 'Alaa Sayyidatinaa Zainaba Wa 'Alaa Sayyidinaa Hasaninw Wa 'Alaa Sayyidinaa Husaininw Wa 'Alaa Aalihee Wa Sahbihee Wa Baarik Wa Sallim.";
const dua_e_noori_ro = "Allaahummaj'al Lee Nooran Fee Qalbee, Wa Nooran Fee Qabree, Wa Nooran Fee Sam'ee, Wa Nooran Fee Basaree, Wa Nooran Fee Sha'ree, Wa Nooran Fee Basharee, Wa Nooran Fee Lahmee, Wa Nooran Fee Damee, Wa Nooran Fee 'Izaamee, Wa Nooran Mim Bain Yadayya, Wa Nooran Min Khalfee, Wa Nooran 'An Yameenee, Wa Nooran 'An Shimaalee, Wa Nooran Min Fawqee, Wa Nooran Min Tahtee, Waj'alnee Nooraa, Wa Sallimnee Haqqaa.";
const muraqaba_zikr_words_ro_transliteration = "Ya Hu, Ya Man Hu, Ya Man Laisa Lahu Illa Hu, Haqq, Haqq, Haqq";


// Hindi Transliterations
const durood_ahlulbayt_hi = "अल्लाहुम्म सल्लि 'अला सय्यिदिना व मौलाना मुहम्मदिन व 'अला सय्यिदिना 'अलिय्यिन व 'अला सय्यिदतिना फ़ातिमा व 'अला सय्यिदतिना ज़ैनब व 'अला सय्यिदिना हसनिन व 'अला सय्यिदिना हुसैनिन व 'अला आलिही व सह्बिही व बारिक व सल्लिम।";
const dua_e_noori_hi = "अल्लाहुम्मज-अल ली नूरन फ़ी क़ल्बी, व नूरन फ़ी क़बरी, व नूरन फ़ी समई, व नूरन फ़ी बसरी, व नूरन फ़ी शअरी, व नूरन फ़ी बशरी, व नूरन फ़ी लह्मी, व नूरन फ़ी दमी, व नूरन फ़ी इज़ामी, व नूरन मिम-बैनि यदय-य, व नूरन मिन ख़लफ़ी, व नूरन अन यमीनी, व नूरन अन शिमाली, व नूरन मिन फ़ौक़ी, व नूरन मिन तहती, वज-अलनी नूरन, व सल्लिमनी हक़्क़न।";
const muraqaba_zikr_words_hi_transliteration = "या हू, या मन हू, या मन लैसा लहू इल्ला हू, हक़्क़, हक़्क़, हक़्क़"; // Corrected


// Translations and Transliterations
const translations = {
  pageTitle: {
    en: "Shaghl-e-Noori (The Practice of Light)", ur: "شغلِ نوری", ro: "Shaghl-e-Noori", hi: "शग़्ल-ए-नूरी", ar: "الشغل النوري",
  },
  pageSubtitle: {
    en: "A Spiritual Exercise for Inner Light", ur: "باطنی نور کا ایک روحانی عمل", ro: "Baatini noor ka ek roohaani amal", hi: "बातिनी नूर का एक रूहानी अमल", ar: "تمرين روحي للنور الباطني",
  },
  backToPractices: {
    en: "Back to Practices", ur: "روحانی اعمال کی طرف واپس", ro: "Practices ki Taraf Wapas", hi: "अभ्यासों पर वापस", ar: "العودة إلى الممارسات الروحية",
  },
  tabs: {
    introduction: { en: "Introduction", ur: "تعارف", ro: "Ta'aruf", hi: "तआरुफ़", ar: "مقدمة" },
    method: { en: "Method", ur: "طریقہ", ro: "Tareeqa", hi: "तरीक़ा", ar: "الطريقة" },
    benefits: { en: "Benefits & Guidance", ur: "فوائد و ہدایات", ro: "Fawaid o Hidayat", hi: "फ़वाइद ओ हिदायत", ar: "الفوائد والإرشادات" },
  },
  introCard: {
    title: { en: "Introduction", ur: "تعارف", ro: "Ta'aruf", hi: "तआरुफ़", ar: "مقدمة" },
    description: { en: "An overview of Shaghl-e-Noori and its significance.", ur: "شغلِ نوری کا ایک تعارف اور اس کی اہمیت۔", ro: "Shaghl-e-Noori ka ek ta'aruf aur iski ahmiyat.", hi: "शग़्ल-ए-नूरी का एक तआरुफ़ और इसकी अहमियत।", ar: "نظرة عامة على الشغل النوري وأهميته." },
    mainText: {
      en: "O apple of my eye! To illuminate the inner self (Baatin) and to fill the heart (Qalb) with Divine Lights (Anwaar-e-Ilaahi), there exist many subtle and pure spiritual exercises (Ashghaal) and practices (A'maal). \"Shaghl\" refers to concentrating all one's attention and thought upon a single point so completely that no other thought remains. These practices are sometimes manual (performed by hand), sometimes linguistic (repetition of specific Zikr with the tongue), sometimes auditory (focusing hearing on a particular sound), and sometimes visual or imaginative (performed with the power of sight or imagination). The \"Shaghl-e-Noori\" we discuss today belongs to this visual and imaginative category and is an exceedingly potent practice. In it, the seeker (Saalik) firmly establishes in their heart and inner being the visualization that their entire existence, every limb, even their blood, flesh, and bones, have all transformed into Divine Light (Noor-e-Ilaahi), and that light surrounds them everywhere.",
      ur: "اے میرے نورِ نظر!   باطن کو منور کرنے اور قلب کو انوارِ الٰہی سے معمور کرنے کے لیے کئی لطیف و پاکیزہ اشغال و اعمال موجود ہیں۔ \"شغل\" سے مراد ہے کسی ایک مرکز پر اپنی تمام تر توجہ اور فکر کو یوں مجتمع کر لینا کہ غیر کا خیال تک نہ رہے۔ یہ اشغال کبھی دستی، یعنی ہاتھ کے عمل سے، کبھی لسانی، یعنی زبان سے مخصوص اذکار کی تکرار سے، کبھی سمعی، یعنی سماعت کو کسی خاص صدا پر مرکوز کرنے سے، اور کبھی نظری یا بصری، یعنی نگاہ یا تصور کی قوت سے انجام پاتے ہیں۔ آج ہم جس \"شغلِ نوری\" کا ذکر کر رہے ہیں، اس کا تعلق اسی بصری و نظری قسم سے ہے، اور یہ ایک نہایت ہی پُر تاثیر عمل ہے۔ اس میں سالک اپنے قلب و باطن میں یہ تصور پوری قوت سے قائم کرتا ہے کہ اس کا سارا وجود، اس کا ہر عضو، حتیٰ کہ خون، گوشت اور ہڈیوں تک، سب نورِ الٰہی میں ڈھل چکے ہیں اور اس کے گرد و پیش بھی نور ہی نور پھیلا ہوا ہے۔",
      ro: "Ae mere noor-e-nazar! Baatin ko munawwar karne aur qalb ko anwaar-e-ilaahi se ma'moor karne ke liye kai lateef o pakeezah ashghaal o a'maal maujood hain. \"Shaghl\" se muraad hai kisi ek markaz par apni tamaam tar tawajjuh aur fikr ko yoon mujtama kar lena keh ghair ka khayaal tak na rahe. Yeh ashghaal kabhi dasti, ya'ni haath ke amal se, kabhi lisaani, ya'ni zabaan se makhsoos azkaar ki takraar se, kabhi sama'ee, ya'ni sama'at ko kisi khaas sada par markooz karne se, aur kabhi nazari ya basari, ya'ni nigaah ya tasawwur ki quwwat se anjaam paate hain. Aaj hum jis \"Shaghl-e-Noori\" ka zikr kar rahe hain, is ka ta'alluq isi basari o nazari qism se hai, aur yeh ek nihaayat hi pur-taseer amal hai. Is mein saalik apne qalb o baatin mein yeh tasawwur poori quwwat se qaaim karta hai keh uska saara wujood, uska har uzv, hatta keh khoon, gosht aur haddiyon tak, sab noor-e-ilaahi mein dhal chuke hain aur uske gird o pesh bhi noor hi noor phaila hua hai.",
      hi: "ऐ मेरे नूर-ए-नज़र! बातिन को मुनव्वर करने और क़ल्ब को अनवार-ए-इलाही से मामूर करने के लिए कई लतीफ़ ओ पाकीज़ा अशग़ाल ओ आमाल मौजूद हैं। \"शग़्ल\" से मुराद है किसी एक मरकज़ पर अपनी तमाम तर तवज्जोह और फ़िक्र को यूँ मुज्तमअ कर लेना कि ग़ैर का ख़याल तक न रहे। ये अशग़ाल कभी दस्ती, यानी हाथ के अमल से, कभी लिसानी, यानी ज़बान से मख़सूस अज़कार की तकरार से, कभी समई, यानी समाअत को किसी ख़ास सदा पर मरकूज़ करने से, और कभी नज़री या बसरी, यानी निगाह या तसव्वुर की क़ुव्वत से अंजाम पाते हैं। आज हम जिस \"शग़्ल-ए-नूरी\" का ज़िक्र कर रहे हैं, उसका ताल्लुक़ इसी बसरी ओ नज़री क़िस्म से है, और यह एक निहायत ही पुर-तासीर अमल है। इसमें सालिक अपने क़ल्ब ओ बातिन में यह तसव्वुर पूरी क़ुव्वत से क़ायम करता है कि उसका सारा वजूद, उसका हर उज़्व, हत्ता कि ख़ून, गोश्त और हड्डियों तक, सब नूर-ए-इलाही में ढल चुके हैं और उसके गिर्द-ओ-पेश भी नूर ही नूर फैला हुआ है।",
      ar: "يا قرة عيني! لتنوير الباطن وملء القلب بالأنوار الإلهية، توجد العديد من الأشغال والأعمال اللطيفة والطاهرة. \"الشغل\" يعني تركيز كل الانتباه والفكر على مركز واحد بحيث لا يبقى أي مجال لفكر آخر. هذه الأشغال تكون أحيانًا يدوية (باليد)، وأحيانًا لسانية (بتكرار أذكار معينة باللسان)، وأحيانًا سمعية (بتركيز السمع على صوت معين)، وأحيانًا نظرية أو بصرية (بقوة النظر أو التصور). \"الشغل النوري\" الذي نذكره اليوم ينتمي إلى هذا النوع البصري والنظري، وهو عمل مؤثر للغاية. فيه يثبت السالك في قلبه وباطنه بقوة تصور أن كيانه كله، كل عضو فيه، حتى دمه ولحمه وعظامه، قد تحولت كلها إلى نور إلهي، وأن النور يحيط به من كل جانب.",
    },
    taharatGuidance: {
      en: "O my friend! When you intend to adopt this Shaghl-e-Noori, first ensure thorough outward (Zaahiri) and inward (Baatini) purity (Tahaarat). After cleansing yourself, perform ablution (Wudu), sit in a calm and pure corner, and strive to purify your heart from all sins, evil thoughts, and worldly defilements, for the effulgence of Divine Light always descends upon pure hearts. It is also essential that before starting this blessed practice, you obtain formal permission from your perfect spiritual guide (Murshid-e-Kaamil), as their prayers and spiritual attention will prove to be the best protector and guide for you on this path.",
      ur: "اے میرے دوست! جب تم اس شغلِ نوری کو اپنانے کا ارادہ کرو تو پہلے ظاہری و باطنی طہارت کا خوب اہتمام کرنا۔ پاک صاف ہو کر، باوضو، کسی پرسکون اور پاکیزہ گوشے میں بیٹھنا اور اپنے دل کو ہر طرح کے گناہوں، برے خیالات اور دنیوی آلائشوں سے پاک کرنے کی کوشش کرنا، کیونکہ نور کی تجلی ہمیشہ پاکیزہ قلوب پر ہی ہوتی ہے۔ اور یہ بھی لازم ہے کہ اس مبارک شغل کو شروع کرنے سے پہلے اپنے مرشدِ کامل سے باقاعدہ اجازت حاصل کر لو، کہ ان کی دعا اور روحانی توجہ تمہارے لیے اس راہ میں بہترین محافظ و رہنما ثابت ہوگی۔",
      ro: "Ae mere dost! Jab tum is Shaghl-e-Noori ko apnaane ka iraada karo toh pehle zaahiri o baatini tahaarat ka khoob ehtemaam karna. Paak saaf ho kar, ba-wuzu, kisi pur-sukoon aur pakeezah goshe mein baithna aur apne dil ko har tarah ke gunaahon, bure khayalaat aur duniyavi aalaaishon se paak karne ki koshish karna, kyunkeh noor ki tajalli hamesha pakeezah quloob par hi hoti hai. Aur yeh bhi laazim hai keh is mubarak shaghl ko shuru karne se pehle apne murshid-e-kaamil se ba-qaa'ida ijaazat haasil kar lo, keh unki dua aur roohaani tawajjuh tumhaare liye is raah mein behtareen muhaafiz o rahnuma saabit hogi.",
      hi: "ऐ मेरे दोस्त! जब तुम इस शग़्ल-ए-नूरी को अपनाने का इरादा करो तो पहले ज़ाहिरी ओ बातिनी तहारत का ख़ूब एहतेमाम करना। पाक साफ़ होकर, बा-वज़ू, किसी पुरसुकून और पाकीज़ा गोशे में बैठना और अपने दिल को हर तरह के गुनाहों, बुरे ख़यालात और दुनियावी आलाइशों से पाक करने की कोशिश करना, क्यूंकि नूर की तजल्ली हमेशा पाकीज़ा क़ुलूब पर ही होती है। और यह भी लाज़िम है कि इस मुबारक शग़्ल को शुरू करने से पहले अपने मुर्शिद-ए-कामिल से बाक़ायदा इजाज़त हासिल कर लो, कि उनकी दुआ और रूहानी तवज्जोह तुम्हारे लिए इस राह में बेहतरीन मुहाफ़िज़ ओ रहनुमा साबित होगी।",
      ar: "يا صديقي! عندما تنوي اعتماد هذا الشغل النوري، فاحرص أولاً على الطهارة الظاهرية والباطنية الكاملة. تطهر، توضأ، واجلس في زاوية هادئة ونقية، واجتهد في تطهير قلبك من كل الذنوب والأفكار السيئة والشوائب الدنيوية، فإن تجلي النور الإلهي لا يكون إلا على القلوب الطاهرة. ومن الضروري أيضًا أن تحصل على إذن رسمي من مرشدك الكامل قبل البدء في هذا الشغل المبارك، فإن دعاءه وتوجهه الروحي سيكونان لك خير حافظ ومرشد في هذا الطريق.",
    },
  },
  methodCard: {
    title: { en: "Method", ur: "طریقہ", ro: "Tareeqa", hi: "तरीक़ा", ar: "الطريقة" },
    description: { en: "Detailed instructions for performing Shaghl-e-Noori.", ur: "شغلِ نوری ادا کرنے کی تفصیلی ہدایات۔", ro: "Shaghl-e-Noori ada karne ki tafseeli hidayaat.", hi: "शग़्ल-ए-नूरी अदा करने की तफ़सीली हिदायात।", ar: "تعليمات مفصلة لأداء الشغل النوري." },
    step1Heading: { en: "1. Starting Durood", ur: "۱۔ ابتدائی درود", ro: "1. Ibtidai Durood", hi: "१. इब्तिदाई दरूद", ar: "١. الدرود الابتدائي" },
    step1Text: {
      en: "Begin the Shaghl with utmost respect and love by reciting Durood-e-Ahl-e-Bait-e-Athaar (Salutations upon the Purified Household of the Prophet ﷺ).",
      ur: "شغل کا آغاز نہایت ادب و محبت سے درودِ اہلِ بیتِ اطہار (علیہم السلام) سے کرنا۔",
      ro: "Shaghl ka aaghaaz nihaayat adab o mohabbat se Durood-e-Ahl-e-Bait-e-Athaar (Alaihimus Salaam) se karna.",
      hi: "शग़्ल का आग़ाज़ निहायत अदब ओ मोहब्बत से दरूद-ए-अहल-ए-बैत-ए-अतहार (अलैहिमुस सलाम) से करना।",
      ar: "ابدأ الشغل بأقصى درجات الأدب والمحبة بتلاوة درود أهل البيت الأطهار (عليهم السلام).",
    },
    step2Heading: { en: "2. Dua-e-Noori (Prayer of Light)", ur: "۲۔ دعائے نوری", ro: "2. Dua-e-Noori", hi: "२. दुआ-ए-नूरी", ar: "٢. دعاء النور" },
    step2Text: {
      en: "Next, recite the following Dua-e-Noori, contemplating its meanings and with firm conviction that by the blessings of these sacred words, your entire being is becoming luminous:",
      ur: "اس کے بعد، درج ذیل دعائے نوری کو اس کے معانی و مفہوم کا تصور کرتے ہوئے، اور اس کامل یقین کے ساتھ پڑھنا کہ ان مقدس کلمات کی برکت سے تمہارا انگ انگ نورانی ہو رہا ہے:",
      ro: "Is ke ba'd, darj zail Dua-e-Noori ko iske ma'aani o mafhoom ka tasawwur karte hue, aur is kaamil yaqeen ke saath parhna keh in muqaddas kalimaat ki barkat se tumhaara ang ang nooraani ho raha hai:",
      hi: "इसके बाद, दर्ज ज़ैल दुआ-ए-नूरी को इसके मआनी ओ मफ़हूम का तसव्वुर करते हुए, और इस कामिल यक़ीन के साथ पढ़ना कि इन मुक़द्दस कलिमात की बरकत से तुम्हारा अंग-अंग नूरानी हो रहा है:",
      ar: "بعد ذلك، اقرأ دعاء النور التالي، متأملاً معانيه ومفاهيمه، وبإيمان راسخ بأن كيانك كله يتنور ببركة هذه الكلمات المقدسة:",
    },
    duaNooriTranslation: {
      en: "(Translation: O Allah! Make for me light in my heart, and light in my grave, and light in my hearing, and light in my sight, and light in my hair, and light in my skin, and light in my flesh, and light in my blood, and light in my bones, and light before me, and light behind me, and light on my right, and light on my left, and light above me, and light beneath me, and make me light, and grant me safety in truth.)",
      ur: "(ترجمہ: اے اللہ! میرے دل، میری قبر، میرے کانوں، میری آنکھوں، میرے بالوں، میری جلد، میرے گوشت، میرے خون اور میری ہڈیوں میں نور پیدا فرما۔ میرے آگے، پیچھے، دائیں، بائیں، اوپر اور نیچے نور پیدا فرما، اور مجھے سراپا نور بنا دے، اور مجھے سچائی کے ساتھ سلامتی عطا فرما۔)",
      ro: "(Tarjuma: Ae Allah! Mere dil, meri qabr, mere kaanon, meri aankhon, mere baalon, meri jild, mere gosht, mere khoon aur meri haddiyon mein noor paida farma. Mere aage, peechhe, daayein, baayein, oopar aur neeche noor paida farma, aur mujhe saraapa noor bana de, aur mujhe sachchaai ke saath salaamati ataa farma.)",
      hi: "(तर्जुमा: ऐ अल्लाह! मेरे दिल, मेरी क़ब्र, मेरे कानों, मेरी आँखों, मेरे बालों, मेरी जिल्द, मेरे गोश्त, मेरे ख़ून और मेरी हड्डियों में नूर पैदा फ़रमा। मेरे आगे, पीछे, दाएं, बाएं, ऊपर और नीचे नूर पैदा फ़रमा, और मुझे सरापा नूर बना दे, और मुझे सच्चाई के साथ सलामती अता फ़रमा।)",
      ar: "(الترجمة: اللهم اجعل لي نورًا في قلبي، ونورًا في قبري، ونورًا في سمعي، ونورًا في بصري، ونورًا في شعري، ونورًا في بشري، ونورًا في لحمي، ونورًا في دمي، ونورًا في عظامي، ونورًا من بين يدي، ونورًا من خلفي، ونورًا عن يميني، ونورًا عن شمالي، ونورًا من فوقي، ونورًا من تحتي، واجعلني نورًا، وسلمني حقًا.)",
    },
    step3Heading: { en: "3. Muraqaba (Meditation) and Zikr", ur: "۳۔ مراقبہ اور ذکر", ro: "3. Muraqaba aur Zikr", hi: "३. मुराक़बा और ज़िक्र", ar: "٣. المراقبة والذكر" },
    step3Intro: {
      en: "Now, proceed to the main Muraqaba. Sit in a cross-legged posture (Chaar Zaanu) and repeat these words:",
      ur: "اب اصل مراقبے کی جانب آؤ۔ چار زانو ہو کر بیٹھ کر ان کلمات کا ورد کرو:",
      ro: "Ab asl muraqbe ki jaanib aao. Chaar zaanu ho kar baith kar in kalimaat ka wird karo:",
      hi: "अब असल मुराक़बे की जानिब आओ। चार ज़ानू होकर बैठकर इन कलिमात का विर्द करो:",
      ar: "الآن، انتقل إلى المراقبة الرئيسية. اجلس متربعًا (چار زانو) وكرر هذه الكلمات:",
    },
    step3Method: {
      en: `The method is as follows: While saying "Yaa Hoo", move your head in a semi-circle from your right knee to your left knee, and then while saying "Yaa Man Hoo", move your head from your left knee to your right knee. After this, bring the movement of "Yaa Man" to your left knee, then turn your attention towards your right shoulder and, with "Laisa Lahoo", completely negate all kinds of satanic whisperings (thoughts of sin), carnal dangers (worldly pleasures), and angelic pride (pride in worship) from your inner self. Then, draw out "Illaa" up to the Ummud-Dimāgh (the highest spiritual center of the brain). Thereafter, with full spiritual force and absorption, immersed in the love and gnosis of Allah Almighty, strike "Hoo" upon your heart in such a way that every trace of 'other-than-Allah' (Ghairullah) is erased. Following this, in the same state, strike "Haqq" three times successively upon your heart.`,
      ur: "اس کا طریقہ یہ ہے کہ \"یَا ھُوْ\" کہتے ہوئے سر کو دائیں زانو سے بائیں زانو تک، اور پھر \"یَا مَنْ ھُوْ\" کہتے ہوئے بائیں زانو سے دائیں زانو تک نیم دائرے میں حرکت دو۔ اس کے بعد \"یَا مَنْ\" کی گردش بائیں زانو تک لے جا کر، دائیں کندھے کی طرف متوجہ ہوتے ہوئے \"لَیْسَ لَہٗ\" کے ذریعے اپنے باطن سے ہر قسم کے شیطانی وسوسوں (گناہ کے خیالات)، نفسانی خطرات (دنیاوی لذتوں) اور ملکی اندیشوں (عبادت پر غرور) کی پوری طرح نفی کر دو۔ پھر \"اِلَّا\" کو کھینچ کر اُم الدماغ (یعنی دماغ کے سب سے اعلیٰ روحانی مرکز) تک لے جاؤ، اور پھر پوری روحانی قوت اور استغراق کے ساتھ، اللہ تعالیٰ کی محبت و معرفت میں ڈوب کر \"ھُوْ\" کی ضرب اپنے دل پر یوں لگاؤ کہ غیر اللہ کا ہر نقش مٹ جائے۔ اس کے بعد، اسی کیفیت میں \"حَقْ\" کی تین ضربیں پے در پے اپنے دل پر لگاؤ۔",
      ro: "Is ka tareeqa yeh hai keh \"Yaa Hoo\" kehte hue sar ko daayein zaanu se baayein zaanu tak, aur phir \"Yaa Man Hoo\" kehte hue baayein zaanu se daayein zaanu tak neem daa'ire mein harkat do. Is ke ba'd \"Yaa Man\" ki gardish baayein zaanu tak le jaa kar, daayein kandhe ki taraf mutawajjeh hote hue \"Laisa Lahoo\" ke zariye apne baatin se har qism ke shaitaani waswason (gunaah ke khayalaat), nafsaani khatraat (duniyavi lazzaton) aur mulki andeshon (ibaadat par ghuroor) ki poori tarah nafi kar do. Phir \"Illaa\" ko kheench kar Ummud Dimaagh (ya'ni dimaagh ke sab se a'laa roohaani markaz) tak le jaao, aur phir poori roohaani quwwat aur istighraaq ke saath, Allah Ta'ala ki mohabbat o ma'rifat mein doob kar \"Hoo\" ki zarb apne dil par yoon lagaao keh ghair Allah ka har naqsh mit jaaye. Is ke ba'd, isi kaifiyat mein \"Haqq\" ki teen zarbein pay dar pay apne dil par lagaao.",
      hi: "इसका तरीक़ा यह है कि \"या हू\" कहते हुए सर को दाएं ज़ानू से बाएं ज़ानू तक, और फिर \"या मन हू\" कहते हुए बाएं ज़ानू से दाएं ज़ानू तक नीम दायरे में हरकत दो। इसके बाद \"या मन\" की गर्दिश बाएं ज़ानू तक ले जाकर, दाएं कंधे की तरफ़ मुतवज्जेह होते हुए \"लैसा लहू\" के ज़रिए अपने बातिन से हर क़िस्म के शैतानी वसवलों (गुनाह के ख़यालात), नफ़्सानी ख़तरात (दुनियावी लज़्ज़तों) और मलकी अंदेशों (इबादत पर ग़ुरूर) की पूरी तरह नफ़ी कर दो। फिर \"इल्ला\" को खींचकर उम्मुद-दिमाग़ (यानी दिमाग़ के सबसे आला रूहानी मरकज़) तक ले जाओ, और फिर पूरी रूहानी क़ुव्वत और इस्तिग़राक़ के साथ, अल्लाह तआला की मोहब्बत ओ मारिफ़त में डूबकर \"हू\" की ज़र्ब अपने दिल पर यूँ लगाओ कि ग़ैर अल्लाह का हर नक़्श मिट जाए। इसके बाद, इसी कैफ़ियत में \"हक़्क़\" की तीन ज़र्बें पय दर पय अपने दिल पर लगाओ।",
      ar: "الطريقة هي كالتالي: أثناء قول \"يَا ھُوْ\"، حرِّك رأسك في نصف دائرة من ركبتك اليمنى إلى ركبتك اليسرى، ثم أثناء قول \"يَا مَنْ ھُوْ\"، حرِّك رأسك من ركبتك اليسرى إلى ركبتك اليمنى. بعد ذلك، اجلب حركة \"يَا مَنْ\" إلى ركبتك اليسرى، ثم وجِّه انتباهك نحو كتفك الأيمن، وبـ \"لَيْسَ لَهُ\"، انفِ تمامًا كل أنواع الوساوس الشيطانية (أفكار الخطيئة)، والمخاطر النفسية (الملذات الدنيوية)، والكبر الملائكي (الكبر في العبادة) من باطنك. ثم، مد \"إِلَّا\" حتى تصل إلى أم الدماغ (أعلى مركز روحي في الدماغ). بعد ذلك، بكامل القوة الروحية والاستغراق، مغمورًا في محبة الله ومعرفته، اضرب \"ھُوْ\" على قلبك بطريقة يُمحى بها كل أثر لغير الله. بعد هذا، وفي نفس الحالة، اضرب \"حَقْ\" ثلاث مرات متتالية على قلبك.",
    },
    step4Heading: { en: "4. Concluding Durood", ur: "۴۔ اختتامی درود", ro: "4. Ikhtitami Durood", hi: "४. इख़्तितामी दरूद", ar: "٤. الدرود الختامي" },
    step4Text: {
      en: "After completing the Muraqaba, conclude the Shaghl by reciting the same Durood-e-Ahl-e-Bait (recited at the beginning) three times.",
      ur: "مراقبہ مکمل کرنے کے بعد، شغل کے اختتام پر تین مرتبہ پھر وہی درودِ اہلِ بیت (جو ابتدا میں پڑھا گیا تھا) پڑھ لینا۔",
      ro: "Muraqba mukammal karne ke ba'd, shaghl ke ikhtetaam par teen martaba phir wahi Durood-e-Ahl-e-Bait (jo ibtida mein parha gaya tha) parh lena.",
      hi: "मुराक़बा मुकम्मल करने के बाद, शग़्ल के इख़्तिताम पर तीन मर्तबा फिर वही दरूद-ए-अहल-ए-बैत (जो इब्तिदा में पढ़ा गया था) पढ़ लेना।",
      ar: "بعد إكمال المراقبة، اختتم الشغل بتلاوة نفس درود أهل البيت (الذي تلي في البداية) ثلاث مرات.",
    },
  },
  benefitsCard: {
    title: { en: "Benefits & Guidance", ur: "فوائد و ہدایات", ro: "Fawaid o Hidayat", hi: "फ़वाइद ओ हिदायत", ar: "الفوائد والإرشادات" },
    description: { en: "Understanding the outcomes and important considerations.", ur: "نتائج اور اہم پہلوؤں کو سمجھنا۔", ro: "Nata'ij aur aham pehluon ko samajhna.", hi: "नताइज और अहम पहलुओं को समझना।", ar: "فهم النتائج والاعتبارات الهامة." },
    conclusionText: {
      en: "O my dear! This Shaghl-e-Noori, if performed with sincerity (Ikhlaas) and regularity, becomes a means for the illumination of your heart, the attainment of closeness (Qurb) and gnosis (Ma'rifat) of Allah, the Exalted, and reaching a special station of spiritual presence and state (Kaifiyat).",
      ur: "اے میرے عزیز! یہ شغلِ نوری، اگر اخلاص اور پابندی سے کیا جائے، تو یہ تمہارے دل کی روشنی، اللہ تبارک و تعالیٰ کے قرب و معرفت کے حصول اور ایک خاص روحانی حضوری و کیفیت کے مقام تک پہنچنے کا ذریعہ بنتا ہے۔",
      ro: "Ae mere azeez! Yeh Shaghl-e-Noori, agar ikhlaas aur paabandi se kiya jaaye, toh yeh tumhaare dil ki raushni, Allah Tabaarak Wa Ta'ala ke qurb o ma'rifat ke husool aur ek khaas roohaani huzoori o kaifiyat ke maqaam tak pahunchne ka zariya banta hai.",
      hi: "ऐ मेरे अज़ीज़! यह शग़्ल-ए-नूरी, अगर इख़लास और पाबंदी से किया जाए, तो यह तुम्हारे दिल की रौशनी, अल्लाह तबारक व तआला के क़ुर्ब ओ मारिफ़त के हुसूल और एक ख़ास रूहानी हुज़ूरी ओ कैफ़ियत के मक़ाम तक पहुंचने का ज़रिया बनता है।",
      ar: "يا عزيزي! هذا الشغل النوري، إذا أُدي بإخلاص وانتظام، يصبح وسيلة لإنارة قلبك، ونيل قرب الله ومعرفته، والوصول إلى مقام خاص من الحضور الروحي والحالة الروحية.",
    },
    guidanceLabel: { en: "Guidance:", ur: "ہدایت:", ro: "Hidayat:", hi: "हिदायत:", ar: "إرشاد:" },
    durationGuidance: {
      en: "Perform this daily for approximately thirty minutes, and more if possible. The best time for it is the last part of the night (Tahajjud time) or after the Isha prayer.",
      ur: "اسے روزانہ تقریباً تیس منٹ تک کیا جا ئے اور زیادہ جتنا ہو جائے، اور اس کے لیے بہترین وقت رات کا آخری پہر (تہجد کا وقت) یا نمازِ عشاء کے بعد کا ہے۔",
      ro: "Ise rozaana taqreeban tees minute tak kiya jaaye aur zyaada jitna ho jaaye, aur is ke liye behtareen waqt raat ka aakhri pehar (tahajjud ka waqt) ya namaaz-e-ishaa ke ba'd ka hai.",
      hi: "इसे रोज़ाना तक़रीबन तीस मिनट तक किया जाए और ज़्यादा जितना हो जाए, और इसके लिए बेहतरीन वक़्त रात का आख़िरी पहर (तहज्जुद का वक़्त) या नमाज़-ए-ईशा के बाद का है।",
      ar: "قم به يوميًا لمدة ثلاثين دقيقة تقريبًا، وأكثر إذا أمكن. أفضل وقت له هو الجزء الأخير من الليل (وقت التهجد) أو بعد صلاة العشاء.",
    }
  },
  tasbeehLink: {
    en: "Tasbeeh Counter", ur: "تسبیح کاؤنٹر", ro: "Tasbeeh Counter", hi: "तस्बीह काउंटर", ar: "عداد التسبيح",
  }
};


export default function ShagleNooriPage() {
  const { language } = useLanguage();

  const currentContent = {
    pageTitle: translations.pageTitle[language] || translations.pageTitle.en,
    pageSubtitle: translations.pageSubtitle[language] || translations.pageSubtitle.en,
    backToPracticesText: translations.backToPractices[language] || translations.backToPractices.en,
    introTabText: translations.tabs.introduction[language] || translations.tabs.introduction.en,
    introCardTitleText: translations.introCard.title[language] || translations.introCard.title.en,
    introCardDescriptionText: translations.introCard.description[language] || translations.introCard.description.en,
    introduction_main: translations.introCard.mainText[language] || translations.introCard.mainText.en,
    taharat_guidance: translations.introCard.taharatGuidance[language] || translations.introCard.taharatGuidance.en,
    methodTabText: translations.tabs.method[language] || translations.tabs.method.en,
    methodCardTitleText: translations.methodCard.title[language] || translations.methodCard.title.en,
    methodCardDescriptionText: translations.methodCard.description[language] || translations.methodCard.description.en,
    durood_instruction_heading: translations.methodCard.step1Heading[language] || translations.methodCard.step1Heading.en,
    durood_instruction_text: translations.methodCard.step1Text[language] || translations.methodCard.step1Text.en,
    dua_e_noori_instruction_heading: translations.methodCard.step2Heading[language] || translations.methodCard.step2Heading.en,
    dua_e_noori_instruction_text: translations.methodCard.step2Text[language] || translations.methodCard.step2Text.en,
    dua_e_noori_translation: translations.methodCard.duaNooriTranslation[language] || translations.methodCard.duaNooriTranslation.en,
    muraqaba_instruction_heading: translations.methodCard.step3Heading[language] || translations.methodCard.step3Heading.en,
    muraqaba_instruction_intro: translations.methodCard.step3Intro[language] || translations.methodCard.step3Intro.en,
    muraqaba_method: translations.methodCard.step3Method[language] || translations.methodCard.step3Method.en,
    concluding_durood_instruction_heading: translations.methodCard.step4Heading[language] || translations.methodCard.step4Heading.en,
    concluding_durood_instruction_text: translations.methodCard.step4Text[language] || translations.methodCard.step4Text.en,
    benefitsTabText: translations.tabs.benefits[language] || translations.tabs.benefits.en,
    benefitsCardTitleText: translations.benefitsCard.title[language] || translations.benefitsCard.title.en,
    benefitsCardDescriptionText: translations.benefitsCard.description[language] || translations.benefitsCard.description.en,
    benefits_conclusion: translations.benefitsCard.conclusionText[language] || translations.benefitsCard.conclusionText.en,
    guidanceLabel: translations.benefitsCard.guidanceLabel[language] || translations.benefitsCard.guidanceLabel.en,
    duration_guidance: translations.benefitsCard.durationGuidance[language] || translations.benefitsCard.durationGuidance.en,
    tasbeehButtonLabel: translations.tasbeehLink[language] || translations.tasbeehLink.en,
  };

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : (language === 'hi' ? 'font-sans' : 'font-sans');
  const textDir = isRtl ? 'rtl' : 'ltr';

  const accordionItems = [
    {
      value: "item-introduction",
      title: currentContent.introTabText,
      cardTitle: currentContent.introCardTitleText,
      description: currentContent.introCardDescriptionText,
      icon: Info,
      content: (
        <>
          <p className={cn("text-base md:text-lg", fontClass)}>{currentContent.introduction_main}</p>
          <p className={cn("mt-4 text-base md:text-lg", fontClass)}>{currentContent.taharat_guidance}</p>
        </>
      )
    },
    {
      value: "item-method",
      title: currentContent.methodTabText,
      cardTitle: currentContent.methodCardTitleText,
      description: currentContent.methodCardDescriptionText, 
      icon: ListChecks,
      content: (
        <div className="space-y-6">
          <section aria-labelledby={`method-step1-${language}`}>
            <h3 id={`method-step1-${language}`} className={cn("font-semibold text-base md:text-lg mb-2 text-foreground", fontClass)}>
                {currentContent.durood_instruction_heading}
            </h3>
            <p className={cn("mb-2 text-sm md:text-base", fontClass)}>{currentContent.durood_instruction_text}</p>
            {(language === 'ro' || language === 'hi') ? (
                <div lang={language} dir="ltr" className={cn("font-sans text-lg md:text-xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose")}>
                    {language === 'ro' ? durood_ahlulbayt_ro : durood_ahlulbayt_hi}
                </div>
            ) : (
                <div lang="ar" dir="rtl" className="font-arabic text-xl md:text-2xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose">{durood_ahlulbayt_ar}</div>
            )}
          </section>

          <section aria-labelledby={`method-step2-${language}`}>
            <h3 id={`method-step2-${language}`} className={cn("font-semibold text-base md:text-lg mb-2 text-foreground", fontClass)}>
                {currentContent.dua_e_noori_instruction_heading}
            </h3>
            <p className={cn("mb-2 text-sm md:text-base", fontClass)}>{currentContent.dua_e_noori_instruction_text}</p>
             {(language === 'ro' || language === 'hi') ? (
                <div lang={language} dir="ltr" className={cn("font-sans text-lg md:text-xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose")}>
                   {language === 'ro' ? dua_e_noori_ro : dua_e_noori_hi}
                </div>
            ) : (
                <div lang="ar" dir="rtl" className="font-arabic text-xl md:text-2xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose">{dua_e_noori_ar}</div>
            )}
            <p className={cn("text-muted-foreground text-xs md:text-sm", fontClass)}>{currentContent.dua_e_noori_translation}</p>
          </section>

          <section aria-labelledby={`method-step3-${language}`}>
             <h3 id={`method-step3-${language}`} className={cn("font-semibold text-base md:text-lg mb-2 text-foreground", fontClass)}>
                {currentContent.muraqaba_instruction_heading}
            </h3>
            <p className={cn("mb-2 text-sm md:text-base", fontClass)}>{currentContent.muraqaba_instruction_intro}</p>
             {(language === 'ro' || language === 'hi') ? (
                <div lang={language} dir="ltr" className={cn("font-sans text-lg md:text-xl my-3 p-4 bg-primary/10 text-primary rounded-md text-center shadow-inner leading-loose")}>
                    {language === 'ro' ? muraqaba_zikr_words_ro_transliteration : muraqaba_zikr_words_hi_transliteration}
                </div>
            ) : (
                 <div lang="ar" dir="rtl" className={cn("font-arabic text-xl md:text-2xl my-3 p-4 bg-primary/10 text-primary rounded-md text-center shadow-inner leading-loose", fontClass)}>
                  {zikrWords_ar}
                </div>
            )}
            <p className={cn("text-sm md:text-base", fontClass)}>{currentContent.muraqaba_method}</p>
          </section>

          <section aria-labelledby={`method-step4-${language}`}>
            <h3 id={`method-step4-${language}`} className={cn("font-semibold text-base md:text-lg mb-2 text-foreground", fontClass)}>
                 {currentContent.concluding_durood_instruction_heading}
            </h3>
            <p className={cn("mb-2 text-sm md:text-base", fontClass)}>{currentContent.concluding_durood_instruction_text}</p>
            {(language === 'ro' || language === 'hi') ? (
                 <div lang={language} dir="ltr" className={cn("font-sans text-lg md:text-xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose")}>
                    {language === 'ro' ? durood_ahlulbayt_ro : durood_ahlulbayt_hi}
                </div>
            ) : (
                <div lang="ar" dir="rtl" className="font-arabic text-xl md:text-2xl my-3 p-4 bg-muted/50 rounded-md text-center shadow-inner leading-loose">{durood_ahlulbayt_ar}</div>
            )}
          </section>
          <div className="h-20"></div> {/* Empty div for ~3 lines of space */}
        </div>
      )
    },
    {
      value: "item-benefits",
      title: currentContent.benefitsTabText,
      cardTitle: currentContent.benefitsCardTitleText,
      description: currentContent.benefitsCardDescriptionText, 
      icon: ShieldCheck,
      content: (
        <div className="space-y-6">
          <section aria-labelledby={`benefits-${language}-heading`}>
            <p className={cn("text-base md:text-lg", fontClass)}>{currentContent.benefits_conclusion}</p>
          </section>
          <section aria-labelledby={`guidance-${language}-heading`} className="mt-6">
             <h3 id={`guidance-${language}-heading`} className={cn("font-semibold text-base md:text-lg text-foreground mb-2", fontClass, (language === 'en' || language === 'ro' || language === 'hi' || language === 'ar') && "capitalize")}>
                 {currentContent.guidanceLabel}
             </h3>
            <p className={cn("text-base md:text-lg", fontClass)}>{currentContent.duration_guidance}</p>
          </section>
          <div className="h-28"></div> {/* Empty div for 4 lines of space */}
        </div>
      )
    }
  ];


  return (
    <div className="space-y-8">
      <div className="px-0">
        <PageTitle 
          title={currentContent.pageTitle} 
          subtitle={currentContent.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      <Accordion type="single" collapsible className="w-full mt-6 space-y-4">
          {accordionItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <AccordionItem key={item.value} value={item.value} className="border bg-card rounded-lg shadow-lg overflow-hidden">
                <AccordionTrigger
                  className={cn(
                    "text-xl font-semibold p-4 w-full flex justify-between items-center group hover:no-underline",
                    "data-[state=open]:bg-primary data-[state=open]:text-primary-foreground data-[state=open]:rounded-b-none",
                    "hover:bg-muted/50",
                    "text-sm sm:text-base py-3 px-3 sm:py-2.5 sm:px-4", 
                    textDir === 'rtl' ? "sm:justify-end" : "sm:justify-start", 
                  )}
                >
                  <div className={cn("flex items-center", textDir === 'rtl' && 'flex-row-reverse w-full justify-end')}>
                    <IconComponent className={cn("h-5 w-5 transition-colors duration-200 group-data-[state=open]:text-primary-foreground text-primary", textDir === 'rtl' ? "ml-2" : "mr-2")} />
                    <span className={cn(fontClass, 'text-sm sm:text-base')}>{item.title}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-0">
                  <Card className="shadow-none border-0 rounded-t-none bg-transparent">
                    <CardHeader className={cn("pt-2 pb-2 px-6", textDir === 'rtl' && 'text-right')}>
                      <h2 className={cn("text-lg md:text-xl font-semibold", fontClass)}>
                        {item.cardTitle}
                      </h2>
                      <CardDescription className={cn("text-xs md:text-sm text-muted-foreground", fontClass)}>
                        {item.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent 
                        lang={language} 
                        dir={textDir} 
                        className={cn("space-y-4 text-foreground/90 leading-relaxed px-6 pb-6 text-sm md:text-base", fontClass)} // Adjusted base font size for content
                    >
                      {item.content}
                    </CardContent>
                  </Card>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      
      <Link href="/tasbeeh" passHref>
        <Button
          variant="default"
          className={cn(
            "fixed bottom-6 right-6 z-40 rounded-full w-16 h-16 p-0 flex flex-col items-center justify-center shadow-2xl",
            "bg-gradient-to-br from-primary via-primary/80 to-accent text-primary-foreground",
            "hover:scale-110 hover:shadow-primary/50 focus:scale-110 focus:shadow-primary/50 transition-all duration-300 ease-out"
          )}
          aria-label={currentContent.tasbeehButtonLabel}
        >
          <Repeat className="w-7 h-7" />
          <span className="sr-only">{currentContent.tasbeehButtonLabel}</span>
        </Button>
      </Link>
    </div>
  );
}

