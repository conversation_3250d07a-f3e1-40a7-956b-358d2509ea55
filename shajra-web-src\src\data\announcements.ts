
// Mock data for admin announcements
// In a real app, this would come from Firestore's 'admin_broadcasts' collection

export type AdminAnnouncement = {
  id: string;
  messageText: string;
  timestamp: Date; // Using Date object for easier manipulation
  sentBy?: string;
  title?: string;
};

export const mockAnnouncementsData: AdminAnnouncement[] = [
  {
    id: 'anno3',
    messageText: 'Reminder: Special <PERSON><PERSON><PERSON> session tonight after <PERSON><PERSON> prayer. All are welcome to join online.',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    sentBy: 'Admin',
    title: 'Zikr Session Reminder',
  },
  {
    id: 'anno2',
    messageText: 'The new series of teachings on "Patience in Adversity" will begin next week. More details to follow.',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    sentBy: 'Admin',
    title: 'New Teachings Series',
  },
  {
    id: 'anno1',
    messageText: '<PERSON><PERSON><PERSON><PERSON>. Welcome to the Sabiriya Spiritual Guide application. We hope you find peace and guidance here.',
    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    sentBy: 'Admin',
    title: 'Welcome Message',
  },
  // Add more mock announcements if needed, up to 5 for the initial display
];
