
"use client";

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlayCircle, Info, ListChecks, ClipboardList, Sparkles } from 'lucide-react';
import Link from 'next/link';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';
import { useLanguage, type Language } from '@/context/LanguageContext';
import React from 'react';

// --- Content Definitions ---
type MuhasabaContentItem = {
  heading: string;
  text?: string;
  steps?: string[];
};

type MuhasabaPageContent = {
  pageTitle: string;
  pageSubtitle: string;
  backToPractices: string;
  startSessionButton: string;
  sections: {
    introduction: MuhasabaContentItem;
    importance: MuhasabaContentItem;
    method: MuhasabaContentItem;
    benefits: MuhasabaContentItem;
  };
};

const translations: Record<Language, MuhasabaPageContent> = {
  en: {
    pageTitle: "<PERSON><PERSON><PERSON> (Self-Accountability)",
    pageSubtitle: "Taking Account of the Self",
    backToPractices: "Back to Spiritual Practices",
    startSessionButton: "Start Muhasaba Session",
    sections: {
      introduction: {
        heading: "Introduction",
        text: "Muhasaba is a vital spiritual practice for self-rectification and development. It involves a person meticulously reviewing their actions, thoughts, and intentions.",
      },
      importance: {
        heading: "Importance of Muhasaba",
        text: "Muhasaba protects from heedlessness (ghaflah), instills remorse for sins, and motivates towards better deeds in the future. It is the first step towards self-awareness.",
      },
      method: {
        heading: "Method of Muhasaba",
        steps: [
          "1. At the beginning of the day, make a firm resolution for good deeds.",
          "2. Throughout the day, monitor your actions to see if they align with your resolutions.",
          "3. At the end of the day, conduct a detailed review of your actions: what good was done, what bad was done, and what shortcomings occurred?",
          "4. Thank Allah for the good deeds and ask for the ability to do more.",
          "5. Express remorse for bad deeds or shortcomings, seek repentance (Tawbah) and forgiveness (Istighfar), and make a firm commitment not to repeat them."
        ],
      },
      benefits: {
        heading: "Benefits of Muhasaba",
        text: "Purification and illumination of the heart, cleansing and rectification of the self (nafs), ability to abstain from sins, a sense of closeness to Allah Almighty, and mindfulness and preparation for the Hereafter.",
      },
    },
  },
  ur: {
    pageTitle: "محاسبہ",
    pageSubtitle: "اپنے نفس کا احتساب",
    backToPractices: "روحانی اعمال کی طرف واپس",
    startSessionButton: "محاسبہ شروع کریں",
    sections: {
      introduction: {
        heading: "تعارف",
        text: "محاسبہ نفس کی اصلاح اور روحانی ترقی کے لیے ایک نہایت اہم عمل ہے۔ اس میں انسان اپنے اعمال، خیالات اور نیتوں کا جائزہ لیتا ہے۔",
      },
      importance: {
        heading: "محاسبے کی اہمیت",
        text: "محاسبہ غفلت سے بچاتا ہے، گناہوں پر ندامت پیدا کرتا ہے، اور آئندہ کے لیے بہتر اعمال کی ترغیب دیتا ہے۔ یہ خود شناسی کی طرف پہلا قدم ہے۔",
      },
      method: {
        heading: "محاسبے کا طریقہ",
        steps: [
          "۱۔ دن کے آغاز میں نیک اعمال کا عزم کرنا۔",
          "۲۔ دن بھر اپنے اعمال کی نگرانی کرنا کہ وہ عزم کے مطابق ہیں یا نہیں۔",
          "۳۔ دن کے اختتام پر اپنے اعمال کا تفصیلی جائزہ لینا: کیا اچھا کیا اور کیا برا، کیا کمی رہ گئی؟",
          "۴۔ اچھے اعمال پر اللہ کا شکر ادا کرنا اور مزید کی توفیق مانگنا۔",
          "۵۔ برے اعمال یا کوتاہیوں پر ندامت کا اظہار، توبہ و استغفار کرنا اور آئندہ نہ کرنے کا پختہ عہد کرنا۔"
        ],
      },
      benefits: {
        heading: "محاسبے کے فوائد",
        text: "قلب کی صفائی اور نورانیت، نفس کی پاکیزگی اور اصلاح، گناہوں سے بچنے کی توفیق، اللہ تعالیٰ سے قربت کا احساس، اور آخرت کی فکر اور تیاری۔",
      },
    },
  },
  ro: {
    pageTitle: "Muhasaba",
    pageSubtitle: "Apne Nafs ka Ehtisaab",
    backToPractices: "Roohaani Amaal ki Taraf Wapas",
    startSessionButton: "Muhasaba Shuru Karein",
    sections: {
      introduction: {
        heading: "Ta'aruf",
        text: "Muhasaba nafs ki islaah aur roohani taraqqi ke liye ek nihayat aham amal hai. Is mein insaan apne a'maal, khayalaat aur niyyaton ka jaiza leta hai.",
      },
      importance: {
        heading: "Muhasabe ki Ahmiyat",
        text: "Muhasaba ghaflat se bachata hai, gunahon par nadamat paida karta hai, aur aainda ke liye behtar a'maal ki targheeb deta hai. Yeh khud-shanaasi ki taraf pehla qadam hai.",
      },
      method: {
        heading: "Muhasabe ka Tareeqa",
        steps: [
          "1. Din ke aaghaaz mein nek a'maal ka 'azm karna.",
          "2. Din bhar apne a'maal ki nigraani karna keh woh 'azm ke mutaabiq hain ya nahin.",
          "3. Din ke ikhtitaam par apne a'maal ka tafseeli jaiza lena: kya achha kiya aur kya bura, kya kami reh gayi?",
          "4. Achhe a'maal par Allah ka shukr ada karna aur mazeed ki tawfeeq maangna.",
          "5. Bure a'maal ya kotaahiyon par nadamat ka izhaar, tauba o istighfaar karna aur aainda na karne ka pukhta 'ahd karna."
        ],
      },
      benefits: {
        heading: "Muhasabe ke Fawaid",
        text: "Qalb ki safaai aur nooraaniyat, nafs ki paakeezgi aur islaah, gunahon se bachne ki tawfeeq, Allah Ta'ala se qurbat ka ehsaas, aur aakhirat ki fikr aur tayyari.",
      },
    },
  },
  hi: {
    pageTitle: "मुहासबा",
    pageSubtitle: "अपने नफ़्स का एहतिसाब",
    backToPractices: "रूहानी आमाल की तरफ़ वापस",
    startSessionButton: "मुहासबा शुरू करें",
    sections: {
      introduction: {
        heading: "तआरुफ़",
        text: "मुहासबा नफ़्स की इस्लाह और रूहानी तरक़्क़ी के लिए एक निहायत अहम अमल है। इसमें इंसान अपने आमाल, ख़यालात और नियतों का जायज़ा लेता है।",
      },
      importance: {
        heading: "मुहासबे की अहमियत",
        text: "मुहासबा ग़फ़लत से बचाता है, गुनाहों पर नदामत पैदा करता है, और आइंदा के लिए बेहतर आमाल की तरग़ीब देता है। यह ख़ुद-शनासी की तरफ़ पहला क़दम है।",
      },
      method: {
        heading: "मुहासबे का तरीक़ा",
        steps: [
          "१. दिन के आग़ाज़ में नेक आमाल का अज़्म करना।",
          "२. दिन भर अपने आमाल की निगरानी करना कि वह अज़्म के मुताबिक़ हैं या नहीं।",
          "३. दिन के इख़्तिताम पर अपने आमाल का तफ़सीली जायज़ा लेना: क्या अच्छा किया और क्या बुरा, क्या कमी रह गई?",
          "४. अच्छे आमाल पर अल्लाह का शुक्र अदा करना और मज़ीद की तौफ़ीक़ माँगना।",
          "५. बुरे आमाल या कोताहियों पर नदामत का इज़हार, तौबा ओ इस्तिग़फ़ार करना और आइंदा न करने का पुख़्ता अहद करना।"
        ],
      },
      benefits: {
        heading: "मुहासबे के फ़वाइद",
        text: "क़ल्ब की सफ़ाई और नूरानियत, नफ़्स की पाकीज़गी और इस्लाह, गुनाहों से बचने की तौफ़ीक़, अल्लाह तआला से क़ुर्बत का एहसास, और आख़िरत की फ़िक्र और तैयारी।",
      },
    },
  },
  ar: {
    pageTitle: "المحاسبة",
    pageSubtitle: "محاسبة النفس",
    backToPractices: "العودة إلى الممارسات الروحية",
    startSessionButton: "ابدأ جلسة المحاسبة",
    sections: {
      introduction: {
        heading: "مقدمة",
        text: "المحاسبة عمل روحي بالغ الأهمية لإصلاح النفس وتنميتها. فيها يراجع الإنسان أعماله وأفكاره ونواياه بدقة.",
      },
      importance: {
        heading: "أهمية المحاسبة",
        text: "المحاسبة تحمي من الغفلة، وتورث الندم على الذنوب، وتحفز على الأعمال الصالحة في المستقبل. وهي الخطوة الأولى نحو معرفة النفس.",
      },
      method: {
        heading: "طريقة المحاسبة",
        steps: [
          "١. في بداية اليوم، اعقد العزم على فعل الخيرات.",
          "٢. طوال اليوم، راقب أعمالك لترى هل تتوافق مع عزمك.",
          "٣. في نهاية اليوم، قم بمراجعة تفصيلية لأعمالك: ما فعلت من خير، وما فعلت من شر، وما هي النواقص؟",
          "٤. اشكر الله على الخيرات واطلب منه التوفيق للمزيد.",
          "٥. عبر عن ندمك على السيئات أو التقصير، وتب واستغفر، واعقد العزم على عدم تكرارها."
        ],
      },
      benefits: {
        heading: "فوائد المحاسبة",
        text: "تطهير القلب وتنوره، وتزكية النفس وإصلاحها، والتوفيق لاجتناب الذنوب، والشعور بالقرب من الله تعالى، واليقظة والاستعداد للآخرة.",
      },
    },
  }
};


export default function MuhasabaPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  const sectionDetails = [
    { id: "introduction", data: content.sections.introduction, icon: Info },
    { id: "importance", data: content.sections.importance, icon: ListChecks },
    { id: "method", data: content.sections.method, icon: ClipboardList },
    { id: "benefits", data: content.sections.benefits, icon: Sparkles },
  ];

  return (
    <div className="space-y-8">
      <div className="px-0">
        <PageTitle 
          title={content.pageTitle} 
          subtitle={content.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      <div className="text-center mb-8">
        <Link href="/practices/muhasaba/session" passHref>
          <Button size="lg" className={cn("bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300", fontClass)}>
            <PlayCircle className={cn("h-5 w-5", textDir === 'rtl' ? 'ml-2' : 'mr-2')} />
            {content.startSessionButton}
          </Button>
        </Link>
      </div>

      <div className="space-y-6">
        {sectionDetails.map((section) => {
          const IconComponent = section.icon;
          return (
            <Card key={section.id} className="shadow-xl bg-card">
              <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
                <h2 className={cn("text-xl md:text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                  <IconComponent className={cn("h-6 w-6", textDir === 'rtl' ? "ml-3" : "mr-3")} />
                  {section.data.heading}
                </h2>
              </CardHeader>
              <CardContent 
                lang={language} 
                dir={textDir} 
                className={cn("space-y-4 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}
              >
                {section.data.text && <p>{section.data.text}</p>}
                {section.data.steps && (
                   <ul className={cn("list-inside space-y-2", isRtl ? "list-decimal pr-4" : "list-disc pl-4", (language === 'en' || language === 'hi' || language === 'ro') && "list-decimal pl-4", language === 'ar' && "list-decimal pr-4")}>
                    {section.data.steps.map((step, index) => (
                      <li key={`method-${index}`}>{step}</li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
    


    