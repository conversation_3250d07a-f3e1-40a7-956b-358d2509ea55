
import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type Language = 'en' | 'ar' | 'ur' | 'hi' | 'ro'; // Added hi and ro for future use

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  availableLanguages: { code: Language; name: string; disabled?: boolean }[];
}

const LANGUAGE_STORAGE_KEY = 'sabiriya-app-language';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const defaultLanguage: Language = 'en';

export const availableLanguagesList: LanguageContextType['availableLanguages'] = [
  { code: 'en', name: 'English' },
  { code: 'ar', name: 'العربية (Arabic)' },
  { code: 'ur', name: 'اردو (Urdu)' },
  { code: 'ro', name: 'Roman Urdu' },
  { code: 'hi', name: 'हिन्दी (Hindi)' }, // Enabled Hindi
];

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, _setLanguage] = useState<Language>(defaultLanguage);

  const setLanguage = useCallback(async (lang: Language) => {
    _setLanguage(lang);
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, lang);
    } catch (error) {
      console.error('Error saving language to AsyncStorage:', error);
    }
  }, []);

  useEffect(() => {
    // Load language from AsyncStorage on app start
    const loadLanguage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY) as Language | null;
        if (storedLanguage && availableLanguagesList.some(lang => lang.code === storedLanguage && !lang.disabled)) {
          _setLanguage(storedLanguage);
        }
      } catch (error) {
        console.error('Error loading language from AsyncStorage:', error);
      }
    };

    loadLanguage();
  }, []);


  return (
    <LanguageContext.Provider value={{ language, setLanguage, availableLanguages: availableLanguagesList }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};