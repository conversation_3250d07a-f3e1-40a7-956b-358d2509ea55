import { initializeApp, getApp, getApps, type FirebaseApp } from 'firebase/app';
import { getFirestore, type Firestore } from 'firebase/firestore';
import { getStorage, type FirebaseStorage } from 'firebase/storage';
import { getAuth, type Auth, initializeAuth, getReactNativePersistence } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyD3SrBnT0ra0kdBCNe6UWGwK8FbiotrVhQ",
  authDomain: "sabriya-7fa21.firebaseapp.com",
  projectId: "sabriya-7fa21",
  storageBucket: "sabriya-7fa21.firebasestorage.app",
  messagingSenderId: "586738512082",
  appId: "1:586738512082:web:4c383395890e6a5b267d0b",
  measurementId: "G-WEJBTM3TJX"
};

// Initialize Firebase
let app: FirebaseApp;
let db: Firestore;
let storage: FirebaseStorage;
let auth: Auth;

if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

// Initialize Firestore
db = getFirestore(app);

// Initialize Storage
storage = getStorage(app);

// Initialize Auth with React Native persistence
auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

export { app, db, storage, auth };
