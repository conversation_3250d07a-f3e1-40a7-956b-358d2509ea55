
"use client";

import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Zap, Brain, Lightbulb, Flower2, PlayCircle, UserRound, Heart, Users2, ListChecks, Info, Repeat, UserCheck, Eye, ShieldCheck, ClipboardList } from 'lucide-react';
import { useLanguage, type Language } from '@/context/LanguageContext';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';

type PracticeInfo = {
  slug: string;
  titleKey: string; // Key to look up in translations
  descriptionKey: string; // Key to look up in translations
  icon: React.ElementType;
};

const practicesList: PracticeInfo[] = [
  { slug: 'paas-anfaas', titleKey: 'paasAnfaasTitle', descriptionKey: 'paasAnfaasDesc', icon: PlayCircle },
  { slug: 'shughl-noori', titleKey: 'shagleNooriTitle', descriptionKey: 'shagleNooriDesc', icon: Lightbulb },
  { slug: 'muhasaba', titleKey: 'muhasabaTitle', descriptionKey: 'muhasabaDesc', icon: ClipboardList },
  { slug: 'muraqaba', titleKey: 'muraqabaTitle', descriptionKey: 'muraqabaDesc', icon: Brain },
  { slug: 'tasawwure-shaikh', titleKey: 'tasawwureShaikhTitle', descriptionKey: 'tasawwureShaikhDesc', icon: UserRound },
  { slug: 'tasawwure-isme-zaat', titleKey: 'tasawwureIsmeZaatTitle', descriptionKey: 'tasawwureIsmeZaatDesc', icon: Eye },
  { slug: 'halqae-zikr', titleKey: 'halqaeZikrTitle', descriptionKey: 'halqaeZikrDesc', icon: Users2 },
  // { slug: 'recommendations', titleKey: 'recommendationsTitle', descriptionKey: 'recommendationsDesc', icon: Wand2 }, // Wand2 not in lucide-react
];

const translations: Record<Language, Record<string, string>> = {
  en: {
    pageTitle: "Spiritual Practices",
    pageSubtitle: "Engage in transformative practices from the Chishti Sabiriya Silsila.",
    paasAnfaasTitle: "Paas Anfaas (Awareness of Breath)",
    paasAnfaasDesc: "A foundational practice focusing on the remembrance of Allah with each breath.",
    shagleNooriTitle: "Shagle Noori (Practice of Light)",
    shagleNooriDesc: "A spiritual exercise to illuminate the heart with divine light.",
    muhasabaTitle: "Muhasaba (Self-Accountability)",
    muhasabaDesc: "The practice of taking account of one's actions and intentions for self-rectification.",
    muraqabaTitle: "Muraqaba (Contemplation/Meditation)",
    muraqabaDesc: "Deep contemplation and awareness to achieve closeness with Allah.",
    tasawwureShaikhTitle: "Tasawwur-e-Shaikh (Visualization of the Guide)",
    tasawwureShaikhDesc: "Visualizing one's spiritual guide to receive spiritual grace and guidance.",
    tasawwureIsmeZaatTitle: "Tasawwur-e-Ism-e-Zaat (Visualization of the Divine Name)",
    tasawwureIsmeZaatDesc: "A profound practice of visualizing the Name of Allah for spiritual elevation.",
    halqaeZikrTitle: "Halqa-e-Zikr (Circle of Remembrance)",
    halqaeZikrDesc: "Congregational remembrance of Allah to collectively elevate spiritual states.",
    recommendationsTitle: "Personalized Recommendations",
    recommendationsDesc: "Get AI-powered suggestions for practices tailored to your spiritual journey.",
    learnMoreButton: "Learn More",
  },
  ur: {
    pageTitle: "روحانی اعمال",
    pageSubtitle: "سلسلہ چشتیہ صابریہ کے روحانی اعمال میں مشغول ہوں۔",
    paasAnfaasTitle: "پاسِ انفاس",
    paasAnfaasDesc: "ہر سانس کے ساتھ اللہ کی یاد پر توجہ مرکوز کرنے کی بنیادی مشق۔",
    shagleNooriTitle: "شغلِ نوری",
    shagleNooriDesc: "قلب کو نورِ الٰہی سے منور کرنے کی روحانی مشق۔",
    muhasabaTitle: "محاسبہ",
    muhasabaDesc: "اصلاحِ نفس کے لیے اپنے اعمال و نیتوں کا جائزہ لینے کا عمل۔",
    muraqabaTitle: "مراقبہ",
    muraqabaDesc: "اللہ سے قربت حاصل کرنے کے لیے گہرا غور و فکر اور آگاہی۔",
    tasawwureShaikhTitle: "تصورِ شیخ",
    tasawwureShaikhDesc: "روحانی فیض و رہنمائی حاصل کرنے کے لیے اپنے مرشد کا تصور کرنا۔",
    tasawwureIsmeZaatTitle: "تصورِ اسمِ ذات",
    tasawwureIsmeZaatDesc: "روحانی بلندی کے لیے اسمِ اللہ ذات کا گہرا تصور۔",
    halqaeZikrTitle: "حلقۂ ذکر",
    halqaeZikrDesc: "اجتماعی طور پر روحانی کیفیات کو بلند کرنے کے لیے اللہ کا ذکر۔",
    recommendationsTitle: "ذاتی تجاویز",
    recommendationsDesc: "اپنے روحانی سفر کے مطابق مصنوعی ذہانت سے چلنے والی تجاویز حاصل کریں۔",
    learnMoreButton: "مزید جانیں",
  },
  ro: {
    pageTitle: "Roohaani Amaal",
    pageSubtitle: "Silsila Chishtiya Sabiriya ke roohaani amaal mein mashghool hon.",
    paasAnfaasTitle: "Paas Anfaas",
    paasAnfaasDesc: "Har saans ke saath Allah ki yaad par tawajjuh markooz karne ki buniyadi mashq.",
    shagleNooriTitle: "Shaghl-e-Noori",
    shagleNooriDesc: "Qalb ko Noor-e-Ilaahi se munawwar karne ki roohaani mashq.",
    muhasabaTitle: "Muhasaba",
    muhasabaDesc: "Islaah-e-nafs ke liye apne a'maal o niyyaton ka jaa'iza lene ka amal.",
    muraqabaTitle: "Muraqaba",
    muraqabaDesc: "Allah se qurbat haasil karne ke liye gehra ghaur o fikr aur aagahi.",
    tasawwureShaikhTitle: "Tasawwur-e-Shaikh",
    tasawwureShaikhDesc: "Roohaani faiz o rehnumaee haasil karne ke liye apne Murshid ka tasawwur karna.",
    tasawwureIsmeZaatTitle: "Tasawwur-e-Ism-e-Zaat",
    tasawwureIsmeZaatDesc: "Roohaani bulandi ke liye Ism-e-Allah Zaat ka gehra tasawwur.",
    halqaeZikrTitle: "Halqa-e-Zikr",
    halqaeZikrDesc: "Ijtima'ee taur par roohaani kaifiyat ko buland karne ke liye Allah ka zikr.",
    recommendationsTitle: "Zaati Tajaweez",
    recommendationsDesc: "Apne roohaani safar ke mutaabiq masnoo'ee zehaanat se chalne wali tajaweez haasil karein.",
    learnMoreButton: "Mazeed Jaanein",
  },
  hi: {
    pageTitle: "रूहानी आमाल",
    pageSubtitle: "सिलसिला चिश्तिया साबिरीया के रूहानी आमाल में मशग़ूल हों।",
    paasAnfaasTitle: "पास-ए-अनफ़ास",
    paasAnfaasDesc: "हर सांस के साथ अल्लाह की याद पर तवज्जो मरकूज़ करने की बुनियादी मश्क़।",
    shagleNooriTitle: "शग़्ल-ए-नूरी",
    shagleNooriDesc: "क़ल्ब को नूर-ए-इलाही से मुनव्वर करने की रूहानी मश्क़।",
    muhasabaTitle: "मुहासबा",
    muhasabaDesc: "इस्लाह-ए-नफ़्स के लिए अपने आमाल ओ नियतों का जायज़ा लेने का अमल।",
    muraqabaTitle: "मुराक़बा",
    muraqabaDesc: "अल्लाह से क़ुर्बत हासिल करने के लिए गहरा ग़ौर ओ फ़िक्र और आगाही।",
    tasawwureShaikhTitle: "तसव्वुर-ए-शैख़",
    tasawwureShaikhDesc: "रूहानी फ़ैज़ ओ रहनुमाई हासिल करने के लिए अपने मुर्शिद का तसव्वुर करना।",
    tasawwureIsmeZaatTitle: "तसव्वुर-ए-इस्मे-ज़ात",
    tasawwureIsmeZaatDesc: "रूहानी बुलंदी के लिए इस्मे-अल्लाह ज़ात का गहरा तसव्वुर।",
    halqaeZikrTitle: "हल्क़ा-ए-ज़िक्र",
    halqaeZikrDesc: "इज्तिमाई तौर पर रूहानी कैफ़ियत को बुलंद करने के लिए अल्लाह का ज़िक्र।",
    recommendationsTitle: "व्यक्तिगत सुझाव",
    recommendationsDesc: "अपने आध्यात्मिक सफ़र के अनुरूप कृत्रिम बुद्धिमत्ता से चलने वाले सुझाव प्राप्त करें।",
    learnMoreButton: "और जानें",
  },
  ar: {
    pageTitle: "الممارسات الروحية",
    pageSubtitle: "انخرط في ممارسات تحويلية من السلسلة الجشتية الصابرية.",
    paasAnfaasTitle: "مراقبة الأنفاس (پاس انفاس)",
    paasAnfaasDesc: "ممارسة أساسية تركز على ذكر الله مع كل نفس.",
    shagleNooriTitle: "الشغل النوري",
    shagleNooriDesc: "تمرين روحي لإنارة القلب بالنور الإلهي.",
    muhasabaTitle: "المحاسبة",
    muhasabaDesc: "ممارسة محاسبة النفس على الأفعال والنوايا لإصلاح الذات.",
    muraqabaTitle: "المراقبة",
    muraqabaDesc: "التأمل العميق والوعي لتحقيق القرب من الله.",
    tasawwureShaikhTitle: "تصور الشيخ",
    tasawwureShaikhDesc: "تصور المرشد الروحي لتلقي الفيض والإرشاد الروحي.",
    tasawwureIsmeZaatTitle: "تصور اسم الذات",
    tasawwureIsmeZaatDesc: "ممارسة عميقة لتصور اسم الله للارتقاء الروحي.",
    halqaeZikrTitle: "حلقة الذكر",
    halqaeZikrDesc: "الذكر الجماعي لله للارتقاء بالحالات الروحية بشكل جماعي.",
    recommendationsTitle: "توصيات شخصية",
    recommendationsDesc: "احصل على اقتراحات مدعومة بالذكاء الاصطناعي لممارسات مصممة خصيصًا لرحلتك الروحية.",
    learnMoreButton: "اعرف المزيد",
  }
};


export default function PracticesPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  return (
    <div className="space-y-8">
      <PageTitle 
        title={content.pageTitle} 
        subtitle={content.pageSubtitle} 
        className={cn(fontClass, isRtl ? 'text-right' : 'text-left')} 
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {practicesList.map((practice) => {
          const IconComponent = practice.icon;
          const title = content[practice.titleKey] || practice.titleKey;
          const description = content[practice.descriptionKey] || practice.descriptionKey;
          return (
            <Card key={practice.slug} className="flex flex-col shadow-lg hover:shadow-xl transition-shadow bg-card/80 backdrop-blur-sm border-border/20">
              <CardHeader className={cn("flex flex-row items-start space-x-4 pb-3", isRtl && "space-x-reverse text-right")}>
                <div className={cn("p-2 bg-primary/10 rounded-lg", isRtl ? "ml-3" : "mr-0")}>
                    <IconComponent className="h-8 w-8 text-primary" />
                </div>
                <div className="flex-1">
                  <h2 className={cn("text-xl font-semibold", fontClass)}>{title}</h2>
                </div>
              </CardHeader>
              <CardContent className={cn("flex-grow", fontClass, isRtl && "text-right")}>
                <p className="text-muted-foreground text-sm">{description}</p>
              </CardContent>
              <CardContent className={cn("pt-3", fontClass)}>
                <Link href={`/practices/${practice.slug}`} passHref>
                  <Button className="w-full mt-auto">
                    {content.learnMoreButton} <ArrowRight className={cn("h-4 w-4", isRtl ? "mr-2 transform rotate-180" : "ml-2")} />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}


    