
"use client";

import type { FC } from 'react';
import { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { SendHorizonal } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInputProps {
  onSendMessage: (messageText: string) => Promise<void> | void;
  placeholder?: string;
  isLoading?: boolean;
  className?: string;
}

const ChatInput: FC<ChatInputProps> = ({
  onSendMessage,
  placeholder = "Type your message...",
  isLoading = false,
  className,
}) => {
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() === '') return;
    
    const messageToSend = message.trim();
    setMessage(''); // Clear input immediately for better UX

    try {
      await onSendMessage(messageToSend);
    } catch (error) {
      console.error("Error sending message:", error);
      setMessage(messageToSend); // Restore message if sending failed
      // Optionally, show a toast or error message to the user
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as unknown as React.FormEvent); // Submit on Enter (not Shift+Enter)
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={cn("flex items-center gap-2 border-t bg-background px-3 py-2.5 sm:px-4 sm:py-3", className)}
    >
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="flex-1 resize-none border-input bg-background/80 shadow-sm focus-visible:ring-1 focus-visible:ring-primary/50 min-h-[40px] max-h-[120px] text-sm sm:text-base"
        rows={1}
        disabled={isLoading}
      />
      <Button type="submit" size="icon" disabled={isLoading || message.trim() === ''} aria-label="Send message">
        <SendHorizonal className="h-5 w-5" />
      </Button>
    </form>
  );
};

export default ChatInput;
