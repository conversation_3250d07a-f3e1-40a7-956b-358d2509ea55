import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { teachingsData, teachingCategories } from '@/data/teachings';
import { useLanguage } from '@/context/LanguageContext';

export default function TeachingsScreen() {
  const { language } = useLanguage();
  const navigation = useNavigation();

  const renderTeachingItem = ({ item }: { item: typeof teachingsData[0] }) => {
    const category = teachingCategories.find(cat => cat.id === item.categoryId);
    
    return (
      <TouchableOpacity
        style={styles.teachingItem}
        onPress={() => navigation.navigate('TeachingDetail' as never, { teachingSlug: item.slug } as never)}
      >
        <View style={styles.teachingContent}>
          <Text style={styles.categoryText}>{category?.name}</Text>
          <Text style={styles.teachingTitle}>{item.title}</Text>
          {item.shortDescription && (
            <Text style={styles.teachingDescription} numberOfLines={2}>
              {item.shortDescription}
            </Text>
          )}
        </View>
        <Ionicons name="chevron-forward" size={20} color="#64748b" />
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Teachings</Text>
        <Text style={styles.headerSubtitle}>Spiritual Guidance & Wisdom</Text>
      </View>
      
      <FlatList
        data={teachingsData}
        renderItem={renderTeachingItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  listContent: {
    padding: 16,
  },
  teachingItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  teachingContent: {
    flex: 1,
  },
  categoryText: {
    fontSize: 12,
    color: '#14b8a6',
    fontWeight: '500',
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  teachingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  teachingDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
});
