{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_FullWindowOverlayNativeComponent", "obj", "__esModule", "default", "NativeFullWindowOverlay", "FullWindowOverlayNativeComponent", "FullWindowOverlay", "props", "Platform", "OS", "console", "warn", "createElement", "View", "style", "position", "width", "height", "children", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/FullWindowOverlay.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,iCAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA0F,SAAAD,uBAAAI,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAD1F;;AAEA,MAAMG,uBAIL,GAAGC,yCAAuC;AAE3C,SAASC,iBAAiBA,CAACC,KAA8B,EAAE;EACzD,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzBC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACrE,oBAAOf,MAAA,CAAAO,OAAA,CAAAS,aAAA,CAACb,YAAA,CAAAc,IAAI,EAAKN,KAAQ,CAAC;EAC5B;EACA,oBACEX,MAAA,CAAAO,OAAA,CAAAS,aAAA,CAACR,uBAAuB;IACtBU,KAAK,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE,GAC9DV,KAAK,CAACW,QACgB,CAAC;AAE9B;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAjB,OAAA,GAEcG,iBAAiB"}