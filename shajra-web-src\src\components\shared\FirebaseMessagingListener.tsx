
"use client";

import { useEffect } from 'react';
import { messaging } from '@/lib/firebaseClient'; // Assuming firebaseClient exports initialized messaging
import { getToken, onMessage } from 'firebase/messaging';
import { useToast } from '@/hooks/use-toast';

const FirebaseMessagingListener = () => {
  const { toast } = useToast();

  const playNotificationSound = () => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      const audioContext = new window.AudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.type = 'sine'; // sine, square, sawtooth, triangle
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); // Volume (reduced a bit)

      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.2); // Play for 0.2 seconds
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window && messaging) {
      const requestPermissionAndToken = async () => {
        try {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            console.log('Notification permission granted.');
            const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;
            if (!vapidKey) {
              console.error('VAPID key is not defined. Ensure NEXT_PUBLIC_FIREBASE_VAPID_KEY is set.');
              return;
            }
            const currentToken = await getToken(messaging, { vapidKey: vapidKey });
            if (currentToken) {
              console.log('FCM Token:', currentToken);
              // TODO: Send this token to your server and store it for the user
              // e.g., await saveTokenToServer(currentToken);
            } else {
              console.log('No registration token available. Request permission to generate one.');
            }
          } else {
            console.log('Unable to get permission to notify.');
          }
        } catch (error) {
          console.error('An error occurred while retrieving token or requesting permission. ', error);
        }
      };

      requestPermissionAndToken();

      // Handle foreground messages
      const unsubscribeOnMessage = onMessage(messaging, (payload) => {
        console.log('Message received in foreground. ', payload);
        playNotificationSound();
        toast({
          title: payload.notification?.title || "New Notification",
          description: payload.notification?.body || "You have a new message.",
          // You can add actions here or make the toast clickable to navigate
        });
      });

      return () => {
        unsubscribeOnMessage(); // Unsubscribe when component unmounts
      };
    }
  }, [toast]); // Added toast to dependency array

  return null; // This component does not render anything itself
};

export default FirebaseMessagingListener;
