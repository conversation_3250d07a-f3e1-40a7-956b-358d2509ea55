'use server';

/**
 * @fileOverview Provides personalized spiritual practice recommendations based on user preferences and progress.
 *
 * - getPersonalizedPracticeRecommendations - A function that returns personalized practice recommendations.
 * - PersonalizedPracticeRecommendationsInput - The input type for the getPersonalizedPracticeRecommendations function.
 * - PersonalizedPracticeRecommendationsOutput - The return type for the getPersonalizedPracticeRecommendations function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const PersonalizedPracticeRecommendationsInputSchema = z.object({
  preferences: z
    .string()
    .describe('The user preferences for spiritual practices.'),
  progress: z.string().describe('The user progress in spiritual practices.'),
});
export type PersonalizedPracticeRecommendationsInput = z.infer<
  typeof PersonalizedPracticeRecommendationsInputSchema
>;

const PersonalizedPracticeRecommendationsOutputSchema = z.object({
  recommendations: z
    .string()
    .describe(
      'A list of personalized spiritual practice recommendations based on user preferences and progress, incorporating teachings from the Silsila Aaliya Chishtiya Sabiriya.'
    ),
});
export type PersonalizedPracticeRecommendationsOutput = z.infer<
  typeof PersonalizedPracticeRecommendationsOutputSchema
>;

export async function getPersonalizedPracticeRecommendations(
  input: PersonalizedPracticeRecommendationsInput
): Promise<PersonalizedPracticeRecommendationsOutput> {
  return personalizedPracticeRecommendationsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'personalizedPracticeRecommendationsPrompt',
  input: {schema: PersonalizedPracticeRecommendationsInputSchema},
  output: {schema: PersonalizedPracticeRecommendationsOutputSchema},
  prompt: `You are a spiritual guide specializing in the Silsila Aaliya Chishtiya Sabiriya teachings. Based on the user's preferences and progress, provide personalized recommendations for spiritual practices.

User Preferences: {{{preferences}}}
User Progress: {{{progress}}}

Recommendations:`, // The recommendations should reference teachings from the Silsila Aaliya Chishtiya Sabiriya.
});

const personalizedPracticeRecommendationsFlow = ai.defineFlow(
  {
    name: 'personalizedPracticeRecommendationsFlow',
    inputSchema: PersonalizedPracticeRecommendationsInputSchema,
    outputSchema: PersonalizedPracticeRecommendationsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
