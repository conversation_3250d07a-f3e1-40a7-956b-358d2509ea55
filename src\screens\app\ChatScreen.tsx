import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { useLanguage } from '../../context/LanguageContext';

interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: Date;
  isCurrentUser: boolean;
}

export default function ChatScreen() {
  const { user } = useAuth();
  const { language } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  // Mock initial message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: '1',
      text: 'Welcome to <PERSON><PERSON>h <PERSON>. How can we assist you in your spiritual journey today?',
      senderId: 'admin',
      timestamp: new Date(),
      isCurrentUser: false,
    };
    setMessages([welcomeMessage]);
  }, []);

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      senderId: 'user',
      timestamp: new Date(),
      isCurrentUser: true,
    };

    setMessages(prev => [...prev, newMessage]);
    const userQuestion = inputText.trim();
    setInputText('');
    setIsLoading(true);

    // Simple AI response system
    setTimeout(() => {
      const response = getAIResponse(userQuestion);
      const adminResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        senderId: 'ai-guide',
        timestamp: new Date(),
        isCurrentUser: false,
      };
      setMessages(prev => [...prev, adminResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const getAIResponse = (question: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('prayer') || lowerQuestion.includes('namaz') || lowerQuestion.includes('salah')) {
      return 'Prayer is the pillar of faith. Establish your five daily prayers with presence of heart. Remember Allah in your prayers and seek His guidance. Would you like to know about specific duas for prayer?';
    }

    if (lowerQuestion.includes('dua') || lowerQuestion.includes('supplication')) {
      return 'Duas are the essence of worship. Start with the duas from our collection - especially the morning and evening adhkar. Recite with understanding and presence of heart. Check our Duas section for specific supplications.';
    }

    if (lowerQuestion.includes('zikr') || lowerQuestion.includes('dhikr') || lowerQuestion.includes('remembrance')) {
      return 'Zikr is the food of the soul. Begin with "La ilaha illa Allah" and "Subhan Allah, Alhamdulillah, Allahu Akbar". Consistency is more important than quantity. Set aside time daily for remembrance of Allah.';
    }

    if (lowerQuestion.includes('spiritual') || lowerQuestion.includes('sufism') || lowerQuestion.includes('tasawwuf')) {
      return 'The spiritual path requires sincerity, patience, and guidance. Focus on purifying your heart, following the Sunnah, and seeking knowledge. Our Teachings section contains guidance from the Chishti Sabiri tradition.';
    }

    if (lowerQuestion.includes('guidance') || lowerQuestion.includes('help') || lowerQuestion.includes('advice')) {
      return 'May Allah guide you on the straight path. Remember: start with small, consistent acts of worship. Seek knowledge, purify your intentions, and maintain good character. What specific area would you like guidance on?';
    }

    // Default response
    return 'Assalamu Alaikum. Thank you for reaching out to Khanqah Sabiriya. May Allah bless you and guide you on your spiritual journey. Please feel free to ask about prayers, duas, zikr, or spiritual guidance. How can we assist you today?';
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <View style={[
      styles.messageContainer,
      item.isCurrentUser ? styles.userMessage : styles.adminMessage
    ]}>
      <Text style={[
        styles.messageText,
        item.isCurrentUser ? styles.userMessageText : styles.adminMessageText
      ]}>
        {item.text}
      </Text>
      <Text style={styles.timestamp}>
        {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Chat with Khanqah</Text>
        <Text style={styles.headerSubtitle}>Spiritual Guidance & Support</Text>
      </View>

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.messagesContainer}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
          showsVerticalScrollIndicator={false}
        />

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Type your message..."
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={!inputText.trim() || isLoading}
          >
            <Ionicons 
              name="send" 
              size={20} 
              color={inputText.trim() ? 'white' : '#94a3b8'} 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    marginBottom: 12,
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#14b8a6',
  },
  adminMessage: {
    alignSelf: 'flex-start',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: 'white',
  },
  adminMessageText: {
    color: '#1f2937',
  },
  timestamp: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 4,
    textAlign: 'right',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#14b8a6',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#e2e8f0',
  },
});
