import * as React from "react"
import {
  Text,
  StyleSheet,
  TextStyle,
  TextProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  foreground: '#0f172a',
}

export interface LabelProps extends TextProps {
  style?: TextStyle
  children?: React.ReactNode
}

const Label = React.forwardRef<Text, LabelProps>(
  ({ style, children, ...props }, ref) => {
    return (
      <Text
        ref={ref}
        style={[styles.label, style]}
        {...props}
      >
        {children}
      </Text>
    )
  }
)

Label.displayName = "Label"

const styles = StyleSheet.create({
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.foreground,
    lineHeight: 20,
    marginBottom: 4,
  },
})

export { Label }
