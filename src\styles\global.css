@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for React Native */
.glass-effect {
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-shadow {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Arabic and Urdu font styles */
.font-arabic {
  font-family: 'NotoSansArabic';
}

.font-urdu {
  font-family: 'NotoNastaliqUrdu';
}
