import * as React from "react"
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TextProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  card: '#ffffff',
  cardForeground: '#0f172a',
  border: '#e2e8f0',
  mutedForeground: '#64748b',
}

export interface CardProps extends ViewProps {
  style?: ViewStyle
  children?: React.ReactNode
}

export interface CardTextProps extends TextProps {
  style?: TextStyle
  children?: React.ReactNode
}

const Card = React.forwardRef<View, CardProps>(
  ({ style, children, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.card, style]}
      {...props}
    >
      {children}
    </View>
  )
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<View, CardProps>(
  ({ style, children, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.cardHeader, style]}
      {...props}
    >
      {children}
    </View>
  )
)
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<Text, CardTextProps>(
  ({ style, children, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.cardTitle, style]}
      {...props}
    >
      {children}
    </Text>
  )
)
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<Text, CardTextProps>(
  ({ style, children, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.cardDescription, style]}
      {...props}
    >
      {children}
    </Text>
  )
)
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<View, CardProps>(
  ({ style, children, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.cardContent, style]}
      {...props}
    >
      {children}
    </View>
  )
)
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<View, CardProps>(
  ({ style, children, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.cardFooter, style]}
      {...props}
    >
      {children}
    </View>
  )
)
CardFooter.displayName = "CardFooter"

const styles = StyleSheet.create({
  card: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    padding: 24,
    paddingBottom: 12,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.cardForeground,
    lineHeight: 24,
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.mutedForeground,
    lineHeight: 20,
  },
  cardContent: {
    padding: 24,
    paddingTop: 0,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 0,
  },
})

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
