
"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Language } from "@/context/LanguageContext";

interface HazriInitialPopupProps {
  isOpen: boolean;
  onClose: () => void;
  message: string;
  language: Language;
}

const popupVariants = {
  hidden: { opacity: 0, y: "-10%", scale: 0.9, rotateX: -20 },
  visible: { opacity: 1, y: "0%", scale: 1, rotateX: 0, transition: { duration: 0.5, ease: [0.16, 1, 0.3, 1] } },
  exit: { opacity: 0, y: "10%", scale: 0.9, rotateX: 20, transition: { duration: 0.3, ease: [0.16, 1, 0.3, 1] } }
};

const salutations: Record<Language, string> = {
  en: "Assalamu <PERSON>.",
  ur: "السلام علیکم۔",
  ro: "Assalamu <PERSON>.",
  hi: "अस्सलामु अलैकुम।",
  ar: "السلام عليكم.",
};

const dialogTitles: Record<Language, string> = {
  en: "Notification",
  ur: "اطلاع",
  ro: "Itla'",
  hi: "इत्तिला", // Transliterated from Urdu
  ar: "إشعار",
};

const HazriInitialPopup: React.FC<HazriInitialPopupProps> = ({ isOpen, onClose, message, language }) => {
  const isRtl = language === 'ar' || language === 'ur';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const salutation = salutations[language] || salutations.en;
  const currentDialogTitle = dialogTitles[language] || dialogTitles.en;

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog open={isOpen} onOpenChange={(open) => { if (!open) onClose(); }}>
          <DialogContent
            className={cn(
              "w-[90vw] max-w-xs text-center p-4 glass-effect rounded-lg",
              fontClass
            )}
            dir={isRtl ? "rtl" : "ltr"}
            lang={language}
          >
            <motion.div
              variants={popupVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <DialogHeader className="items-center">
                <CheckCircle className="h-8 w-8 sm:h-10 sm:w-10 text-green-500 mb-2" />
                <DialogTitle className={cn("text-base sm:text-lg font-semibold text-foreground", fontClass)}>
                  {currentDialogTitle}
                </DialogTitle>
              </DialogHeader>
              <DialogDescription className={cn("text-xs sm:text-sm text-muted-foreground mt-1", fontClass)}>
                {salutation} {message}
              </DialogDescription>
            </motion.div>
          </DialogContent>
        </Dialog>
      )}
    </AnimatePresence>
  );
};

export default HazriInitialPopup;

    