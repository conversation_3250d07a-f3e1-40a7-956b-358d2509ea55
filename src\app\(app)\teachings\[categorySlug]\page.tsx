
import { 
  teachingCategories as mockTeachingCategories, 
  teachingsData as mockTeachingsData, 
  type Teaching as MockTeaching, 
  type TeachingCategory as MockTeachingCategory 
} from '@/data/teachings'; // Simulating Firestore
import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card'; // Removed CardTitle
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, List } from 'lucide-react';
import { notFound } from 'next/navigation';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';

// Define the expected structure, mapping from mock data
type TeachingUrdu = {
  id: string;
  slug: string; // Keep slug for routing to detail page
  advice_title_urdu: string; // This will be mapped from title
  shortDescription?: string;
  category_slug: string; // For linking back or context
};

type CategoryUrdu = {
  id: string;
  slug: string;
  category_urdu: string;
  description: string;
};

// Simulate fetching category details and its teachings
const getCategoryAndTeachings = async (categorySlug: string): Promise<{ category: CategoryUrdu | null; teachings: TeachingUrdu[] }> => {
  // Simulate API delay
  // await new Promise(resolve => setTimeout(resolve, 1000));

  const categoryMock = mockTeachingCategories.find(c => c.slug === categorySlug);
  if (!categoryMock) {
    return { category: null, teachings: [] };
  }

  const category: CategoryUrdu = {
    id: categoryMock.id,
    slug: categoryMock.slug,
    category_urdu: categoryMock.name, // Using name as stand-in for category_urdu
    description: categoryMock.description,
  };

  const teachingsMock = mockTeachingsData.filter(t => t.categoryId === categoryMock.id);
  const teachings: TeachingUrdu[] = teachingsMock.map(t => ({
    id: t.id,
    slug: t.slug,
    advice_title_urdu: t.title, // Using title as stand-in for advice_title_urdu
    shortDescription: t.shortDescription,
    category_slug: categorySlug,
  }));

  return { category, teachings };
};


export default async function TeachingsCategoryPage({ params }: { params: { categorySlug: string } }) {
  const { categorySlug } = params;
  
  let category: CategoryUrdu | null = null;
  let categoryTeachings: TeachingUrdu[] = [];
  let fetchError: string | null = null;
  let isLoading = true;

  try {
    const data = await getCategoryAndTeachings(categorySlug);
    category = data.category;
    categoryTeachings = data.teachings;
    isLoading = false;
  } catch (error) {
    console.error(`Error fetching teachings for category ${categorySlug}:`, error);
    fetchError = "Failed to load teachings. Please try again later.";
    isLoading = false;
  }

  if (!isLoading && !fetchError && !category) {
    notFound();
  }

  return (
    <div className="space-y-8">
      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <PageTitle title="Loading Teachings..." />
          <LoadingSpinner size={48} />
        </div>
      )}

      {fetchError && !isLoading && (
        <>
         <PageTitle title="Error" />
         <ErrorMessage message={fetchError} />
         <Link href="/teachings" passHref>
            <Button variant="outline" className="mt-4">
              <ArrowRight className="h-4 w-4 mr-1 transform rotate-180" /> Back to Categories
            </Button>
          </Link>
        </>
      )}

      {!isLoading && !fetchError && category && (
        <>
          <div className="px-0"> {/* Replaced CardHeader with div for PageTitle */}
            <Link href="/teachings" className="text-sm text-primary hover:underline mb-2 inline-flex items-center">
              <ArrowRight className="h-4 w-4 mr-1 transform rotate-180" /> Back to Categories
            </Link>
            {/* Displaying category_urdu */}
            <PageTitle title={category.category_urdu} subtitle={category.description} />
          </div>

          {categoryTeachings.length > 0 ? (
            <div className="space-y-4">
              {categoryTeachings.map((teaching: TeachingUrdu) => (
                <Card key={teaching.id} className="shadow-md hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    {/* Displaying advice_title_urdu */}
                    <h2 className="text-xl mb-2 font-arabic" lang="ur" dir="rtl">{teaching.advice_title_urdu}</h2>
                    {teaching.shortDescription && (
                      <CardDescription className="mb-4">{teaching.shortDescription}</CardDescription>
                    )}
                    {/* Link uses existing categorySlug and teaching.slug (mapped to teachingId in prompt) */}
                    <Link href={`/teachings/${categorySlug}/${teaching.slug}`} passHref>
                      <Button variant="outline">
                        Read Full Teaching <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <List className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No teachings found in this category yet.</p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
