
"use client";

import Image from 'next/image'; 
import Link from 'next/link';
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { User, LogIn, Heart, Languages } from "lucide-react"; // Removed Phone
import { LanguageSelector } from "@/components/shared/LanguageSelector";
import { useLanguage, type Language } from "@/context/LanguageContext";
import { cn } from "@/lib/utils";
import { SidebarTrigger } from '@/components/ui/sidebar';

const donateButtonTranslations: Record<Language, string> = {
  en: "Donate",
  ur: "عطیہ دیں",
  ro: "Atiya Dein",
  hi: "अतिया दें",
  ar: "تبرع",
};

const appTitleTranslations: Record<Language, string> = {
  en: "Sabiriya",
  ur: "صابریہ",
  ro: "Sabiriya",
  hi: "साबिरिया",
  ar: "صابرية",
};

export function AppHeader() {
  const { user, loading } = useAuth();
  const { language } = useLanguage();

  const donateText = donateButtonTranslations[language] || donateButtonTranslations.en;
  const currentAppTitle = appTitleTranslations[language] || appTitleTranslations.en;

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-2 sm:gap-4 border-b bg-card px-4 md:px-6 shadow-sm">
      <SidebarTrigger className="md:hidden" />
      <Link href="/home" className="flex items-center gap-2 text-lg font-semibold text-foreground hover:text-primary transition-colors">
        <Image
          src="/app-icon.png" 
          alt="Sabiriya App Icon"
          width={28} 
          height={28}
          data-ai-hint="app icon dome calligraphy"
        />
        <span className={cn("font-display text-primary", (language === 'ur' || language === 'ar') && "font-arabic")}>{currentAppTitle}</span>
      </Link>
      <div className="ml-auto flex items-center gap-2 sm:gap-3">
        <a
          href="https://sawadeazam.org/pe/"
          target="_blank"
          rel="noopener noreferrer"
          aria-label={donateText}
        >
          <Button
            variant="default"
            size="sm"
            className={cn(
              "font-semibold shadow-md hover:shadow-lg transition-all duration-300 ease-in-out",
              "hover:-translate-y-0.5 active:translate-y-px",
              (language === 'ur' || language === 'ar') && "font-arabic text-base px-3",
              language === 'hi' && "font-sans text-base px-3"
            )}
          >
            <Heart className={cn("h-4 w-4", (language === 'ur' || language === 'ar' || language === 'hi') ? 'ml-1' : 'mr-1')} />
            {donateText}
          </Button>
        </a>
        <LanguageSelector buttonClassName="hover:bg-accent/80 hover:border-primary/50 hover:shadow-[0_0_10px_hsl(var(--primary)_/_0.5)]" />
        {!loading && user ? (
          <Link href="/profile" passHref>
            <Button variant="outline" size="icon" aria-label="Profile" className="hover:bg-accent/80 hover:border-primary/50">
              <User className="h-5 w-5" />
              <span className="sr-only">Profile</span>
            </Button>
          </Link>
        ) : !loading && !user ? (
          <Link href="/login" passHref>
            <Button variant="outline" size="sm" className="hover:bg-accent/80 hover:border-primary/50">
              <LogIn className="h-5 w-5 mr-0 sm:mr-2" />
              <span className="hidden sm:inline">Sign In</span>
            </Button>
          </Link>
        ) : null}
      </div>
    </header>
  );
}
