
"use client";

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlayCircle, Clock, Info, ListChecks } from 'lucide-react';
import PageTitle from '@/components/shared/PageTitle';
import { cn } from '@/lib/utils';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { useState } from 'react';
import GuidedSessionTimer from '@/components/shared/GuidedSessionTimer';

type PaasAnfaasContent = {
  pageTitle: string;
  pageSubtitle: string;
  backToPractices: string;
  definitionHeading: string;
  definitionText: string;
  methodHeading: string;
  methodSteps: string[];
  durationHeading: string;
  durationInfo: string;
  guidedSessionButton: string;
  timerPracticeName: string;
  timerPrompts: { inhale: string; exhale: string };
  timerButtonTexts: { start: string; pause: string; resume: string; reset: string };
};

const translations: Record<Language, PaasAnfaasContent> = {
  en: {
    pageTitle: "Paas Anfaas (Awareness of Breath)",
    pageSubtitle: "The Practice of Guarding the Breaths",
    backToPractices: "Back to Spiritual Practices",
    definitionHeading: "Definition",
    definitionText: "Paas Anfaas, literally 'guarding the breaths,' is a spiritual exercise in which the breath is synchronized with the remembrance of Allah. It is significant in both the Chishti and Qadiri orders as it aids in controlling the self (nafs) and directing the heart towards Allah.",
    methodHeading: "Method",
    methodSteps: [
      "Sit in a quiet and clean place, and focus on your breaths.",
      "When you inhale, conceptualize 'Allah' from the heart, and when you exhale, conceptualize 'Hu' (or 'Hoo'), as if every breath is being illuminated by the essence of Allah's name.",
      "Continue this practice for at least ten to fifteen minutes, and keep the heart free from worldly thoughts."
    ],
    durationHeading: "Duration",
    durationInfo: "Beginners can start with 10-15 minutes per session, once or twice a day. Gradually, as concentration improves, the duration can be increased. The ultimate goal is to maintain this awareness throughout all activities.",
    guidedSessionButton: "Start 10-minute Guided Session",
    timerPracticeName: "Paas Anfaas Session",
    timerPrompts: { inhale: "Inhale: Allah", exhale: "Exhale: Hu" },
    timerButtonTexts: { start: "Start", pause: "Pause", resume: "Resume", reset: "Reset"},
  },
  ur: {
    pageTitle: "پاسِ انفاس",
    pageSubtitle: "سانسوں کی پاسبانی کا عمل",
    backToPractices: "روحانی اعمال کی طرف واپس",
    definitionHeading: "تعریف",
    definitionText: "پاسِ انفاس ایک ایسی روحانی مشق ہے جس میں سانس کو ذکرِ الٰہی سے ہم آہنگ کیا جاتا ہے۔ یہ سلسلہ چشتیہ اور قادریہ دونوں میں اہم ہے، کیونکہ یہ نفس کو قابو میں رکھنے اور قلب کو اللہ کی طرف متوجہ کرنے کا ذریعہ ہے۔",
    methodHeading: "کیفیت",
    methodSteps: [
      "خاموش اور پاکیزہ جگہ پر بیٹھیں، اور اپنی سانسوں پر توجہ دیں۔",
      "جب سانس اندر لیں تو قلب سے \"اللہ\" کا تصور کریں، اور جب سانس باہر نکالیں تو \"ہو\" کا تصور کریں، گویا ہر سانس اللہ کے اسمِ ذات سے منور ہو رہی ہے۔",
      "یہ عمل کم از کم دس سے پندرہ منٹ تک جاری رکھیں، اور قلب کو دنیاوی خیالات سے آزاد رکھیں۔"
    ],
    durationHeading: "دورانیہ",
    durationInfo: "مبتدی حضرات دن میں ایک یا دو بار 10-15 منٹ کے سیشن سے آغاز کر سکتے ہیں۔ آہستہ آہستہ، جیسے جیسے توجہ بہتر ہوتی جائے، دورانیہ بڑھایا جا سکتا ہے۔ حتمی مقصد تمام سرگرمیوں کے دوران اس آگاہی کو برقرار رکھنا ہے۔",
    guidedSessionButton: "10 منٹ کا گائیڈڈ سیشن شروع کریں",
    timerPracticeName: "پاسِ انفاس سیشن",
    timerPrompts: { inhale: "سانس اندر: اللہ", exhale: "سانس باہر: ھو" },
    timerButtonTexts: { start: "شروع کریں", pause: "وقفہ", resume: "جاری رکھیں", reset: "دوبارہ شروع کریں" },
  },
  ro: {
    pageTitle: "Paas-e-Anfaas",
    pageSubtitle: "Saanson ki paasbaani ka amal",
    backToPractices: "Roohaani Amaal ki Taraf Wapas",
    definitionHeading: "Ta'reef",
    definitionText: "Paas-e-Anfaas ek aisi roohaani mashq hai jis mein saans ko Zikr-e-Ilaahi se hum-aahang kiya jaata hai. Yeh Silsila Chishtiya aur Qadiriya donon mein aham hai, kyunkay yeh nafs ko qaabu mein rakhne aur qalb ko Allah ki taraf mutawajjah karne ka zariya hai.",
    methodHeading: "Kaifiyat",
    methodSteps: [
      "Khaamosh aur pakeezah jagah par baithein, aur apni saanson par tawajjuh dein.",
      "Jab saans andar lein toh qalb se \"Allah\" ka tasawwur karein, aur jab saans baahar nikaalein toh \"Hu\" (or \"Hoo\") ka tasawwur karein, goya har saans Allah ke Ism-e-Zaat se munawwar ho rahi hai.",
      "Yeh amal kam az kam das se pandrah minute tak jaari rakhein, aur qalb ko duniyavi khayalaat se aazaad rakhein."
    ],
    durationHeading: "Dauraniya",
    durationInfo: "Mubtadi hazraat din mein ek ya do baar 10-15 minute ke session se aaghaaz kar sakte hain. Aahista aahista, jaise jaise tawajjuh behtar hoti jaaye, dauraniya barhaya ja sakta hai. Hatmi maqsad tamaam sargarmiyon ke dauraan is aagahi ko barqaraar rakhna hai.",
    guidedSessionButton: "10 Minute ka Guided Session Shuru Karein",
    timerPracticeName: "Paas Anfaas Session",
    timerPrompts: { inhale: "Saans Andar: Allah", exhale: "Saans Baahar: Hu" },
    timerButtonTexts: { start: "Shuru Karein", pause: "Waqfa", resume: "Jaari Rakhein", reset: "Dobara Shuru Karein" },
  },
  hi: {
    pageTitle: "पास-ए-अनफ़ास",
    pageSubtitle: "साँसों की पासबानी का अमल",
    backToPractices: "रूहानी आमाल की तरफ़ वापस",
    definitionHeading: "तारीफ़",
    definitionText: "पास-ए-अनफ़ास एक ऐसी रूहानी मश्क़ है जिसमें साँस को ज़िक्र-ए-इलाही से हम-आहंग किया जाता है। यह सिलसिला चिश्तिया और क़ादिरिया दोनों में अहम है, क्यूंकि यह नफ़्स को क़ाबू में रखने और क़ल्ब को अल्लाह की तरफ़ मुतवज्जह करने का ज़रिया है।",
    methodHeading: "कैफ़ियत",
    methodSteps: [
      "ख़ामोश और पाकीज़ा जगह पर बैठें, और अपनी साँसों पर तवज्जो दें।",
      "जब साँस अंदर लें तो क़ल्ब से \"अल्लाह\" का तसव्वुर करें, और जब साँस बाहर निकालें तो \"हू\" (या \"हू\") का तसव्वुर करें, गोया हर साँस अल्लाह के इस्म-ए-ज़ात से मुनव्वर हो रही है।",
      "यह अमल कम अज़ कम दस से पंद्रह मिनट तक जारी रखें, और क़ल्ब को दुनियावी ख़यालात से आज़ाद रखें।"
    ],
    durationHeading: "दौरानिया",
    durationInfo: "मुब्तदी हज़रात दिन में एक या दो बार 10-15 मिनट के सेशन से आग़ाज़ कर सकते हैं। आहिस्ता-आहिस्ता, जैसे-जैसे तवज्जो बेहतर होती जाए, दौरानिया बढ़ाया जा सकता है। हत्मी मक़सद तमाम सरगर्मियों के दौरान इस आगाही को बरक़रार रखना है।",
    guidedSessionButton: "10 मिनट का गाइडेड सेशन शुरू करें",
    timerPracticeName: "पास अनफ़ास सेशन",
    timerPrompts: { inhale: "साँस अंदर: अल्लाह", exhale: "साँس باہر: हू" }, // Note: "saans bahar"
    timerButtonTexts: { start: "शुरू करें", pause: "विराम", resume: "जारी रखें", reset: "दोबारा शुरू करें" },
  },
  ar: {
    pageTitle: "مراقبة الأنفاس",
    pageSubtitle: "تمرين حراسة الأنفاس في الذكر",
    backToPractices: "العودة إلى الممارسات الروحية",
    definitionHeading: "تعريف",
    definitionText: "مراقبة الأنفاس هي ممارسة روحية يتم فيها مزامنة التنفس مع ذكر الله. هذه الممارسة مهمة في الطريقتين الجشتية والقادرية، حيث تساعد في السيطرة على النفس وتوجيه القلب نحو الله.",
    methodHeading: "الكيفية",
    methodSteps: [
      "اجلس في مكان هادئ ونظيف، وركز على أنفاسك.",
      "عندما تستنشق، تصور كلمة \"الله\" من القلب، وعندما تزفر، تصور كلمة \"هو\"، كأن كل نفس يتنور بجوهر اسم الله.",
      "استمر في هذه الممارسة لمدة عشر إلى خمس عشرة دقيقة على الأقل، وحافظ على خلو القلب من الأفكار الدنيوية."
    ],
    durationHeading: "المدة",
    durationInfo: "يمكن للمبتدئين البدء بـ 10-15 دقيقة لكل جلسة، مرة أو مرتين في اليوم. تدريجيًا، مع تحسن التركيز، يمكن زيادة المدة. الهدف النهائي هو الحفاظ على هذا الوعي طوال جميع الأنشطة.",
    guidedSessionButton: "ابدأ جلسة موجهة لمدة 10 دقائق",
    timerPracticeName: "جلسة مراقبة الأنفاس",
    timerPrompts: { inhale: "شهيق: الله", exhale: "زفير: هو" },
    timerButtonTexts: { start: "ابدأ", pause: "إيقاف مؤقت", resume: "استئناف", reset: "إعادة تعيين" },
  },
};


export default function PaasAnfaasPage() {
  const { language } = useLanguage();
  const [showTimer, setShowTimer] = useState(false);

  const content = translations[language] || translations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';
  
  return (
    <div className="space-y-8">
      <div className={cn("px-0", isRtl && "text-right")}>
        <PageTitle 
          title={content.pageTitle} 
          subtitle={content.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      {!showTimer ? (
        <>
          <Card className="shadow-xl bg-card">
            <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
              <h2 className={cn("text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                <Info className={cn("h-6 w-6", textDir === 'rtl' ? "ml-2" : "mr-2")} />
                {content.definitionHeading}
              </h2>
            </CardHeader>
            <CardContent lang={language} dir={textDir} className={cn("space-y-4 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}>
              <p>{content.definitionText}</p>
            </CardContent>
          </Card>

          <Card className="shadow-xl bg-card">
            <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
              <h2 className={cn("text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                <ListChecks className={cn("h-6 w-6", textDir === 'rtl' ? "ml-2" : "mr-2")} />
                {content.methodHeading}
              </h2>
            </CardHeader>
            <CardContent lang={language} dir={textDir} className={cn("space-y-4 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}>
              <ul className={cn("list-inside space-y-3", isRtl ? "list-decimal pr-5" : "list-disc pl-5", language === 'hi' && "list-decimal pl-5")}>
                {content.methodSteps.map((step, index) => (
                  <li key={index} dangerouslySetInnerHTML={{ 
                      __html: 
                        language === 'ur' ? step.replace(/"اللہ"/g, '<strong class="text-primary">اللہ</strong>').replace(/"ہو"/g, '<strong class="text-primary">ہو</strong>') :
                        language === 'ar' ? step.replace(/"الله"/g, '<strong class="text-primary">الله</strong>').replace(/"هو"/g, '<strong class="text-primary">هو</strong>') :
                        language === 'hi' ? step.replace(/"अल्लाह"/g, '<strong class="text-primary">अल्लाह</strong>').replace(/"हू"/g, '<strong class="text-primary">हू</strong>') :
                        step.replace(/"Allah"/g, '<strong class="text-primary">Allah</strong>').replace(/"Hu"/g, '<strong class="text-primary">Hu</strong>').replace(/"Hoo"/g, '<strong class="text-primary">Hoo</strong>')
                    }} />
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="shadow-xl bg-card">
            <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
              <h2 className={cn("text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                <Clock className={cn("h-6 w-6", textDir === 'rtl' ? "ml-2" : "mr-2")} />
                {content.durationHeading}
              </h2>
            </CardHeader>
            <CardContent lang={language} dir={textDir} className={cn("text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}>
              <p>{content.durationInfo}</p>
            </CardContent>
          </Card>
          
          <div className="text-center pt-4">
            <Button size="lg" onClick={() => setShowTimer(true)} className={cn(fontClass)}>
              <PlayCircle className="h-5 w-5 mr-2" /> {content.guidedSessionButton}
            </Button>
          </div>
        </>
      ) : (
        <GuidedSessionTimer 
          durationInMinutes={10} 
          prompts={content.timerPrompts}
          practiceName={content.timerPracticeName}
          buttonTexts={content.timerButtonTexts}
          onClose={() => setShowTimer(false)} 
          onSessionEnd={() => {
            // Optionally, do something when session ends
          }}
        />
      )}
    </div>
  );
}

    

    

    