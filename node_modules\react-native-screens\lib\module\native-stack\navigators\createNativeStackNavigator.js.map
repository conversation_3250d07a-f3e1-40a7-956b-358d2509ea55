{"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "NativeStackNavigator", "_ref", "initialRouteName", "children", "screenOptions", "rest", "state", "descriptors", "navigation", "useEffect", "dangerouslyGetParent", "undefined", "console", "warn", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key", "createElement", "_extends"], "sourceRoot": "../../../../src", "sources": ["native-stack/navigators/createNativeStackNavigator.tsx"], "mappings": ";AAAA,SACEA,sBAAsB,EAEtBC,YAAY,EAGZC,WAAW,EAGXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAM9B,OAAOC,eAAe,MAAM,0BAA0B;AAEtD,SAASC,oBAAoBA,CAAAC,IAAA,EAKC;EAAA,IALA;IAC5BC,gBAAgB;IAChBC,QAAQ;IACRC,aAAa;IACb,GAAGC;EACsB,CAAC,GAAAJ,IAAA;EAC1B,MAAM;IAAEK,KAAK;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAGX,oBAAoB,CAM7DD,WAAW,EAAE;IACbM,gBAAgB;IAChBC,QAAQ;IACRC;EACF,CAAC,CAAC;;EAEF;EACA;EACAN,KAAK,CAACW,SAAS,CAAC,MAAM;IACpB;IACA,IAAID,UAAU,EAAEE,oBAAoB,KAAKC,SAAS,EAAE;MAClDC,OAAO,CAACC,IAAI,CACV,2LACF,CAAC;IACH;EACF,CAAC,EAAE,CAACL,UAAU,CAAC,CAAC;EAEhBV,KAAK,CAACW,SAAS,CACb;EACE;EACCD,UAAU,EAA+CM,WAAW,GACnE,UAAU,EACTC,CAAM,IAAK;IACV,MAAMC,SAAS,GAAGR,UAAU,CAACQ,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACEX,KAAK,CAACY,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;QACA;QACA;QACAX,UAAU,CAACY,QAAQ,CAAC;UAClB,GAAGzB,YAAY,CAAC0B,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEhB,KAAK,CAACiB;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CACF,CAAC,EACH,CAACf,UAAU,EAAEF,KAAK,CAACY,KAAK,EAAEZ,KAAK,CAACiB,GAAG,CACrC,CAAC;EAED,oBACEzB,KAAA,CAAA0B,aAAA,CAACzB,eAAe,EAAA0B,QAAA,KACVpB,IAAI;IACRC,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN;AAEA,eAAeb,sBAAsB,CAKnCM,oBAAoB,CAAC"}