
"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import GuidedSessionTimer from '@/components/shared/GuidedSessionTimer';
import PageTitle from '@/components/shared/PageTitle';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import { CheckCircle2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

const salutations: Record<Language, string> = {
  en: "Assalamu Alaikum.",
  ur: "السلام علیکم۔",
  ro: "Assalamu Alaikum.",
  hi: "अस्सलामु अलैकुम।",
  ar: "السلام عليكم.",
};

const translations = {
  pageTitle: {
    en: "Attendance Confirmed",
    ur: "آپ کی حاضری لگ گئی",
    ro: "Aap Ki <PERSON>",
    hi: "आपकी हाज़िरी लग गई",
    ar: "تم تأكيد الحضور",
  },
  cardTitleMain: {
    en: "Your Attendance is Marked!",
    ur: "آپ کی حاضری لگا دی گئی ہے!",
    ro: "Aap Ki Hazri Laga Di Gayi Hai!",
    hi: "आपकी हाज़िरी लगा दी गई है!",
    ar: "تم تسجيل حضورك بنجاح!",
  },
  instructions: {
    en: "Apply fragrance, face the Qibla, sit down, recite Durood Sharif, sincerely repent to your Lord (Tawbah & Istighfar) and seek forgiveness for your sins. Then, for approximately 20 minutes, perform the Zikr of Allahu Hoo (Paas Anfaas) through your breaths. You will be notified upon completion of your Hazri, Insha'Allah.",
    ur: "خوشبو لگا کر قبلہ کی جانب چہرہ کر کے بیٹھ جائیں اور درود شریف پڑھ کر اپنے رب کی بارگاہ میں سچے دل سے توبہ استغفار کریں اپنے گناہوں کی معافی طلب کریں پھر تقریباً ۲۰ منٹ سانسوں کے ذریعے اللهُ ھُو (پاس انفاس) کا ذکر کریں۔ آپ کی حاضری مکمل ہونے پر آپ کو ان شاء الله اطلاع کر دی جائے گی۔",
    ro: "Khushbu laga kar Qibla ki jaanib rukh kar ke baith jaayein aur Durood Sharif padh kar apne Rabb ki baargah mein sachche dil se Tauba o Istighfaar karein aur apne gunahon ki maafi talab karein. Phir taqreeban 20 minute tak saanson ke zariye Allahu Hoo (Paas Anfaas) ka zikr karein. Aap ki hazri mukammal hone par aap ko Insha'Allah ittela kar di jaayegi.",
    hi: "ख़ुशबू लगाकर क़िबला की जानिब रुख़ करके बैठ जाएँ और दरूद शरीफ़ पढ़कर अपने रब्ब की बारगाह में सच्चे दिल से तौबा ओ इस्तिग़फ़ार करें और अपने गुनाहों की माफ़ी तलब करें। फिर तक़रीबन २० मिनट तक साँसों के ज़रिए अल्लाहु हू (पास अनफ़ास) का ज़िक्र करें। आपकी हाज़िरी मुकम्मल होने पर आपको इंशा'अल्लाह इत्तिला कर दी जाएगी।",
    ar: "ضع العطر، واستقبل القبلة، واجلس، ثم اقرأ الدرود الشريف وتب إلى ربك بصدق واستغفر واطلب المغفرة لذنوبك. بعد ذلك، لمدة ٢٠ دقيقة تقريبًا، قم بذكر الله هو (مراقبة الأنفاس) من خلال أنفاسك. سيتم إعلامك عند اكتمال حضورك، إن شاء الله.",
  },
  timerPracticeName: {
    en: "Hazri Session (Paas Anfaas)",
    ur: "حاضری سیشن (پاس انفاس)",
    ro: "Hazri Session (Paas Anfaas)",
    hi: "हाज़िरी सेशन (पास अनफ़ास)",
    ar: "جلسة الحضور (مراقبة الأنفاس)",
  },
  timerPrompts: {
    en: { inhale: "Inhale: Allah", exhale: "Exhale: Hu" },
    ur: { inhale: "سانس اندر: اللہ", exhale: "سانس باہر: ھو" },
    ro: { inhale: "Saans Andar: Allah", exhale: "Saans Baahar: Hu" },
    hi: { inhale: "साँस अंदर: अल्लाह", exhale: "साँस बाहर: हू" },
    ar: { inhale: "شهيق: الله", exhale: "زفير: هو" },
  },
  timerButtonTexts: {
    start: { en: "Start", ur: "شروع کریں", ro: "Shuru Karein", hi: "शुरू करें", ar: "ابدأ" },
    pause: { en: "Pause", ur: "وقفہ", ro: "Waqfa", hi: "विराम", ar: "إيقاف مؤقت" },
    resume: { en: "Resume", ur: "جاری رکھیں", ro: "Jaari Rakhein", hi: "जारी रखें", ar: "استئناف" },
    reset: { en: "Reset", ur: "دوبارہ شروع کریں", ro: "Dobara Shuru Karein", hi: "दोबारा शुरू करें", ar: "إعادة تعيين" },
  },
  sessionCompleteNotification: {
    title: {
        en: "Hazri Complete",
        ur: "حاضری مکمل",
        ro: "Hazri Mukammal",
        hi: "हाज़िरी मुकम्मल",
        ar: "اكتمل الحضور",
    },
    body: {
        en: "Your attendance for today is complete. You may now get up if you wish, or you can continue your Zikr for as long as you like.",
        ur: "آج کی آپ کی حاضری مکمل ہو گئی ہے۔ اگر اٹھنا چاہیں تو اب اٹھ سکتے ہیں یا ذکر کرتے رہنا چاہتے ہیں تو جب تک مرضی ہو کر سکتے ہیں۔",
        ro: "Aaj ki aap ki hazri mukammal ho gayi hai. Agar uthna chahein toh ab uth sakte hain ya Zikr karte rehna chahte hain toh jab tak marzi ho kar sakte hain.",
        hi: "आपकी आज की हाज़िरी मुकम्मल हो गई है। अगर उठना चाहें तो अब उठ सकते हैं या ज़िक्र करते रहना चाहते हैं तो जब तक मर्ज़ी हो कर सकते हैं।",
        ar: "قد اكتمل حضوركم لهذا اليوم. يمكنكم الآن القيام إن شئتم، أو يمكنكم مواصلة الذكر ما شئتم.",
    }
  }
};


export default function HazriConfirmedPage() {
  const { language } = useLanguage();
  const router = useRouter();
  const { toast } = useToast();

  const salutation = salutations[language] || salutations.en;
  const pageTitleText = translations.pageTitle[language] || translations.pageTitle.en;
  const cardTitleMainText = translations.cardTitleMain[language] || translations.cardTitleMain.en;
  const instructionsText = translations.instructions[language] || translations.instructions.en;
  const timerPracticeNameText = translations.timerPracticeName[language] || translations.timerPracticeName.en;
  const currentTimerPrompts = translations.timerPrompts[language] || translations.timerPrompts.en;
  const currentTimerButtonTexts = {
    start: translations.timerButtonTexts.start[language] || translations.timerButtonTexts.start.en,
    pause: translations.timerButtonTexts.pause[language] || translations.timerButtonTexts.pause.en,
    resume: translations.timerButtonTexts.resume[language] || translations.timerButtonTexts.resume.en,
    reset: translations.timerButtonTexts.reset[language] || translations.timerButtonTexts.reset.en,
  };
  const notificationSalutation = salutations[language] || salutations.en;
  const notificationTitle = translations.sessionCompleteNotification.title[language] || translations.sessionCompleteNotification.title.en;
  const notificationBody = translations.sessionCompleteNotification.body[language] || translations.sessionCompleteNotification.body.en;
  const sessionCompleteNotificationMessage = `${notificationSalutation} ${notificationBody}`;


  const playNotificationSound = () => {
    if (typeof window !== 'undefined' && window.AudioContext) {
      const audioContext = new window.AudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); 
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); 

      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.2); 
    }
  };

  const showCompletionNotification = async () => {
    if (!('Notification' in window)) {
      console.log('This browser does not support desktop notification');
      toast({
        title: notificationTitle,
        description: sessionCompleteNotificationMessage,
        duration: 15000, 
      });
      playNotificationSound();
      return;
    }

    if (Notification.permission === 'granted') {
      new Notification(notificationTitle, { body: sessionCompleteNotificationMessage, icon: '/icons/icon-192x192.png' }); 
      playNotificationSound();
    } else if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        new Notification(notificationTitle, { body: sessionCompleteNotificationMessage, icon: '/icons/icon-192x192.png' });
        playNotificationSound();
      } else {
        toast({
          title: notificationTitle,
          description: sessionCompleteNotificationMessage,
          duration: 15000,
        });
        playNotificationSound();
      }
    } else {
      toast({
        title: notificationTitle,
        description: sessionCompleteNotificationMessage,
        duration: 15000,
      });
      playNotificationSound();
    }
  };


  const handleSessionEnd = async () => {
    await showCompletionNotification();
    router.push('/hazri-complete');
  };

  const isRtl = language === 'ar' || language === 'ur';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  return (
    <div className={cn("space-y-4 sm:space-y-6 flex flex-col items-center justify-center min-h-[calc(100vh-var(--header-height,4rem)-var(--footer-height,0rem)-2rem)] p-4", fontClass)} lang={language} dir={isRtl ? "rtl" : "ltr"}>
      <PageTitle title={pageTitleText} className={cn("text-center mb-2 sm:mb-4", fontClass)} />

      <Card className={cn("w-full max-w-xs sm:max-w-md shadow-xl text-center glass-effect", fontClass)}>
        <CardHeader className="items-center pt-4 sm:pt-6 pb-2 sm:pb-3">
          <CheckCircle2 className="h-10 w-10 sm:h-12 sm:w-12 text-green-500 mb-1 sm:mb-2" />
          <CardTitle className={cn("text-lg sm:text-xl md:text-2xl", fontClass)}>
            {salutation} {cardTitleMainText}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 sm:space-y-3 px-3 sm:px-4 pb-4 sm:pb-6">
          <CardDescription className={cn("text-sm sm:text-base md:text-lg text-foreground/90 leading-relaxed", fontClass)}>
             {instructionsText}
          </CardDescription>
        </CardContent>
      </Card>
      
      <div className="w-full max-w-xs sm:max-w-sm mt-3 sm:mt-4">
        <GuidedSessionTimer
          durationInMinutes={20}
          prompts={currentTimerPrompts}
          practiceName={timerPracticeNameText}
          onSessionEnd={handleSessionEnd}
          autoStart={true}
          buttonTexts={currentTimerButtonTexts}
        />
      </div>
    </div>
  );
}
