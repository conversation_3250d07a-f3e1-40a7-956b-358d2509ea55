/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { AppConfig } from '../interfaces/installation-impl';
import { IdChangeCallbackFn } from '../api';
/**
 * Calls the onIdChange callbacks with the new FID value, and broadcasts the
 * change to other tabs.
 */
export declare function fidChanged(appConfig: AppConfig, fid: string): void;
export declare function addCallback(appConfig: AppConfig, callback: IdChangeCallbackFn): void;
export declare function removeCallback(appConfig: AppConfig, callback: IdChangeCallbackFn): void;
