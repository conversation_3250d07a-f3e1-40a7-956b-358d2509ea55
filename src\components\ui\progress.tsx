import * as React from "react"
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
  Animated,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  secondary: '#f1f5f9',
}

export interface ProgressProps extends ViewProps {
  value?: number
  style?: ViewStyle
}

const Progress = React.forwardRef<View, ProgressProps>(
  ({ value = 0, style, ...props }, ref) => {
    const animatedValue = React.useRef(new Animated.Value(0)).current

    React.useEffect(() => {
      Animated.timing(animatedValue, {
        toValue: Math.max(0, Math.min(100, value)),
        duration: 300,
        useNativeDriver: false,
      }).start()
    }, [value, animatedValue])

    const widthInterpolated = animatedValue.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
      extrapolate: 'clamp',
    })

    return (
      <View
        ref={ref}
        style={[styles.progressContainer, style]}
        {...props}
      >
        <Animated.View
          style={[
            styles.progressIndicator,
            { width: widthInterpolated },
          ]}
        />
      </View>
    )
  }
)

Progress.displayName = "Progress"

const styles = StyleSheet.create({
  progressContainer: {
    height: 16,
    width: '100%',
    backgroundColor: colors.secondary,
    borderRadius: 8,
    overflow: 'hidden',
  },
  progressIndicator: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
})

export { Progress }
