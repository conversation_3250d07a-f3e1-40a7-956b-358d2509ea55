{"version": 3, "names": ["isF<PERSON><PERSON>", "global", "_IS_FABRIC", "findHostInstance", "require", "findHostInstance_DEPRECATED", "e", "Error", "getShadowNodeWrapperAndTagFromRef", "ref", "hostInstance", "shadowNodeWrapper", "_internalInstanceHandle", "stateNode", "node", "tag", "_nativeTag"], "sourceRoot": "../../../src", "sources": ["gesture-handler/fabricUtils.ts"], "mappings": ";;;;;;;AAaO,SAASA,QAAQA,CAAA,EAAG;EACzB,OAAO,CAAC,CAAEC,MAAM,CAAiBC,UAAU;AAC7C;AAEA,IAAIC,gBAA+D,GAAGA,CAAA,KAAM;EAC1E,OAAO,IAAI;AACb,CAAC;AACD,IAAIH,QAAQ,CAAC,CAAC,EAAE;EACd,IAAI;IACFG,gBAAgB;IACd;IACAC,OAAO,CAAC,mDAAmD,CAAC,CAACC,2BAA2B;EAC5F,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;AACF;AAEO,SAASC,iCAAiCA,CAC/CC,GAA2D,EAC3D;EACA,MAAMC,YAAY,GAAGP,gBAAgB,CAACM,GAAsB,CAAC;EAC7D,OAAO;IACLE,iBAAiB,EAAED,YAAY,EAAEE,uBAAuB,CAACC,SAAS,CAACC,IAAI;IACvEC,GAAG,EAAEL,YAAY,EAAEM;EACrB,CAAC;AACH"}