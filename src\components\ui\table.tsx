import * as React from "react"
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TextProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  foreground: '#0f172a',
  mutedForeground: '#64748b',
  border: '#e2e8f0',
  muted: '#f1f5f9',
}

export interface TableProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TableHeaderProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TableBodyProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TableFooterProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TableRowProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TableHeadProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

export interface TableCellProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

export interface TableCaptionProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

const Table = React.forwardRef<ScrollView, TableProps>(
  ({ children, style, ...props }, ref) => (
    <ScrollView
      ref={ref}
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      style={[styles.tableContainer, style]}
      {...props}
    >
      <View style={styles.table}>
        {children}
      </View>
    </ScrollView>
  )
)

const TableHeader = React.forwardRef<View, TableHeaderProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.tableHeader, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const TableBody = React.forwardRef<View, TableBodyProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.tableBody, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const TableFooter = React.forwardRef<View, TableFooterProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.tableFooter, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const TableRow = React.forwardRef<View, TableRowProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.tableRow, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const TableHead = React.forwardRef<Text, TableHeadProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.tableHead, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

const TableCell = React.forwardRef<Text, TableCellProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.tableCell, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

const TableCaption = React.forwardRef<Text, TableCaptionProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.tableCaption, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

Table.displayName = "Table"
TableHeader.displayName = "TableHeader"
TableBody.displayName = "TableBody"
TableFooter.displayName = "TableFooter"
TableRow.displayName = "TableRow"
TableHead.displayName = "TableHead"
TableCell.displayName = "TableCell"
TableCaption.displayName = "TableCaption"

const styles = StyleSheet.create({
  tableContainer: {
    width: '100%',
  },
  table: {
    minWidth: '100%',
  },
  tableHeader: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tableBody: {
    // No specific styles needed
  },
  tableFooter: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.muted,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    minHeight: 48,
    alignItems: 'center',
  },
  tableHead: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: colors.mutedForeground,
    paddingHorizontal: 16,
    paddingVertical: 12,
    textAlign: 'left',
  },
  tableCell: {
    flex: 1,
    fontSize: 14,
    color: colors.foreground,
    paddingHorizontal: 16,
    paddingVertical: 12,
    textAlign: 'left',
  },
  tableCaption: {
    fontSize: 14,
    color: colors.mutedForeground,
    textAlign: 'center',
    marginTop: 16,
  },
})

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
