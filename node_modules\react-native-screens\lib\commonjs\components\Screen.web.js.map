{"version": 3, "names": ["_reactNative", "require", "_react", "_interopRequireDefault", "_core", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "InnerScreen", "exports", "View", "NativeScreen", "React", "Component", "render", "active", "activityState", "style", "enabled", "screensEnabled", "rest", "props", "undefined", "createElement", "hidden", "display", "Screen", "Animated", "createAnimatedComponent", "ScreenContext", "createContext", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.web.tsx"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,KAAA,GAAAH,OAAA;AAAyC,SAAAE,uBAAAE,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA;AAElC,MAAMQ,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAGE,iBAAI;;AAE/B;AACA;AACO,MAAMC,YAAY,SAASC,cAAK,CAACC,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;MAC1B,GAAGC;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIH,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKO,SAAS,IAAIN,aAAa,KAAKM,SAAS,EAAE;QACvDN,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MACA,oBACE3B,MAAA,CAAAK,OAAA,CAAA8B,aAAA,CAACrC,YAAA,CAAAwB;MACC;MAAA,EAAAhB,QAAA;QACA8B,MAAM,EAAER,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEQ,OAAO,EAAET,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DI,IAAI,CACT,CAAC;IAEN;IAEA,oBAAOhC,MAAA,CAAAK,OAAA,CAAA8B,aAAA,CAACrC,YAAA,CAAAwB,IAAI,EAAKU,IAAO,CAAC;EAC3B;AACF;AAACX,OAAA,CAAAE,YAAA,GAAAA,YAAA;AAED,MAAMe,MAAM,GAAGC,qBAAQ,CAACC,uBAAuB,CAACjB,YAAY,CAAC;AAEtD,MAAMkB,aAAa,GAAApB,OAAA,CAAAoB,aAAA,gBAAGjB,cAAK,CAACkB,aAAa,CAACJ,MAAM,CAAC;AAAC,IAAAK,QAAA,GAAAtB,OAAA,CAAAhB,OAAA,GAE1CiC,MAAM"}