
"use client";

import PageTitle from "@/components/shared/PageTitle";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Compass, MapPin, ExternalLink } from "lucide-react";
import { useLanguage, type Language } from "@/context/LanguageContext";
import { cn } from "@/lib/utils";

type QiblaPageContent = {
  pageTitle: string;
  pageSubtitle: string;
  cardTitle: string;
  locationInfo: string;
  findQiblaButton: string;
  googleQiblaLinkText: string;
  manualQiblaInfo: string;
};

const translations: Record<Language, QiblaPageContent> = {
  en: {
    pageTitle: "Qibla Direction",
    pageSubtitle: "Find the direction of the Kaaba for your prayers.",
    cardTitle: "Find Qibla",
    locationInfo: "Enable location services or manually input your location for accurate Qibla direction. (Feature under development)",
    findQiblaButton: "Show My Qibla (Coming Soon)",
    googleQiblaLinkText: "Use Google Qibla Finder",
    manualQiblaInfo: "Alternatively, you can use an external service like Google Qibla Finder for immediate direction."
  },
  ur: {
    pageTitle: "قبلہ رخ",
    pageSubtitle: "اپنی نمازوں کے لیے خانہ کعبہ کی سمت معلوم کریں۔",
    cardTitle: "قبلہ معلوم کریں",
    locationInfo: "قبلہ کی درست سمت کے لیے لوکیشن سروسز کو فعال کریں یا دستی طور پر اپنا مقام درج کریں۔ (یہ فیچر ابھی زیر تکمیل ہے)",
    findQiblaButton: "میرا قبلہ دکھائیں (جلد آرہا ہے)",
    googleQiblaLinkText: "گوگل قبلہ فائنڈر استعمال کریں",
    manualQiblaInfo: "متبادل کے طور پر، آپ فوری سمت کے لیے گوگل قبلہ فائنڈر جیسی بیرونی سروس استعمال کر سکتے ہیں۔"
  },
  ro: {
    pageTitle: "Qibla Rukh",
    pageSubtitle: "Apni namazon ke liye Kaaba ki simt maloom karein.",
    cardTitle: "Qibla Maloom Karein",
    locationInfo: "Qibla ki durust simt ke liye location services ko enable karein ya manual taur par apna maqaam darj karein. (Yeh feature abhi zer-e-takmeel hai)",
    findQiblaButton: "Mera Qibla Dikhayein (Jald Aa Raha Hai)",
    googleQiblaLinkText: "Google Qibla Finder Istemal Karein",
    manualQiblaInfo: "Mutabadil ke taur par, aap fauri simt ke liye Google Qibla Finder jaisi bairuni service istemal kar sakte hain."
  },
  hi: {
    pageTitle: "क़िब्ला रुख़",
    pageSubtitle: "अपनी नमाज़ों के लिए काबा की सिम्त मालूम करें।",
    cardTitle: "क़िब्ला मालूम करें",
    locationInfo: "क़िब्ला की दुरुस्त सिम्त के लिए लोकेशन सर्विसेज़ को सक्षम करें या मैन्युअल रूप से अपना स्थान दर्ज करें। (यह फ़ीचर अभी निर्माणाधीन है)",
    findQiblaButton: "मेरा क़िब्ला दिखाएँ (जल्द आ रहा है)",
    googleQiblaLinkText: "गूगल क़िब्ला फ़ाइंडर इस्तेमाल करें",
    manualQiblaInfo: "वैकल्पिक रूप से, आप तत्काल दिशा के लिए गूगल क़िब्ला फ़ाइंडर जैसी बाहरी सेवा का उपयोग कर सकते हैं।"
  },
  ar: {
    pageTitle: "اتجاه القبلة",
    pageSubtitle: "اعثر على اتجاه الكعبة لصلواتك.",
    cardTitle: "تحديد القبلة",
    locationInfo: "قم بتمكين خدمات الموقع أو أدخل موقعك يدويًا للحصول على اتجاه القبلة بدقة. (الميزة قيد التطوير)",
    findQiblaButton: "أظهر قبلتي (قريباً)",
    googleQiblaLinkText: "استخدم مكتشف القبلة من جوجل",
    manualQiblaInfo: "بدلاً من ذلك، يمكنك استخدام خدمة خارجية مثل مكتشف القبلة من جوجل لمعرفة الاتجاه فورًا."
  }
};


export default function QiblaPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  return (
    <div className="space-y-8">
      <PageTitle 
        title={content.pageTitle} 
        subtitle={content.pageSubtitle} 
        className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
      />

      <Card className="shadow-xl bg-card">
        <CardHeader className={cn("items-center", textDir === 'rtl' && 'text-right')}>
            <Compass className={cn("h-12 w-12 text-primary mb-3", isRtl && "ml-3")} />
            <h2 className={cn("text-2xl font-semibold text-primary", fontClass)}>
                {content.cardTitle}
            </h2>
        </CardHeader>
        <CardContent 
            lang={language} 
            dir={textDir} 
            className={cn("space-y-6 text-center text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}
        >
            <div className="flex flex-col items-center gap-2 p-4 border rounded-md bg-muted/50">
                <MapPin className="h-6 w-6 text-muted-foreground" />
                <p className="text-muted-foreground">{content.locationInfo}</p>
            </div>
            
            <Button size="lg" disabled className={cn("w-full", fontClass)}>
                {content.findQiblaButton}
            </Button>

            <div className="text-sm text-muted-foreground">OR</div>

            <a href="https://qiblafinder.withgoogle.com/" target="_blank" rel="noopener noreferrer">
                <Button variant="outline" size="lg" className={cn("w-full", fontClass)}>
                    {content.googleQiblaLinkText} <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
            </a>
            <p className="text-xs text-muted-foreground pt-2">{content.manualQiblaInfo}</p>
        </CardContent>
      </Card>
    </div>
  );
}

    