
import * as React from "react"
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
  ActivityIndicator,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  primaryForeground: '#ffffff',
  destructive: '#ef4444',
  destructiveForeground: '#ffffff',
  secondary: '#f1f5f9',
  secondaryForeground: '#0f172a',
  accent: '#f8fafc',
  accentForeground: '#0f172a',
  background: '#ffffff',
  border: '#e2e8f0',
  muted: '#64748b',
}

export type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
export type ButtonSize = 'default' | 'sm' | 'lg' | 'icon'

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  variant?: ButtonVariant
  size?: ButtonSize
  loading?: boolean
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

const Button = React.forwardRef<TouchableOpacity, ButtonProps>(
  ({ variant = 'default', size = 'default', loading = false, children, style, textStyle, disabled, ...props }, ref) => {
    const buttonStyle = getButtonStyle(variant, size)
    const buttonTextStyle = getButtonTextStyle(variant, size)

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.base,
          buttonStyle,
          disabled && styles.disabled,
          style,
        ]}
        disabled={disabled || loading}
        activeOpacity={0.8}
        {...props}
      >
        {loading ? (
          <ActivityIndicator
            size="small"
            color={variant === 'outline' || variant === 'ghost' ? colors.primary : colors.primaryForeground}
          />
        ) : (
          <Text style={[buttonTextStyle, textStyle]}>
            {children}
          </Text>
        )}
      </TouchableOpacity>
    )
  }
)

Button.displayName = "Button"

const getButtonStyle = (variant: ButtonVariant, size: ButtonSize): ViewStyle => {
  const baseStyle: ViewStyle = {
    ...styles.base,
    ...getSizeStyle(size),
  }

  switch (variant) {
    case 'default':
      return {
        ...baseStyle,
        backgroundColor: colors.primary,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }
    case 'destructive':
      return {
        ...baseStyle,
        backgroundColor: colors.destructive,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }
    case 'outline':
      return {
        ...baseStyle,
        backgroundColor: colors.background,
        borderWidth: 1,
        borderColor: colors.border,
      }
    case 'secondary':
      return {
        ...baseStyle,
        backgroundColor: colors.secondary,
      }
    case 'ghost':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
      }
    case 'link':
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        paddingHorizontal: 0,
      }
    default:
      return baseStyle
  }
}

const getButtonTextStyle = (variant: ButtonVariant, size: ButtonSize): TextStyle => {
  const baseTextStyle: TextStyle = {
    fontWeight: '500',
    textAlign: 'center',
    ...getSizeTextStyle(size),
  }

  switch (variant) {
    case 'default':
      return {
        ...baseTextStyle,
        color: colors.primaryForeground,
      }
    case 'destructive':
      return {
        ...baseTextStyle,
        color: colors.destructiveForeground,
      }
    case 'outline':
      return {
        ...baseTextStyle,
        color: colors.primary,
      }
    case 'secondary':
      return {
        ...baseTextStyle,
        color: colors.secondaryForeground,
      }
    case 'ghost':
      return {
        ...baseTextStyle,
        color: colors.primary,
      }
    case 'link':
      return {
        ...baseTextStyle,
        color: colors.primary,
        textDecorationLine: 'underline',
      }
    default:
      return baseTextStyle
  }
}

const getSizeStyle = (size: ButtonSize): ViewStyle => {
  switch (size) {
    case 'sm':
      return {
        height: 36,
        paddingHorizontal: 12,
        borderRadius: 6,
      }
    case 'lg':
      return {
        height: 44,
        paddingHorizontal: 32,
        borderRadius: 8,
      }
    case 'icon':
      return {
        height: 40,
        width: 40,
        paddingHorizontal: 0,
        borderRadius: 8,
      }
    default:
      return {
        height: 40,
        paddingHorizontal: 16,
        borderRadius: 6,
      }
  }
}

const getSizeTextStyle = (size: ButtonSize): TextStyle => {
  switch (size) {
    case 'sm':
      return {
        fontSize: 14,
      }
    case 'lg':
      return {
        fontSize: 16,
      }
    case 'icon':
      return {
        fontSize: 14,
      }
    default:
      return {
        fontSize: 14,
      }
  }
}

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
  },
  disabled: {
    opacity: 0.5,
  },
})

export { Button }
    