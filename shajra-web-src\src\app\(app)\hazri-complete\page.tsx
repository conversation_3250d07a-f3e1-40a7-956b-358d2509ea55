
"use client";

import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import { CheckCircle2, Home } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

const salutations: Record<Language, string> = {
  en: "Assalamu Alaikum.",
  ur: "السلام علیکم۔",
  ro: "Assalamu Alaikum.",
  hi: "अस्सलामु अलैकुम।",
  ar: "السلام عليكم.",
};

const translations = {
  pageTitle: {
    en: "Attendance Session Complete",
    ur: "حاضری سیشن مکمل",
    ro: "Hazri Session Mukammal",
    hi: "हाज़िरी सेशन मुकम्मल",
    ar: "اكتملت جلسة الحضور",
  },
  cardTitle: {
    en: "Session Complete!",
    ur: "سیشن مکمل!",
    ro: "Session Mukammal!",
    hi: "सेशन मुकम्मल!",
    ar: "اكتملت الجلسة!",
  },
  completionMessage: {
    en: "Your attendance for today is complete. You may now get up if you wish, or you can continue your Zikr for as long as you like.",
    ur: "آج کی آپ کی حاضری مکمل ہو گئی ہے۔ اگر اٹھنا چاہیں تو اب اٹھ سکتے ہیں یا ذکر کرتے رہنا چاہتے ہیں تو جب تک مرضی ہو کر سکتے ہیں۔",
    ro: "Aaj ki aap ki hazri mukammal ho gayi hai. Agar uthna chahein toh ab uth sakte hain ya Zikr karte rehna chahte hain toh jab tak marzi ho kar sakte hain.",
    hi: "आज की आपकी हाज़िरी मुकम्मल हो गई है। अगर उठना चाहें तो अब उठ सकते हैं या ज़िक्र करते रहना चाहते हैं तो जब तक मर्ज़ी हो कर सकते हैं।",
    ar: "قد اكتمل حضوركم لهذا اليوم. يمكنكم الآن القيام إن شئتم، أو يمكنكم مواصلة الذكر ما شئتم.",
  },
  backToHomeButton: {
    en: "Back to Home",
    ur: "مرکزی صفحہ پر واپس",
    ro: "Home Page Par Wapas",
    hi: "मुख्य पृष्ठ पर वापस",
    ar: "العودة إلى الرئيسية",
  }
};

export default function HazriCompletePage() {
  const { language } = useLanguage();
  const router = useRouter();

  const pageTitleText = translations.pageTitle[language] || translations.pageTitle.en;
  const cardTitleText = translations.cardTitle[language] || translations.cardTitle.en;
  const completionMessageText = `${salutations[language] || salutations.en} ${translations.completionMessage[language] || translations.completionMessage.en}`;
  const backButtonText = translations.backToHomeButton[language] || translations.backToHomeButton.en;

  const isRtl = language === 'ar' || language === 'ur';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  return (
    <div className={cn("flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-4 space-y-8", fontClass)} lang={language} dir={isRtl ? "rtl" : "ltr"}>
      <PageTitle title={pageTitleText} className={cn("text-center", fontClass)} />

      <Card className={cn("w-full max-w-md shadow-xl text-center glass-effect", fontClass)}>
        <CardHeader className="items-center">
          <CheckCircle2 className="h-16 w-16 text-green-500 mb-3" />
          <CardTitle className={cn("text-2xl md:text-3xl", fontClass)}>
            {cardTitleText}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className={cn("text-md md:text-lg text-foreground/90 leading-relaxed", fontClass)}>
            {completionMessageText}
          </p>
        </CardContent>
      </Card>

      <Button onClick={() => router.push('/home')} size="lg" className={cn("font-semibold", fontClass)}>
        <Home className="h-5 w-5 mr-2" />
        {backButtonText}
      </Button>
    </div>
  );
}
