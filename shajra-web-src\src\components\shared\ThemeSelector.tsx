
"use client";

import * as React from "react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Palette } from "lucide-react"; 

export const baseColorThemes = [
  { name: "Serene Teal", value: "teal" }, // Moved Teal to the top, it will be the new default
  { name: "Crystal Blue", value: "crystal" },
  { name: "Sabiriya Blue", value: "sabiriya" },
  { name: "Warm Amethyst", value: "amethyst" },
];

export const SABIRIYA_BASE_COLOR_THEME_KEY = "sabiriya-app-base-color-theme"; // Unified key
export const DEFAULT_BASE_COLOR = "teal"; // Changed default to teal

export function ThemeSelector() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
    // On initial load, if theme is 'system', 'light', or 'dark' (generic),
    // apply the stored base color preference with the resolved system mode.
    const storedBaseColor = localStorage.getItem(SABIRIYA_BASE_COLOR_THEME_KEY) || DEFAULT_BASE_COLOR;
    if (theme === 'system' || theme === 'light' || theme === 'dark') {
        const currentMode = resolvedTheme?.startsWith("dark") ? "dark" : "light";
        // Only set if the resolved theme doesn't already include our base color (to avoid loop)
        if (resolvedTheme === currentMode) { // e.g. resolvedTheme is 'light', not 'light-teal'
            setTheme(`${currentMode}-${storedBaseColor}`);
        }
    }
  }, [theme, resolvedTheme, setTheme]);


  const handleBaseColorChange = (newBaseColor: string) => {
    localStorage.setItem(SABIRIYA_BASE_COLOR_THEME_KEY, newBaseColor);
    const currentMode = resolvedTheme?.startsWith("dark") ? "dark" : "light";
    setTheme(`${currentMode}-${newBaseColor}`);
  };

  if (!mounted) {
    return <div className="h-10 w-10" />; 
  }

  let currentAppliedBaseColor = DEFAULT_BASE_COLOR;
  if (theme && theme.includes('-')) {
    const parts = theme.split('-');
    if (parts.length === 2 && baseColorThemes.some(bct => bct.value === parts[1])) {
      currentAppliedBaseColor = parts[1];
    }
  } else {
    // If theme is generic (light/dark/system) or not in mode-base format, get from localStorage
    currentAppliedBaseColor = localStorage.getItem(SABIRIYA_BASE_COLOR_THEME_KEY) || DEFAULT_BASE_COLOR;
  }
  // Final check to ensure the value is one of the known base themes
  if (!baseColorThemes.some(b => b.value === currentAppliedBaseColor)) {
    currentAppliedBaseColor = DEFAULT_BASE_COLOR;
  }


  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" aria-label="Select Theme Color">
          <Palette className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Select Primary Color</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup
          value={currentAppliedBaseColor}
          onValueChange={handleBaseColorChange}
        >
          {baseColorThemes.map((baseTheme) => (
            <DropdownMenuRadioItem key={baseTheme.value} value={baseTheme.value}>
              {baseTheme.name}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
    
