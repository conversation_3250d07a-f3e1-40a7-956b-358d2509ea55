{"version": 3, "names": ["React", "useEffect", "Dimensions", "Platform", "findNodeHandle", "GestureDetector", "Gesture", "useSharedValue", "measure", "startScreenTransition", "finishScreenTransition", "makeMutable", "runOnUI", "getShadowNodeWrapperAndTagFromRef", "isF<PERSON><PERSON>", "RNScreensTurboModule", "DefaultEvent", "DefaultScreenDimensions", "checkBoundaries", "checkIfTransitionCancelled", "getAnimationForTransition", "EmptyGestureHandler", "Fling", "ScreenGestureDetector", "_ref", "children", "gestureDetectorBridge", "goBackGesture", "screenEdgeGesture", "transitionAnimation", "customTransitionAnimation", "screensRefs", "currentRouteKey", "sharedEvent", "startingGesturePosition", "canPerformUpdates", "screenTransitionConfig", "stackTag", "belowTopScreenId", "topScreenId", "screenTransition", "isTransitionCanceled", "screenDimensions", "onFinishAnimation", "screenTagToNodeWrapperUI", "IS_FABRIC", "current", "stackUseEffectCallback", "stackRef", "value", "OS", "disableSwipeBackForTopScreen", "screenTagToNodeWrapper", "key", "screenRef", "screenData", "tag", "shadowNodeWrapper", "console", "warn", "computeProgress", "event", "progress", "startingPosition", "translationX", "width", "absoluteX", "translationY", "height", "absoluteY", "Math", "abs", "progressX", "progressY", "max", "onStart", "transitionConfig", "transitionData", "startTransition", "canStartTransition", "topScreenTag", "belowTopScreenTag", "animatedRefMock", "screenSize", "Error", "finishTransition", "onUpdate", "updateTransition", "onEnd", "velocityFactor", "distanceX", "min", "velocityX", "distanceY", "velocityY", "requiredXDistance", "requiredYDistance", "panGesture", "Pan", "HIT_SLOP_SIZE", "ACTIVATION_DISTANCE", "activeOffsetX", "hitSlop", "left", "top", "right", "activeOffsetY", "get", "bottom", "createElement", "gesture"], "sourceRoot": "../../../src", "sources": ["gesture-handler/ScreenGestureDetector.tsx"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,cAAc;AACnE,SACEC,eAAe,EACfC,OAAO,QAGF,8BAA8B;AACrC,SACEC,cAAc,EACdC,OAAO,EACPC,qBAAqB,EACrBC,sBAAsB,EACtBC,WAAW,EACXC,OAAO,QACF,yBAAyB;AAEhC,SAASC,iCAAiC,EAAEC,QAAQ,QAAQ,eAAe;AAC3E,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,YAAY;AAClE,SACEC,eAAe,EACfC,0BAA0B,EAC1BC,yBAAyB,QACpB,eAAe;AAEtB,MAAMC,mBAAmB,GAAGf,OAAO,CAACgB,KAAK,CAAC,CAAC;AAE3C,MAAMC,qBAAqB,GAAGC,IAAA,IAQF;EAAA,IARG;IAC7BC,QAAQ;IACRC,qBAAqB;IACrBC,aAAa;IACbC,iBAAiB;IACjBC,mBAAmB,EAAEC,yBAAyB;IAC9CC,WAAW;IACXC;EACoB,CAAC,GAAAR,IAAA;EACrB,MAAMS,WAAW,GAAG1B,cAAc,CAACS,YAAY,CAAC;EAChD,MAAMkB,uBAAuB,GAAG3B,cAAc,CAACS,YAAY,CAAC;EAC5D,MAAMmB,iBAAiB,GAAGxB,WAAW,CAAC,KAAK,CAAC;EAC5C,MAAMkB,mBAAmB,GAAGT,yBAAyB,CACnDO,aAAa,EACbG,yBACF,CAAC;EACD,MAAMM,sBAAsB,GAAGzB,WAAW,CAAC;IACzC0B,QAAQ,EAAE,CAAC,CAAC;IACZC,gBAAgB,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC;IACfN,WAAW;IACXC,uBAAuB;IACvBM,gBAAgB,EAAEX,mBAAmB;IACrCY,oBAAoB,EAAE,KAAK;IAC3Bd,aAAa,EAAEA,aAAa,IAAI,YAAY;IAC5Ce,gBAAgB,EAAEzB,uBAAuB;IACzC0B,iBAAiB,EAAEA,CAAA,KAAM;MACvB,SAAS;IACX;EACF,CAAC,CAAC;EACF,MAAMN,QAAQ,GAAG1B,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAMiC,wBAAwB,GAAGjC,WAAW,CAAsB,CAAC,CAAC,CAAC;EACrE,MAAMkC,SAAS,GAAG/B,QAAQ,CAAC,CAAC;EAE5BY,qBAAqB,CAACoB,OAAO,CAACC,sBAAsB,GAAGC,QAAQ,IAAI;IACjE,IAAI,CAACrB,aAAa,EAAE;MAClB;IACF;IACAU,QAAQ,CAACY,KAAK,GAAG7C,cAAc,CAAC4C,QAAQ,CAACF,OAAc,CAAW;IAClE,IAAI3C,QAAQ,CAAC+C,EAAE,KAAK,KAAK,EAAE;MACzBtC,OAAO,CAAC,MAAM;QACZG,oBAAoB,CAACoC,4BAA4B,CAACd,QAAQ,CAACY,KAAK,CAAC;MACnE,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,SAAS,IAAI,CAAClB,aAAa,EAAE;MAChC;IACF;IACA,MAAMyB,sBAA+D,GAAG,CAAC,CAAC;IAC1E,KAAK,MAAMC,GAAG,IAAItB,WAAW,CAACe,OAAO,EAAE;MACrC,MAAMQ,SAAS,GAAGvB,WAAW,CAACe,OAAO,CAACO,GAAG,CAAC;MAC1C,MAAME,UAAU,GAAG1C,iCAAiC,CAACyC,SAAS,CAACR,OAAO,CAAC;MACvE,IAAIS,UAAU,CAACC,GAAG,IAAID,UAAU,CAACE,iBAAiB,EAAE;QAClDL,sBAAsB,CAACG,UAAU,CAACC,GAAG,CAAC,GAAGD,UAAU,CAACE,iBAAiB;MACvE,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;MAC5D;IACF;IACAf,wBAAwB,CAACK,KAAK,GAAGG,sBAAsB;EACzD,CAAC,EAAE,CAACpB,eAAe,CAAC,CAAC;EAErB,SAAS4B,eAAeA,CACtBC,KAAwD,EACxD;IACA,SAAS;;IACT,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMpB,gBAAgB,GAAGN,sBAAsB,CAACa,KAAK,CAACP,gBAAgB;IACtE,MAAMqB,gBAAgB,GAAG7B,uBAAuB,CAACe,KAAK;IACtD,IAAItB,aAAa,KAAK,YAAY,EAAE;MAClCmC,QAAQ,GACND,KAAK,CAACG,YAAY,IACjBtB,gBAAgB,CAACuB,KAAK,GAAGF,gBAAgB,CAACG,SAAS,CAAC;IACzD,CAAC,MAAM,IAAIvC,aAAa,KAAK,WAAW,EAAE;MACxCmC,QAAQ,GAAI,CAAC,CAAC,GAAGD,KAAK,CAACG,YAAY,GAAID,gBAAgB,CAACG,SAAS;IACnE,CAAC,MAAM,IAAIvC,aAAa,KAAK,WAAW,EAAE;MACxCmC,QAAQ,GACL,CAAC,CAAC,GAAGD,KAAK,CAACM,YAAY,IACvBzB,gBAAgB,CAAC0B,MAAM,GAAGL,gBAAgB,CAACM,SAAS,CAAC;IAC1D,CAAC,MAAM,IAAI1C,aAAa,KAAK,SAAS,EAAE;MACtCmC,QAAQ,GAAGD,KAAK,CAACM,YAAY,GAAGJ,gBAAgB,CAACM,SAAS;IAC5D,CAAC,MAAM,IAAI1C,aAAa,KAAK,iBAAiB,EAAE;MAC9CmC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACG,YAAY,GAAGtB,gBAAgB,CAACuB,KAAK,GAAG,CAAC,CAAC;IACtE,CAAC,MAAM,IAAItC,aAAa,KAAK,eAAe,EAAE;MAC5CmC,QAAQ,GAAGQ,IAAI,CAACC,GAAG,CAACV,KAAK,CAACM,YAAY,GAAGzB,gBAAgB,CAAC0B,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC,MAAM,IAAIzC,aAAa,KAAK,qBAAqB,EAAE;MAClD,MAAM6C,SAAS,GAAGF,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACG,YAAY,GAAGtB,gBAAgB,CAACuB,KAAK,GAAG,CAChD,CAAC;MACD,MAAMQ,SAAS,GAAGH,IAAI,CAACC,GAAG,CACxBV,KAAK,CAACM,YAAY,GAAGzB,gBAAgB,CAAC0B,MAAM,GAAG,CACjD,CAAC;MACDN,QAAQ,GAAGQ,IAAI,CAACI,GAAG,CAACF,SAAS,EAAEC,SAAS,CAAC;IAC3C;IACA,OAAOX,QAAQ;EACjB;EAEA,SAASa,OAAOA,CAACd,KAAwD,EAAE;IACzE,SAAS;;IACT5B,WAAW,CAACgB,KAAK,GAAGY,KAAK;IACzB,MAAMe,gBAAgB,GAAGxC,sBAAsB,CAACa,KAAK;IACrD,MAAM4B,cAAc,GAAG9D,oBAAoB,CAAC+D,eAAe,CAACzC,QAAQ,CAACY,KAAK,CAAC;IAC3E,IAAI4B,cAAc,CAACE,kBAAkB,KAAK,KAAK,EAAE;MAC/C5C,iBAAiB,CAACc,KAAK,GAAG,KAAK;MAC/B;IACF;IAEA,IAAIJ,SAAS,EAAE;MACb+B,gBAAgB,CAACrC,WAAW,GAC1BK,wBAAwB,CAACK,KAAK,CAAC4B,cAAc,CAACG,YAAY,CAAC;MAC7DJ,gBAAgB,CAACtC,gBAAgB,GAC/BM,wBAAwB,CAACK,KAAK,CAAC4B,cAAc,CAACI,iBAAiB,CAAC;IACpE,CAAC,MAAM;MACLL,gBAAgB,CAACrC,WAAW,GAAGsC,cAAc,CAACG,YAAY;MAC1DJ,gBAAgB,CAACtC,gBAAgB,GAAGuC,cAAc,CAACI,iBAAiB;IACtE;IAEAL,gBAAgB,CAACvC,QAAQ,GAAGA,QAAQ,CAACY,KAAK;IAC1Cf,uBAAuB,CAACe,KAAK,GAAGY,KAAK;IACrC,MAAMqB,eAAe,GAAGA,CAAA,KAAM;MAC5B,OAAO9C,sBAAsB,CAACa,KAAK,CAACV,WAAW;IACjD,CAAC;IACD,MAAM4C,UAAU,GAAG3E,OAAO,CAAC0E,eAAsB,CAAC;IAClD,IAAIC,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;IAC1D;IACA,IAAID,UAAU,IAAI,IAAI,EAAE;MACtBhD,iBAAiB,CAACc,KAAK,GAAG,KAAK;MAC/BlC,oBAAoB,CAACsE,gBAAgB,CAAChD,QAAQ,CAACY,KAAK,EAAE,IAAI,CAAC;MAC3D;IACF;IACA2B,gBAAgB,CAAClC,gBAAgB,GAAGyC,UAAU;IAC9C1E,qBAAqB,CAACmE,gBAAgB,CAAC;IACvCzC,iBAAiB,CAACc,KAAK,GAAG,IAAI;EAChC;EAEA,SAASqC,QAAQA,CAACzB,KAAwD,EAAE;IAC1E,SAAS;;IACT,IAAI,CAAC1B,iBAAiB,CAACc,KAAK,EAAE;MAC5B;IACF;IACA/B,eAAe,CAACS,aAAa,EAAEkC,KAAK,CAAC;IACrC,MAAMC,QAAQ,GAAGF,eAAe,CAACC,KAAK,CAAC;IACvC5B,WAAW,CAACgB,KAAK,GAAGY,KAAK;IACzB,MAAMxB,QAAQ,GAAGD,sBAAsB,CAACa,KAAK,CAACZ,QAAQ;IACtDtB,oBAAoB,CAACwE,gBAAgB,CAAClD,QAAQ,EAAEyB,QAAQ,CAAC;EAC3D;EAEA,SAAS0B,KAAKA,CAAC3B,KAAwD,EAAE;IACvE,SAAS;;IACT,IAAI,CAAC1B,iBAAiB,CAACc,KAAK,EAAE;MAC5B;IACF;IAEA,MAAMwC,cAAc,GAAG,GAAG;IAC1B,MAAMN,UAAU,GAAG/C,sBAAsB,CAACa,KAAK,CAACP,gBAAgB;IAChE,MAAMgD,SAAS,GACb7B,KAAK,CAACG,YAAY,GAAGM,IAAI,CAACqB,GAAG,CAAC9B,KAAK,CAAC+B,SAAS,GAAGH,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMI,SAAS,GACbhC,KAAK,CAACM,YAAY,GAAGG,IAAI,CAACqB,GAAG,CAAC9B,KAAK,CAACiC,SAAS,GAAGL,cAAc,EAAE,GAAG,CAAC;IACtE,MAAMM,iBAAiB,GAAGZ,UAAU,CAAClB,KAAK,GAAG,CAAC;IAC9C,MAAM+B,iBAAiB,GAAGb,UAAU,CAACf,MAAM,GAAG,CAAC;IAC/C,MAAM3B,oBAAoB,GAAGtB,0BAA0B,CACrDQ,aAAa,EACb+D,SAAS,EACTK,iBAAiB,EACjBF,SAAS,EACTG,iBACF,CAAC;IACD,MAAM3D,QAAQ,GAAGD,sBAAsB,CAACa,KAAK,CAACZ,QAAQ;IACtDD,sBAAsB,CAACa,KAAK,CAACN,iBAAiB,GAAG,MAAM;MACrD5B,oBAAoB,CAACsE,gBAAgB,CAAChD,QAAQ,EAAEI,oBAAoB,CAAC;IACvE,CAAC;IACDL,sBAAsB,CAACa,KAAK,CAACR,oBAAoB,GAAGA,oBAAoB;IACxE/B,sBAAsB,CAAC0B,sBAAsB,CAACa,KAAK,CAAC;EACtD;EAEA,IAAIgD,UAAU,GAAG3F,OAAO,CAAC4F,GAAG,CAAC,CAAC,CAC3BvB,OAAO,CAACA,OAAO,CAAC,CAChBW,QAAQ,CAACA,QAAQ,CAAC,CAClBE,KAAK,CAACA,KAAK,CAAC;EAEf,IAAI5D,iBAAiB,EAAE;IACrB,MAAMuE,aAAa,GAAG,EAAE;IACxB,MAAMC,mBAAmB,GAAG,EAAE;IAC9B,IAAIzE,aAAa,KAAK,YAAY,EAAE;MAClCsE,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAACD,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEC,IAAI,EAAE,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEvC,KAAK,EAAEkC;MAAc,CAAC,CAAC;IACvD,CAAC,MAAM,IAAIxE,aAAa,KAAK,WAAW,EAAE;MACxCsE,UAAU,GAAGA,UAAU,CACpBI,aAAa,CAAC,CAACD,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEG,KAAK,EAAE,CAAC;QAAED,GAAG,EAAE,CAAC;QAAEvC,KAAK,EAAEkC;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIxE,aAAa,KAAK,WAAW,EAAE;MACxCsE,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAACN,mBAAmB,CAAC,CAClCE,OAAO,CAAC;QAAEE,GAAG,EAAE,CAAC;QAAEpC,MAAM,EAAElE,UAAU,CAACyG,GAAG,CAAC,QAAQ,CAAC,CAACvC,MAAM,GAAG;MAAI,CAAC,CAAC;MACrE;IACF,CAAC,MAAM,IAAIzC,aAAa,KAAK,SAAS,EAAE;MACtCsE,UAAU,GAAGA,UAAU,CACpBS,aAAa,CAAC,CAACN,mBAAmB,CAAC,CACnCE,OAAO,CAAC;QAAEM,MAAM,EAAE,CAAC;QAAExC,MAAM,EAAE+B;MAAc,CAAC,CAAC;IAClD;EACF;EACA,oBACEnG,KAAA,CAAA6G,aAAA,CAACxG,eAAe;IAACyG,OAAO,EAAEnF,aAAa,GAAGsE,UAAU,GAAG5E;EAAoB,GACxEI,QACc,CAAC;AAEtB,CAAC;AAED,eAAeF,qBAAqB"}