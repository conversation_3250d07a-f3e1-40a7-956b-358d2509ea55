
"use client";

import PageTitle from "@/components/shared/PageTitle";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { WifiOff } from "lucide-react";
import { useRouter } from "next/navigation";

export default function OfflinePage() {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center p-4 bg-background text-foreground">
      <WifiOff className="w-24 h-24 text-destructive mb-6" />
      <PageTitle
        title="You are Offline"
        subtitle="It seems you're not connected to the internet. Please check your connection."
      />
      <p className="text-muted-foreground mb-8 max-w-md">
        Some features of the app may not be available until you reconnect. Previously visited pages might be accessible.
      </p>
      <Button onClick={() => router.push('/home')} size="lg">
        Try Going Home
      </Button>
    </div>
  );
}
