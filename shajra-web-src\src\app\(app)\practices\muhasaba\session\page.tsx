
"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import PageTitle from '@/components/shared/PageTitle';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarIcon, ArrowRight, Save, CheckCircle } from 'lucide-react';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import LoadingSpinner from '@/components/shared/LoadingSpinner';

type ChecklistItem = {
  id: string;
  label: string;
};

type MuhasabaEntry = {
  checkedGoodDeeds: string[];
  manualGoodDeeds: string;
  checkedShortcomings: string[];
  manualShortcomings: string;
  resolutions: string;
};

// --- Checklist Items Content ---
const goodDeedsItemsContent: Record<Language, ChecklistItem[]> = {
  ur: [
    { id: 'gd_fardh_prayers', label: 'نماز کی پابندی (فرض)' },
    { id: 'gd_shagle_noori', label: 'شغلِ نوری کیا' },
    { id: 'gd_durood_shareef', label: 'درود شریف پڑھا' },
    { id: 'gd_tasbeeh_malaika', label: 'تسبیح ملائکہ پڑھی' },
    { id: 'gd_shajra_shareef', label: 'شجرہ شریف پڑھا' },
    { id: 'gd_muraqaba', label: 'مراقبہ کیا' },
    { id: 'gd_walidain_khidmat', label: 'والدین کی خدمت کی' },
    { id: 'gd_istighfaar', label: 'استغفار کیا' },
    { id: 'gd_quran_recitation', label: 'تلاوتِ قرآن کی' },
    { id: 'gd_sunnah_nawafil', label: 'سنت/نوافل نمازیں ادا کیں' },
    { id: 'gd_niyyat_ikhlas', label: 'نیت میں اخلاص' },
    { id: 'gd_qalb_taharat', label: 'قلب کی طہارت' },
    { id: 'gd_kirdaar_istiqamat', label: 'کردار میں استقامت' },
    { id: 'gd_sachchai', label: 'سچائی اختیار کی' },
    { id: 'gd_luqma_halal', label: 'لقمۂ حلال کا اہتمام' },
    { id: 'gd_amanat_pasdari', label: 'امانت اور عہد کی پاسداری' },
    { id: 'gd_nafs_tazkiya', label: 'نفس کا تزکیہ کرنے کی کوشش کی' },
    { id: 'gd_tawakkul_allah', label: 'اللہ پر کامل توکل رکھا' },
    { id: 'gd_tasleem_riza', label: 'اللہ کی رضا پر راضی رہا' },
    { id: 'gd_dunya_beraghbati', label: 'دنیا سے بے رغبتی اختیار کی' },
    { id: 'gd_fikr_akhirat', label: 'آخرت کی فکر کی' },
    { id: 'gd_sohbat_saliheen', label: 'صالحین کی صحبت اختیار کی' },
    { id: 'gd_zikr_kasrat', label: 'ذکرِ الٰہی کی کثرت کی' },
    { id: 'gd_paas_anfaas', label: 'پاسِ انفاس کا خیال رکھا' },
    { id: 'gd_aajizi_inkisari', label: 'عاجزی و انکساری اختیار کی' },
    { id: 'gd_khidmat_khalq', label: 'خدمتِ خلق کی' },
    { id: 'gd_control_anger', label: 'غصے پر قابو رکھا' },
    { id: 'gd_patience', label: 'صبر کیا' },
    { id: 'gd_gratitude', label: 'شکر ادا کیا' },
  ],
  en: [
    { id: 'gd_fardh_prayers', label: 'Observed prayers (Fardh)' },
    { id: 'gd_shagle_noori', label: 'Performed Shagle Noori' },
    { id: 'gd_durood_shareef', label: 'Recited Durood Shareef' },
    { id: 'gd_tasbeeh_malaika', label: 'Recited Tasbeeh-e-Malaika' },
    { id: 'gd_shajra_shareef', label: 'Recited Shajra Shareef' },
    { id: 'gd_muraqaba', label: 'Performed Muraqaba (Meditation)' },
    { id: 'gd_walidain_khidmat', label: 'Served parents' },
    { id: 'gd_istighfaar', label: 'Sought forgiveness (Istighfar)' },
    { id: 'gd_quran_recitation', label: 'Recited the Quran' },
    { id: 'gd_sunnah_nawafil', label: 'Offered Sunnah/Nawafil prayers' },
    { id: 'gd_niyyat_ikhlas', label: 'Sincerity in intention' },
    { id: 'gd_qalb_taharat', label: 'Purity of heart' },
    { id: 'gd_kirdaar_istiqamat', label: 'Steadfastness in character' },
    { id: 'gd_sachchai', label: 'Practiced truthfulness' },
    { id: 'gd_luqma_halal', label: 'Ensured lawful sustenance' },
    { id: 'gd_amanat_pasdari', label: 'Upheld trusts and promises' },
    { id: 'gd_nafs_tazkiya', label: 'Strived for purification of the self (Nafs)' },
    { id: 'gd_tawakkul_allah', label: 'Maintained complete trust in Allah' },
    { id: 'gd_tasleem_riza', label: 'Remained content with Allah\'s decree' },
    { id: 'gd_dunya_beraghbati', label: 'Practiced detachment from the world' },
    { id: 'gd_fikr_akhirat', label: 'Reflected on the Hereafter' },
    { id: 'gd_sohbat_saliheen', label: 'Sought the company of the righteous' },
    { id: 'gd_zikr_kasrat', label: 'Performed abundant remembrance of Allah (Zikr)' },
    { id: 'gd_paas_anfaas', label: 'Maintained awareness of breaths (Paas Anfaas)' },
    { id: 'gd_aajizi_inkisari', label: 'Practiced humility and modesty' },
    { id: 'gd_khidmat_khalq', label: 'Served the creation' },
    { id: 'gd_control_anger', label: 'Controlled anger' },
    { id: 'gd_patience', label: 'Practiced patience' },
    { id: 'gd_gratitude', label: 'Expressed gratitude' },
  ],
  ro: [
    { id: 'gd_fardh_prayers', label: 'Namaz ki pabandi (Farz)' },
    { id: 'gd_shagle_noori', label: 'Shagle Noori kiya' },
    { id: 'gd_durood_shareef', label: 'Durood Shareef parha' },
    { id: 'gd_tasbeeh_malaika', label: 'Tasbeeh Malaika parhi' },
    { id: 'gd_shajra_shareef', label: 'Shajra Shareef parha' },
    { id: 'gd_muraqaba', label: 'Muraqaba kiya' },
    { id: 'gd_walidain_khidmat', label: 'Walidain ki khidmat ki' },
    { id: 'gd_istighfaar', label: 'Istighfaar kiya' },
    { id: 'gd_quran_recitation', label: 'Tilawat-e-Quran ki' },
    { id: 'gd_sunnah_nawafil', label: 'Sunnat/Nawafil namazein ada keen' },
    { id: 'gd_niyyat_ikhlas', label: 'Niyyat mein ikhlaas' },
    { id: 'gd_qalb_taharat', label: 'Qalb ki tahaarat' },
    { id: 'gd_kirdaar_istiqamat', label: 'Kirdaar mein istiqamat' },
    { id: 'gd_sachchai', label: 'Sachchaai ikhtiyaar ki' },
    { id: 'gd_luqma_halal', label: 'Luqma-e-halaal ka ehtemaam' },
    { id: 'gd_amanat_pasdari', label: 'Amaanat aur \'ahd ki paasdaari' },
    { id: 'gd_nafs_tazkiya', label: 'Nafs ka tazkiyah karne ki koshish ki' },
    { id: 'gd_tawakkul_allah', label: 'Allah par kaamil tawakkul rakha' },
    { id: 'gd_tasleem_riza', label: 'Allah ki raza par raazi raha' },
    { id: 'gd_dunya_beraghbati', label: 'Dunya se be-raghbati ikhtiyaar ki' },
    { id: 'gd_fikr_akhirat', label: 'Aakhirat ki fikr ki' },
    { id: 'gd_sohbat_saliheen', label: 'Saaliheen ki sohbat ikhtiyaar ki' },
    { id: 'gd_zikr_kasrat', label: 'Zikr-e-Ilaahi ki kasrat ki' },
    { id: 'gd_paas_anfaas', label: 'Paas Anfaas ka khayaal rakha' },
    { id: 'gd_aajizi_inkisari', label: '\'Aajizi o inkisaari ikhtiyaar ki' },
    { id: 'gd_khidmat_khalq', label: 'Khidmat-e-khalq ki' },
    { id: 'gd_control_anger', label: 'Gusse par qaabu rakha' },
    { id: 'gd_patience', label: 'Sabr kiya' },
    { id: 'gd_gratitude', label: 'Shukr ada kiya' },
  ],
  hi: [
    { id: 'gd_fardh_prayers', label: 'नमाज़ की पाबंदी (फ़र्ज़)' },
    { id: 'gd_shagle_noori', label: 'शग़्ल-ए-नूरी किया' },
    { id: 'gd_durood_shareef', label: 'दुरूद शरीफ़ पढ़ा' },
    { id: 'gd_tasbeeh_malaika', label: 'तस्बीह मलाइका पढ़ी' },
    { id: 'gd_shajra_shareef', label: 'शजरा शरीफ़ पढ़ा' },
    { id: 'gd_muraqaba', label: 'मुराक़बा किया' },
    { id: 'gd_walidain_khidmat', label: 'वालिदैन की ख़िदमत की' },
    { id: 'gd_istighfaar', label: 'इस्तिग़फ़ार किया' },
    { id: 'gd_quran_recitation', label: 'तिलावत-ए-क़ुरान की' },
    { id: 'gd_sunnah_nawafil', label: 'सुन्नत/नवाफ़िल नमाज़ें अदा कीं' },
    { id: 'gd_niyyat_ikhlas', label: 'नीयत में इख़लास' },
    { id: 'gd_qalb_taharat', label: 'क़ल्ब की तहारत' },
    { id: 'gd_kirdaar_istiqamat', label: 'किरदार में इस्तिक़ामत' },
    { id: 'gd_sachchai', label: 'सच्चाई इख़्तियार की' },
    { id: 'gd_luqma_halal', label: 'लुक़्मा-ए-हलाल का एहतिमाम' },
    { id: 'gd_amanat_pasdari', label: 'अमानत और अहद की पासदारी' },
    { id: 'gd_nafs_tazkiya', label: 'नफ़्स का तज़किया करने की कोशिश की' },
    { id: 'gd_tawakkul_allah', label: 'अल्लाह पर कामिल तवक्कुल रखा' },
    { id: 'gd_tasleem_riza', label: 'अल्लाह की रज़ा पर राज़ी रहा' },
    { id: 'gd_dunya_beraghbati', label: 'दुनिया से बे-रग़बती इख़्तियार की' },
    { id: 'gd_fikr_akhirat', label: 'आख़िरत की फ़िक्र की' },
    { id: 'gd_sohbat_saliheen', label: 'सालिहीन की सोहबत इख़्तियार की' },
    { id: 'gd_zikr_kasrat', label: 'ज़िक्र-ए-इलाही की कसरत की' },
    { id: 'gd_paas_anfaas', label: 'पास-अनफ़ास का ख़याल रखा' },
    { id: 'gd_aajizi_inkisari', label: 'आजिज़ी ओ इनकिसारी इख़्तियार की' },
    { id: 'gd_khidmat_khalq', label: 'ख़िदमत-ए-ख़ल्क़ की' },
    { id: 'gd_control_anger', label: 'गुस्से पर क़ाबू रखा' },
    { id: 'gd_patience', label: 'सब्र किया' },
    { id: 'gd_gratitude', label: 'शुक्र अदा किया' },
  ],
  ar: [
    { id: 'gd_fardh_prayers', label: 'المحافظة على الصلاة (الفرائض)' },
    { id: 'gd_shagle_noori', label: 'أداء الشغل النوري' },
    { id: 'gd_durood_shareef', label: 'قراءة الدرود الشريف' },
    { id: 'gd_tasbeeh_malaika', label: 'قراءة تسبيح الملائكة' },
    { id: 'gd_shajra_shareef', label: 'قراءة الشجرة الشريفة' },
    { id: 'gd_muraqaba', label: 'أداء المراقبة' },
    { id: 'gd_walidain_khidmat', label: 'خدمة الوالدين' },
    { id: 'gd_istighfaar', label: 'الاستغفار' },
    { id: 'gd_quran_recitation', label: 'تلاوة القرآن' },
    { id: 'gd_sunnah_nawafil', label: 'أداء صلوات السنن والنوافل' },
    { id: 'gd_niyyat_ikhlas', label: 'الإخلاص في النية' },
    { id: 'gd_qalb_taharat', label: 'طهارة القلب' },
    { id: 'gd_kirdaar_istiqamat', label: 'الاستقامة في السلوك' },
    { id: 'gd_sachchai', label: 'التزام الصدق' },
    { id: 'gd_luqma_halal', label: 'تحري الحلال في الكسب' },
    { id: 'gd_amanat_pasdari', label: 'أداء الأمانات والوفاء بالعهود' },
    { id: 'gd_nafs_tazkiya', label: 'السعي في تزكية النفس' },
    { id: 'gd_tawakkul_allah', label: 'التوكل الكامل على الله' },
    { id: 'gd_tasleem_riza', label: 'الرضا بقضاء الله وقدره' },
    { id: 'gd_dunya_beraghbati', label: 'الزهد في الدنيا' },
    { id: 'gd_fikr_akhirat', label: 'التفكر في الآخرة' },
    { id: 'gd_sohbat_saliheen', label: 'صحبة الصالحين' },
    { id: 'gd_zikr_kasrat', label: 'الإكثار من ذكر الله' },
    { id: 'gd_paas_anfaas', label: 'مراعاة الأنفاس (مراقبة الأنفاس)' },
    { id: 'gd_aajizi_inkisari', label: 'التواضع والانكسار لله' },
    { id: 'gd_khidmat_khalq', label: 'خدمة الخلق' },
    { id: 'gd_control_anger', label: 'التحكم في الغضب' },
    { id: 'gd_patience', label: 'التحلي بالصبر' },
    { id: 'gd_gratitude', label: 'إظهار الشكر' },
  ],
};

const shortcomingsItemsContent: Record<Language, ChecklistItem[]> = {
  ur: [
    { id: 'sc_missing_fardh_prayers', label: 'فرض نمازیں قضا کیں' },
    { id: 'sc_prayer_laziness', label: 'نمازوں میں سستی کی' },
    { id: 'sc_riya_kari', label: 'ریا کاری کی' },
    { id: 'sc_niyyat_kami', label: 'نیت میں اخلاص کی کمی' },
    { id: 'sc_qalb_aaludgi', label: 'قلب کی آلائشیں (حسد، تکبر وغیرہ)' },
    { id: 'sc_badnigahi', label: 'بدنگاہی کی' },
    { id: 'sc_jhoot_bolna', label: 'جھوٹ بولا' },
    { id: 'sc_luqma_mashbook', label: 'مشکوک یا حرام لقمہ استعمال کیا' },
    { id: 'sc_khayanat_ahd_shikni', label: 'امانت میں خیانت یا وعدہ خلافی کی' },
    { id: 'sc_nafs_pairavi', label: 'نفسانی خواہشات کی پیروی کی' },
    { id: 'sc_tawakkul_kami', label: 'توکل میں کمی' },
    { id: 'sc_narazgi_taqdeer', label: 'تقدیر پر ناراضگی یا شکوہ' },
    { id: 'sc_dunya_raghbat', label: 'دنیا کی طرف رغبت' },
    { id: 'sc_ghaflat_akhirat', label: 'آخرت سے غفلت' },
    { id: 'sc_sohbat_ghaafileen', label: 'غافلین کی صحبت اختیار کی' },
    { id: 'sc_zikr_kami', label: 'ذکرِ الٰہی میں کمی یا غفلت' },
    { id: 'sc_takabbur_ghuroor', label: 'تکبر یا غرور کیا' },
    { id: 'sc_khidmat_kotahi', label: 'خدمتِ خلق میں کوتاہی' },
    { id: 'sc_backbiting', label: 'غیبت کی' },
    { id: 'sc_wasting_time', label: 'وقت ضائع کیا' },
    { id: 'sc_unnecessary_anger', label: 'بلاوجہ غصہ کیا' },
    { id: 'sc_ingratitude', label: 'ناشکری کی' },
    { id: 'sc_impatience', label: 'بے صبری کی' },
  ],
  en: [
    { id: 'sc_missing_fardh_prayers', label: 'Missed Fardh prayers' },
    { id: 'sc_prayer_laziness', label: 'Showed laziness in prayers' },
    { id: 'sc_riya_kari', label: 'Showed off / Performed actions for show' },
    { id: 'sc_niyyat_kami', label: 'Lack of sincerity in intention' },
    { id: 'sc_qalb_aaludgi', label: 'Impurities of the heart (envy, pride, etc.)' },
    { id: 'sc_badnigahi', label: 'Casted evil/lustful gaze' },
    { id: 'sc_jhoot_bolna', label: 'Told a lie' },
    { id: 'sc_luqma_mashbook', label: 'Consumed doubtful or unlawful sustenance' },
    { id: 'sc_khayanat_ahd_shikni', label: 'Betrayed trust or broke a promise' },
    { id: 'sc_nafs_pairavi', label: 'Followed desires of the self (Nafs)' },
    { id: 'sc_tawakkul_kami', label: 'Lack of trust in Allah' },
    { id: 'sc_narazgi_taqdeer', label: 'Discontentment or complaint about destiny' },
    { id: 'sc_dunya_raghbat', label: 'Inclination towards the world' },
    { id: 'sc_ghaflat_akhirat', label: 'Heedlessness of the Hereafter' },
    { id: 'sc_sohbat_ghaafileen', label: 'Kept company of the heedless' },
    { id: 'sc_zikr_kami', label: 'Deficiency or negligence in Zikr' },
    { id: 'sc_takabbur_ghuroor', label: 'Exhibited pride or arrogance' },
    { id: 'sc_khidmat_kotahi', label: 'Shortcoming in serving creation' },
    { id: 'sc_backbiting', label: 'Engaged in backbiting' },
    { id: 'sc_wasting_time', label: 'Wasted time' },
    { id: 'sc_unnecessary_anger', label: 'Got angry unnecessarily' },
    { id: 'sc_ingratitude', label: 'Showed ingratitude' },
    { id: 'sc_impatience', label: 'Showed impatience' },
  ],
  ro: [
    { id: 'sc_missing_fardh_prayers', label: 'Farz namazein qaza keen' },
    { id: 'sc_prayer_laziness', label: 'Namazon mein susti ki' },
    { id: 'sc_riya_kari', label: 'Riya kaari ki' },
    { id: 'sc_niyyat_kami', label: 'Niyyat mein ikhlaas ki kami' },
    { id: 'sc_qalb_aaludgi', label: 'Qalb ki aalaa\'ishein (hasad, takabbur waghera)' },
    { id: 'sc_badnigahi', label: 'Badnigahi ki' },
    { id: 'sc_jhoot_bolna', label: 'Jhoot bola' },
    { id: 'sc_luqma_mashbook', label: 'Mashkook ya haraam luqma istemaal kiya' },
    { id: 'sc_khayanat_ahd_shikni', label: 'Amaanat mein khayanat ya wa\'dah khilaafi ki' },
    { id: 'sc_nafs_pairavi', label: 'Nafsaani khwaahishaat ki pairavi ki' },
    { id: 'sc_tawakkul_kami', label: 'Tawakkul mein kami' },
    { id: 'sc_narazgi_taqdeer', label: 'Taqdeer par naaraazgi ya shikwah' },
    { id: 'sc_dunya_raghbat', label: 'Dunya ki taraf raghbat' },
    { id: 'sc_ghaflat_akhirat', label: 'Aakhirat se ghaflat' },
    { id: 'sc_sohbat_ghaafileen', label: 'Ghaafileen ki sohbat ikhtiyaar ki' },
    { id: 'sc_zikr_kami', label: 'Zikr-e-Ilaahi mein kami ya ghaflat' },
    { id: 'sc_takabbur_ghuroor', label: 'Takabbur ya ghuroor kiya' },
    { id: 'sc_khidmat_kotahi', label: 'Khidmat-e-khalq mein kotaahi' },
    { id: 'sc_backbiting', label: 'Gheebat ki' },
    { id: 'sc_wasting_time', label: 'Waqt zaaya kiya' },
    { id: 'sc_unnecessary_anger', label: 'Bila wajah ghussa kiya' },
    { id: 'sc_ingratitude', label: 'Nashukri ki' },
    { id: 'sc_impatience', label: 'Be-sabri ki' },
  ],
  hi: [
    { id: 'sc_missing_fardh_prayers', label: 'फ़र्ज़ नमाज़ें क़ज़ा कीं' },
    { id: 'sc_prayer_laziness', label: 'नमाज़ों में सुस्ती की' },
    { id: 'sc_riya_kari', label: 'रियाकारी की' },
    { id: 'sc_niyyat_kami', label: 'नीयत में इख़लास की कमी' },
    { id: 'sc_qalb_aaludgi', label: 'क़ल्ब की आलाइशें (हसद, तकब्बुर वग़ैरह)' },
    { id: 'sc_badnigahi', label: 'बदनिगाही की' },
    { id: 'sc_jhoot_bolna', label: 'झूठ बोला' },
    { id: 'sc_luqma_mashbook', label: 'मशकूक या हराम लुक़्मा इस्तेमाल किया' },
    { id: 'sc_khayanat_ahd_shikni', label: 'अमानत में ख़यानत या वादा-ख़िलाफ़ी की' },
    { id: 'sc_nafs_pairavi', label: 'नफ़्सानी ख़्वाहिशात की पैरवी की' },
    { id: 'sc_tawakkul_kami', label: 'तवक्कुल में कमी' },
    { id: 'sc_narazgi_taqdeer', label: 'तक़दीर पर नाराज़गी या शिकवा' },
    { id: 'sc_dunya_raghbat', label: 'दुनिया की तरफ़ रग़बत' },
    { id: 'sc_ghaflat_akhirat', label: 'आख़िरत से ग़फ़लत' },
    { id: 'sc_sohbat_ghaafileen', label: 'ग़ाफ़िलीन की सोहबत इख़्तियार की' },
    { id: 'sc_zikr_kami', label: 'ज़िक्र-ए-इलाही में कमी या ग़फ़लत' },
    { id: 'sc_takabbur_ghuroor', label: 'तकब्बुर या ग़ुरूर किया' },
    { id: 'sc_khidmat_kotahi', label: 'ख़िदमत-ए-ख़ल्क़ में कोताही' },
    { id: 'sc_backbiting', label: 'ग़ीबत की' },
    { id: 'sc_wasting_time', label: 'वक़्त ज़ाया किया' },
    { id: 'sc_unnecessary_anger', label: 'बिला वजह गुस्सा किया' },
    { id: 'sc_ingratitude', label: 'नाशुक्री की' },
    { id: 'sc_impatience', label: 'बे-सब्री की' },
  ],
  ar: [
    { id: 'sc_missing_fardh_prayers', label: 'تفويت الصلوات المفروضة' },
    { id: 'sc_prayer_laziness', label: 'التكاسل في الصلاة' },
    { id: 'sc_riya_kari', label: 'الرياء (القيام بالأعمال للمباهاة)' },
    { id: 'sc_niyyat_kami', label: 'نقص الإخلاص في النية' },
    { id: 'sc_qalb_aaludgi', label: 'شوائب القلب (الحسد، الكبر، إلخ)' },
    { id: 'sc_badnigahi', label: 'إطلاق البصر فيما حرم الله' },
    { id: 'sc_jhoot_bolna', label: 'الكذب' },
    { id: 'sc_luqma_mashbook', label: 'أكل المشبوه أو الحرام' },
    { id: 'sc_khayanat_ahd_shikni', label: 'خيانة الأمانة أو نقض العهد' },
    { id: 'sc_nafs_pairavi', label: 'اتباع هوى النفس' },
    { id: 'sc_tawakkul_kami', label: 'نقص التوكل على الله' },
    { id: 'sc_narazgi_taqdeer', label: 'السخط على القدر أو الشكوى منه' },
    { id: 'sc_dunya_raghbat', label: 'الرغبة في الدنيا' },
    { id: 'sc_ghaflat_akhirat', label: 'الغفلة عن الآخرة' },
    { id: 'sc_sohbat_ghaafileen', label: 'صحبة الغافلين' },
    { id: 'sc_zikr_kami', label: 'التقصير أو الغفلة في ذكر الله' },
    { id: 'sc_takabbur_ghuroor', label: 'الكبر أو الغرور' },
    { id: 'sc_khidmat_kotahi', label: 'التقصير في خدمة الخلق' },
    { id: 'sc_backbiting', label: 'الغيبة' },
    { id: 'sc_wasting_time', label: 'إضاعة الوقت' },
    { id: 'sc_unnecessary_anger', label: 'الغضب بلا مبرر' },
    { id: 'sc_ingratitude', label: 'كفران النعمة' },
    { id: 'sc_impatience', label: 'قلة الصبر' },
  ],
};
// --- End Checklist Items ---

type SessionTranslations = {
  pageTitle: string;
  pageSubtitle: string;
  selectDate: string;
  goodDeedsTitle: string;
  manualGoodDeedsPlaceholder: string;
  shortcomingsTitle: string;
  manualShortcomingsPlaceholder: string;
  resolutionsTitle: string;
  resolutionsPlaceholder: string;
  saveButton: string;
  dataSavedToast: string;
  backToMuhasaba: string;
  loadingData: string;
  pleaseSelectDate: string;
};

const translations: Record<Language, SessionTranslations> = {
  en: {
    pageTitle: "Muhasaba Session",
    pageSubtitle: "Reflect on your day and account for your actions.",
    selectDate: "Select Date for Muhasaba",
    goodDeedsTitle: "Good Deeds & Blessings",
    manualGoodDeedsPlaceholder: "Other good deeds or blessings to note...",
    shortcomingsTitle: "Shortcomings & Sins",
    manualShortcomingsPlaceholder: "Other shortcomings, sins, or areas for improvement...",
    resolutionsTitle: "Resolutions for Tomorrow",
    resolutionsPlaceholder: "What will you strive to do better or start doing tomorrow?",
    saveButton: "Save Muhasaba",
    dataSavedToast: "Muhasaba for {date} saved successfully!",
    backToMuhasaba: "Back to Muhasaba Overview",
    loadingData: "Loading data for selected date...",
    pleaseSelectDate: "Please select a date to begin your Muhasaba.",
  },
  ur: {
    pageTitle: "محاسبہ سیشن",
    pageSubtitle: "اپنے دن پر غور کریں اور اپنے اعمال کا محاسبہ کریں۔",
    selectDate: "محاسبہ کے لیے تاریخ منتخب کریں",
    goodDeedsTitle: "نیک اعمال اور نعمتیں",
    manualGoodDeedsPlaceholder: "دیگر نیک اعمال یا نعمتیں یہاں لکھیں۔۔۔",
    shortcomingsTitle: "کوتاہیاں اور گناہ",
    manualShortcomingsPlaceholder: "دیگر کوتاہیاں، گناہ، یا اصلاح طلب امور یہاں لکھیں۔۔۔",
    resolutionsTitle: "آئندہ کل کے لیے عزم",
    resolutionsPlaceholder: "کل آپ کیا بہتر کرنے یا شروع کرنے کی کوشش کریں گے؟",
    saveButton: "محاسبہ محفوظ کریں",
    dataSavedToast: "{date} کا محاسبہ کامیابی سے محفوظ ہو گیا!",
    backToMuhasaba: "محاسبہ کا جائزہ پر واپس",
    loadingData: "منتخب تاریخ کے لیے ڈیٹا لوڈ ہو رہا ہے۔۔۔",
    pleaseSelectDate: "براہ کرم اپنا محاسبہ شروع کرنے کے لیے تاریخ منتخب کریں۔",
  },
  ro: {
    pageTitle: "Muhasaba Session",
    pageSubtitle: "Apne din par ghaur karein aur apne a'maal ka muhasaba karein.",
    selectDate: "Muhasaba ke liye Tareekh Muntakhab Karein",
    goodDeedsTitle: "Nek A'maal aur Ne'matein",
    manualGoodDeedsPlaceholder: "Deegar nek a'maal ya ne'matein yahan likhein...",
    shortcomingsTitle: "Kotaahiyan aur Gunaah",
    manualShortcomingsPlaceholder: "Deegar kotaahiyan, gunaah, ya islaah talab umoor yahan likhein...",
    resolutionsTitle: "Aa'inda Kal ke liye 'Azm",
    resolutionsPlaceholder: "Kal aap kya behtar karne ya shuru karne ki koshish karenge?",
    saveButton: "Muhasaba Mehfooz Karein",
    dataSavedToast: "{date} ka Muhasaba kaamyaabi se mehfooz ho gaya!",
    backToMuhasaba: "Muhasaba Overview par Wapas",
    loadingData: "Muntakhab tareekh ke liye data load ho raha hai...",
    pleaseSelectDate: "Baraah-e-karam apna Muhasaba shuru karne ke liye tareekh muntakhab karein.",
  },
  hi: {
    pageTitle: "मुहासबा सेशन",
    pageSubtitle: "अपने दिन पर ग़ौर करें और अपने आमाल का मुहासबा करें।",
    selectDate: "मुहासबा के लिए तारीख़ चुनें",
    goodDeedsTitle: "नेक आमाल और नेमतें",
    manualGoodDeedsPlaceholder: "दीगर नेक आमाल या नेमतें यहाँ लिखें...",
    shortcomingsTitle: "कोताहियाँ और गुनाह",
    manualShortcomingsPlaceholder: "दीगर कोताहियाँ, गुनाह, या इस्लाह तलब उमूर यहाँ लिखें...",
    resolutionsTitle: "आइंदा कल के लिए अज़्म",
    resolutionsPlaceholder: "कल आप क्या बेहतर करने या शुरू करने की कोशिश करेंगे?",
    saveButton: "मुहासबा महफ़ूज़ करें",
    dataSavedToast: "{date} का मुहासबा कामयाबी से महफ़ूज़ हो गया!",
    backToMuhasaba: "मुहासबा अवलोकन पर वापस",
    loadingData: "चयनित तिथि के लिए डेटा लोड हो रहा है...",
    pleaseSelectDate: "कृपया अपना मुहासबा शुरू करने के लिए एक तिथि चुनें।",
  },
  ar: {
    pageTitle: "جلسة محاسبة",
    pageSubtitle: "تأمل في يومك وحاسب نفسك على أعمالك.",
    selectDate: "اختر تاريخ المحاسبة",
    goodDeedsTitle: "الأعمال الصالحة والنعم",
    manualGoodDeedsPlaceholder: "أعمال صالحة أخرى أو نعم لتدوينها...",
    shortcomingsTitle: "التقصير والذنوب",
    manualShortcomingsPlaceholder: "تقصيرات أخرى، ذنوب، أو جوانب تحتاج إلى تحسين...",
    resolutionsTitle: "عزائم الغد",
    resolutionsPlaceholder: "ما الذي ستسعى لفعله بشكل أفضل أو البدء به غداً؟",
    saveButton: "حفظ المحاسبة",
    dataSavedToast: "تم حفظ محاسبة {date} بنجاح!",
    backToMuhasaba: "العودة إلى نظرة عامة على المحاسبة",
    loadingData: "جارٍ تحميل بيانات التاريخ المحدد...",
    pleaseSelectDate: "يرجى تحديد تاريخ لبدء المحاسبة.",
  },
};

export default function MuhasabaSessionPage() {
  const { language } = useLanguage();
  const content = translations[language] || translations.en;
  const { toast } = useToast();
  const router = useRouter();

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  
  const [checkedGoodDeeds, setCheckedGoodDeeds] = useState<string[]>([]);
  const [manualGoodDeeds, setManualGoodDeeds] = useState("");
  const [checkedShortcomings, setCheckedShortcomings] = useState<string[]>([]);
  const [manualShortcomings, setManualShortcomings] = useState("");
  const [resolutions, setResolutions] = useState("");
  
  const [isLoading, setIsLoading] = useState(false); 

  const getStorageKey = (date: Date | undefined): string => {
    if (!date) return "muhasaba_v2_undefined_date"; // Added _v2 to avoid conflict with old structure
    return `muhasaba_v2_${format(date, "yyyy-MM-dd")}`;
  };

  useEffect(() => {
    if (selectedDate && typeof window !== "undefined") {
      setIsLoading(true);
      const storageKey = getStorageKey(selectedDate);
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        try {
          const parsedData: MuhasabaEntry = JSON.parse(savedData);
          setCheckedGoodDeeds(Array.isArray(parsedData.checkedGoodDeeds) ? parsedData.checkedGoodDeeds : []);
          setManualGoodDeeds(parsedData.manualGoodDeeds || "");
          setCheckedShortcomings(Array.isArray(parsedData.checkedShortcomings) ? parsedData.checkedShortcomings : []);
          setManualShortcomings(parsedData.manualShortcomings || "");
          setResolutions(parsedData.resolutions || "");
        } catch (e) {
          console.error("Error parsing Muhasaba data from localStorage", e);
          setCheckedGoodDeeds([]);
          setManualGoodDeeds("");
          setCheckedShortcomings([]);
          setManualShortcomings("");
          setResolutions("");
        }
      } else {
        setCheckedGoodDeeds([]);
        setManualGoodDeeds("");
        setCheckedShortcomings([]);
        setManualShortcomings("");
        setResolutions("");
      }
      setIsLoading(false);
    } else {
        setCheckedGoodDeeds([]);
        setManualGoodDeeds("");
        setCheckedShortcomings([]);
        setManualShortcomings("");
        setResolutions("");
        setIsLoading(false); 
    }
  }, [selectedDate]);

  const handleSave = () => {
    if (selectedDate && typeof window !== "undefined") {
      const entry: MuhasabaEntry = { 
        checkedGoodDeeds, 
        manualGoodDeeds,
        checkedShortcomings, 
        manualShortcomings,
        resolutions 
      };
      const storageKey = getStorageKey(selectedDate);
      localStorage.setItem(storageKey, JSON.stringify(entry));
      toast({
        title: <span className="flex items-center"><CheckCircle className="h-5 w-5 mr-2 text-green-500" /> {content.dataSavedToast.replace("{date}", format(selectedDate, "PPP"))}</span>,
        description: "", 
      });
    }
  };

  const handleItemToggle = (itemId: string, listType: 'goodDeeds' | 'shortcomings') => {
    const setCheckedItems = listType === 'goodDeeds' ? setCheckedGoodDeeds : setCheckedShortcomings;
    const checkedItems = listType === 'goodDeeds' ? checkedGoodDeeds : checkedShortcomings;

    setCheckedItems(prev =>
      prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId]
    );
  };
  
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  const currentGoodDeedsList = goodDeedsItemsContent[language] || goodDeedsItemsContent.en;
  const currentShortcomingsList = shortcomingsItemsContent[language] || shortcomingsItemsContent.en;

  return (
    <div className="space-y-8">
       <div className="px-0">
        <Button variant="link" onClick={() => router.push('/practices/muhasaba')} className={cn("text-sm text-primary hover:underline mb-2 inline-flex items-center group p-0 h-auto", fontClass)}>
          <ArrowRight className={cn("h-4 w-4 transform transition-transform duration-300 group-hover:-translate-x-1", textDir === 'rtl' ? "ml-1.5 rotate-0" : "mr-1.5 rotate-180")} />
          {content.backToMuhasaba}
        </Button>
        <PageTitle 
          title={content.pageTitle} 
          subtitle={content.pageSubtitle} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      <Card className="shadow-lg bg-card/80 backdrop-blur-md border-border/30">
        <CardHeader className={cn(textDir === 'rtl' ? 'text-right' : 'text-left')}>
          <CardTitle className={cn("text-lg", fontClass)}>{content.selectDate}</CardTitle>
        </CardHeader>
        <CardContent>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal shadow-sm",
                  !selectedDate && "text-muted-foreground",
                  fontClass
                )}
              >
                <CalendarIcon className={cn("h-4 w-4", textDir === 'rtl' ? 'ml-2' : 'mr-2')} />
                {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                initialFocus
                disabled={(date) => date > new Date() || date < new Date("2000-01-01")}
              />
            </PopoverContent>
          </Popover>
        </CardContent>
      </Card>

      {!selectedDate && (
        <Card className="shadow-md text-center py-10 bg-card/80 backdrop-blur-md border-border/30">
            <CardContent>
                <p className={cn("text-muted-foreground", fontClass)}>{content.pleaseSelectDate}</p>
            </CardContent>
        </Card>
      )}

      {isLoading && selectedDate && (
        <div className="text-center py-10 flex items-center justify-center text-muted-foreground">
          <LoadingSpinner size={24} className="mr-2" />
          <span className={cn(fontClass)}>{content.loadingData}</span>
        </div>
      )}

      {!isLoading && selectedDate && (
        <div className="space-y-6">
          {/* Good Deeds Card */}
          <Card className="shadow-lg bg-card/90 backdrop-blur-sm border-border/20">
            <CardHeader className={cn(textDir === 'rtl' ? 'text-right' : 'text-left')}>
              <CardTitle className={cn("text-xl", fontClass)}>{content.goodDeedsTitle}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
                {currentGoodDeedsList.map((item) => (
                  <div key={`gd-${item.id}`} className={cn("flex items-center space-x-2", isRtl && "space-x-reverse")}>
                    <Checkbox
                      id={`gd-${item.id}`}
                      checked={checkedGoodDeeds.includes(item.id)}
                      onCheckedChange={() => handleItemToggle(item.id, 'goodDeeds')}
                      className="border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                    />
                    <Label htmlFor={`gd-${item.id}`} className={cn("font-normal text-base", fontClass, isRtl && "mr-2")}>
                      {item.label}
                    </Label>
                  </div>
                ))}
              </div>
              <Textarea
                value={manualGoodDeeds}
                onChange={(e) => setManualGoodDeeds(e.target.value)}
                placeholder={content.manualGoodDeedsPlaceholder}
                rows={3}
                className={cn("bg-background/70 text-base mt-4", fontClass)}
                lang={language}
                dir={textDir}
              />
            </CardContent>
          </Card>

          {/* Shortcomings Card */}
          <Card className="shadow-lg bg-card/90 backdrop-blur-sm border-border/20">
            <CardHeader className={cn(textDir === 'rtl' ? 'text-right' : 'text-left')}>
              <CardTitle className={cn("text-xl", fontClass)}>{content.shortcomingsTitle}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
                {currentShortcomingsList.map((item) => (
                  <div key={`sc-${item.id}`} className={cn("flex items-center space-x-2", isRtl && "space-x-reverse")}>
                    <Checkbox
                      id={`sc-${item.id}`}
                      checked={checkedShortcomings.includes(item.id)}
                      onCheckedChange={() => handleItemToggle(item.id, 'shortcomings')}
                      className="border-destructive data-[state=checked]:bg-destructive data-[state=checked]:text-destructive-foreground"
                    />
                    <Label htmlFor={`sc-${item.id}`} className={cn("font-normal text-base", fontClass, isRtl && "mr-2")}>
                      {item.label}
                    </Label>
                  </div>
                ))}
              </div>
              <Textarea
                value={manualShortcomings}
                onChange={(e) => setManualShortcomings(e.target.value)}
                placeholder={content.manualShortcomingsPlaceholder}
                rows={3}
                className={cn("bg-background/70 text-base mt-4", fontClass)}
                lang={language}
                dir={textDir}
              />
            </CardContent>
          </Card>

          {/* Resolutions Card */}
          <Card className="shadow-lg bg-card/90 backdrop-blur-sm border-border/20">
            <CardHeader className={cn(textDir === 'rtl' ? 'text-right' : 'text-left')}>
              <CardTitle className={cn("text-xl", fontClass)}>{content.resolutionsTitle}</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={resolutions}
                onChange={(e) => setResolutions(e.target.value)}
                placeholder={content.resolutionsPlaceholder}
                rows={3}
                className={cn("bg-background/70 text-base", fontClass)}
                lang={language}
                dir={textDir}
              />
            </CardContent>
          </Card>

          <div className="flex justify-end pt-4">
            <Button onClick={handleSave} size="lg" className={cn("font-semibold shadow-md hover:shadow-lg hover:shadow-primary/30", fontClass)}>
              <Save className={cn("h-5 w-5", textDir === 'rtl' ? 'ml-2' : 'mr-2')} />
              {content.saveButton}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

    

