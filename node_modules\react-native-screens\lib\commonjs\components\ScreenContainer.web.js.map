{"version": 3, "names": ["_reactNative", "require", "NativeScreenContainer", "exports", "View", "NativeScreenNavigationContainer", "_default", "default"], "sourceRoot": "../../../src", "sources": ["components/ScreenContainer.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAGE,iBAAI;AAClC,MAAMC,+BAA+B,GAAAF,OAAA,CAAAE,+BAAA,GAAGD,iBAAI;AAAC,IAAAE,QAAA,GAAAH,OAAA,CAAAI,OAAA,GAErCH,iBAAI"}