
"use client";

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { db } from '@/lib/firebaseClient';
import {
  collection,
  query,
  orderBy,
  onSnapshot,
  addDoc,
  serverTimestamp,
  doc,
  setDoc,
  getDoc,
  type Timestamp,
  type DocumentData
} from 'firebase/firestore';
import ChatMessage, { type ChatMessageProps as AppChatMessage } from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import PageTitle from '@/components/shared/PageTitle';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

// Define a placeholder UID for the Khanqah Admin
// **IMPORTANT: Replace this with the actual Firebase UID of the admin account in your project**
const KHANQAH_ADMIN_UID = "KHANQAH_ADMIN_ACCOUNT_UID"; 
const KHANQAH_ADMIN_NAME = "Sabiriya Khanqah"; // Display name for the admin
const KHANQAH_ADMIN_AVATAR = "https://placehold.co/100x100/7dd3fc/0f172a.png?text=SK"; // Placeholder

interface Message {
  id: string;
  senderId: string;
  text: string;
  timestamp: Timestamp | null; // Firestore timestamp can be null initially with serverTimestamp
}

const chatPageTranslations: Record<Language, {
    pageTitle: string;
    pageSubtitle: string;
    chattingWith: string;
    inputPlaceholder: string;
    loadingMessages: string;
    errorLoadingMessages: string;
    startConversation: string;
    loginToChat: string;
}> = {
    en: {
        pageTitle: "Chat with Khanqah",
        pageSubtitle: "Ask questions or seek guidance directly.",
        chattingWith: "Chatting with",
        inputPlaceholder: "Type your message to the Khanqah...",
        loadingMessages: "Loading messages...",
        errorLoadingMessages: "Error loading messages. Please try again.",
        startConversation: "Start a conversation by sending a message.",
        loginToChat: "Please log in to chat with the Khanqah.",
    },
    ur: {
        pageTitle: "خانقاہ سے بات کریں",
        pageSubtitle: "براہ راست سوالات پوچھیں یا رہنمائی حاصل کریں۔",
        chattingWith: "کے ساتھ بات چیت",
        inputPlaceholder: "خانقاہ کے لیے اپنا پیغام ٹائپ کریں۔۔۔",
        loadingMessages: "پیغامات لوڈ ہو رہے ہیں۔۔۔",
        errorLoadingMessages: "پیغامات لوڈ کرنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں۔",
        startConversation: "پیغام بھیج کر گفتگو شروع کریں۔",
        loginToChat: "براہ کرم خانقاہ سے بات کرنے کے لیے لاگ ان کریں۔",
    },
    ro: {
        pageTitle: "Khanqah se Baat Karein",
        pageSubtitle: "Baraah-e-raast sawalat poochein ya rehnumai haasil karein.",
        chattingWith: "Ke Saath Baat Cheet",
        inputPlaceholder: "Khanqah ke liye apna paigham type karein...",
        loadingMessages: "Paighamaat load ho rahe hain...",
        errorLoadingMessages: "Paighamaat load karne mein kharabi. Baraah-e-karam dobara koshish karein.",
        startConversation: "Paigham bhej kar guftagu shuru karein.",
        loginToChat: "Baraah-e-karam Khanqah se baat karne ke liye login karein.",
    },
    hi: {
        pageTitle: "खानक़ाह से बात करें",
        pageSubtitle: "सीधे सवाल पूछें या मार्गदर्शन प्राप्त करें।",
        chattingWith: "के साथ बातचीत",
        inputPlaceholder: "खानक़ाह के लिए अपना संदेश टाइप करें...",
        loadingMessages: "संदेश लोड हो रहे हैं...",
        errorLoadingMessages: "संदेश लोड करने में त्रुटि। कृपया पुनः प्रयास करें।",
        startConversation: "संदेश भेजकर बातचीत शुरू करें।",
        loginToChat: "कृपया खानक़ाह से बात करने के लिए लॉग इन करें।",
    },
    ar: {
        pageTitle: "تحدث مع الخانقاه",
        pageSubtitle: "اطرح الأسئلة أو اطلب الإرشاد مباشرة.",
        chattingWith: "تتحدث مع",
        inputPlaceholder: "اكتب رسالتك إلى الخانقاه...",
        loadingMessages: "جارٍ تحميل الرسائل...",
        errorLoadingMessages: "خطأ في تحميل الرسائل. يرجى المحاولة مرة أخرى.",
        startConversation: "ابدأ محادثة بإرسال رسالة.",
        loginToChat: "يرجى تسجيل الدخول للتحدث مع الخانقاه.",
    },
};


export default function ChatWithKhanqahPage() {
  const { user, loading: authLoading } = useAuth();
  const { language } = useLanguage();
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(true);
  const [errorMessages, setErrorMessages] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);
  
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const content = chatPageTranslations[language] || chatPageTranslations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  const getChatRoomId = (userId1: string, userId2: string): string => {
    // Ensure a consistent order for the chat room ID
    return [userId1, userId2].sort().join('_');
  };

  useEffect(() => {
    if (authLoading) return;
    if (!user) {
      router.replace(`/login?redirect=/chat/khanqah`);
      return;
    }

    const chatRoomId = getChatRoomId(user.uid, KHANQAH_ADMIN_UID);
    const messagesRef = collection(db, 'chats', chatRoomId, 'messages');
    const q = query(messagesRef, orderBy('timestamp', 'asc'));

    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const fetchedMessages: Message[] = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data() as DocumentData;
          fetchedMessages.push({
            id: doc.id,
            senderId: data.senderId,
            text: data.text,
            timestamp: data.timestamp as Timestamp | null, // Can be null if serverTimestamp is resolving
          });
        });
        setMessages(fetchedMessages);
        setIsLoadingMessages(false);
        setErrorMessages(null);
      },
      (error) => {
        console.error("Error fetching messages: ", error);
        setErrorMessages(content.errorLoadingMessages);
        setIsLoadingMessages(false);
      }
    );

    return () => unsubscribe();
  }, [user, authLoading, router, content.errorLoadingMessages]);
  
  useEffect(() => {
    // Scroll to bottom when messages change or on initial load
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({ top: scrollAreaRef.current.scrollHeight, behavior: 'smooth' });
    }
  }, [messages]);


  const handleSendMessage = async (messageText: string) => {
    if (!user || !messageText.trim()) return;
    setIsSending(true);

    const chatRoomId = getChatRoomId(user.uid, KHANQAH_ADMIN_UID);
    const messagesRef = collection(db, 'chats', chatRoomId, 'messages');
    const chatDocRef = doc(db, 'chats', chatRoomId);

    try {
      // Add the message
      await addDoc(messagesRef, {
        senderId: user.uid,
        text: messageText.trim(),
        timestamp: serverTimestamp(),
      });

      // Update the parent chat document (last message, timestamp, etc.)
      // This also creates the chat document if it doesn't exist
      await setDoc(chatDocRef, {
        participants: [user.uid, KHANQAH_ADMIN_UID],
        participantInfo: {
          [user.uid]: {
            displayName: user.displayName || user.email || "User",
            photoURL: user.photoURL || `https://placehold.co/100x100?text=${(user.displayName || user.email || "U")[0].toUpperCase()}`
          },
          [KHANQAH_ADMIN_UID]: {
            displayName: KHANQAH_ADMIN_NAME,
            photoURL: KHANQAH_ADMIN_AVATAR
          }
        },
        lastMessageText: messageText.trim(),
        lastMessageTimestamp: serverTimestamp(),
        lastMessageSenderId: user.uid,
        // Initialize unreadCount if setting doc for first time or update as needed
        // For simplicity, not fully implementing unreadCount logic here
        unreadCount: {
            [user.uid]: 0, // Sender has read it
            [KHANQAH_ADMIN_UID]: 1 // Increment for admin, a more robust way is needed
        }
      }, { merge: true }); // Merge true to update if exists, create if not

    } catch (error) {
      console.error("Error sending message: ", error);
      // Handle error (e.g., show a toast)
    } finally {
      setIsSending(false);
    }
  };

  if (authLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <LoadingSpinner size={48} />
      </div>
    );
  }

  if (!user) {
    // This case should ideally be caught by the redirect, but good for fallback
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] text-center p-4">
        <PageTitle title={content.loginToChat} />
      </div>
    );
  }

  const khanqahAvatarInitials = KHANQAH_ADMIN_NAME.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase();

  return (
    <div className={cn("flex flex-col h-[calc(100vh-var(--header-height,4rem)-var(--footer-height,0rem)-2rem)] max-w-3xl mx-auto", fontClass)} lang={language} dir={isRtl ? "rtl" : "ltr"}>
      <PageTitle title={content.pageTitle} subtitle={content.pageSubtitle} className={cn(isRtl ? 'text-right' : 'text-left', "mb-3")} />
      
      <div className="flex items-center p-3 border-b bg-card rounded-t-lg shadow">
        <Avatar className="h-10 w-10 mr-3">
            <AvatarImage src={KHANQAH_ADMIN_AVATAR} alt={KHANQAH_ADMIN_NAME} />
            <AvatarFallback>{khanqahAvatarInitials}</AvatarFallback>
        </Avatar>
        <div>
            <p className="font-semibold text-foreground">{KHANQAH_ADMIN_NAME}</p>
            <p className="text-xs text-muted-foreground">{content.chattingWith} {KHANQAH_ADMIN_NAME}</p>
        </div>
      </div>

      <ScrollArea ref={scrollAreaRef} className="flex-grow p-4 bg-background/70">
        {isLoadingMessages && (
          <div className="flex justify-center items-center h-full">
            <LoadingSpinner /> <span className="ml-2 text-muted-foreground">{content.loadingMessages}</span>
          </div>
        )}
        {errorMessages && !isLoadingMessages && <ErrorMessage message={errorMessages} />}
        {!isLoadingMessages && !errorMessages && messages.length === 0 && (
          <p className="text-center text-muted-foreground py-10">{content.startConversation}</p>
        )}
        {!isLoadingMessages && !errorMessages && messages.length > 0 && (
          <div className="space-y-1">
            {messages.map((msg) => (
              <ChatMessage
                key={msg.id}
                messageId={msg.id}
                text={msg.text}
                timestamp={msg.timestamp?.toDate() || new Date()} // Handle null timestamp from serverTimestamp
                senderId={msg.senderId}
                senderName={msg.senderId === user.uid ? (user.displayName || user.email || "You") : KHANQAH_ADMIN_NAME}
                senderAvatar={msg.senderId === user.uid ? user.photoURL || undefined : KHANQAH_ADMIN_AVATAR}
                isCurrentUser={msg.senderId === user.uid}
              />
            ))}
          </div>
        )}
      </ScrollArea>
      <ChatInput
        onSendMessage={handleSendMessage}
        placeholder={content.inputPlaceholder}
        isLoading={isSending}
        className="rounded-b-lg border-t"
      />
    </div>
  );
}

    