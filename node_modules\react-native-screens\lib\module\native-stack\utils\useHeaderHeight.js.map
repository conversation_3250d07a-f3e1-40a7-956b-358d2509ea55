{"version": 3, "names": ["React", "HeaderHeightContext", "useHeaderHeight", "height", "useContext", "undefined", "Error"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/useHeaderHeight.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,mBAAmB,MAAM,uBAAuB;AAEvD,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAEpD,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,wFACF,CAAC;EACH;EAEA,OAAOH,MAAM;AACf"}