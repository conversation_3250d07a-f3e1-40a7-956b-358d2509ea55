
import * as React from "react"
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TouchableOpacityProps,
  TextProps,
} from "react-native"
import { Ionicons } from '@expo/vector-icons'

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  foreground: '#0f172a',
  mutedForeground: '#64748b',
  border: '#e2e8f0',
  accent: '#f8fafc',
  ring: '#14b8a6',
}

export interface DialogProps extends ViewProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
}

export interface DialogTriggerProps extends TouchableOpacityProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface DialogContentProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface DialogHeaderProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface DialogFooterProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface DialogTitleProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

export interface DialogDescriptionProps extends TextProps {
  children?: React.ReactNode
  style?: TextStyle
}

const DialogContext = React.createContext<{
  open: boolean
  onOpenChange: (open: boolean) => void
}>({
  open: false,
  onOpenChange: () => {},
})

const Dialog = React.forwardRef<View, DialogProps>(
  ({ open = false, onOpenChange, children, style, ...props }, ref) => {
    return (
      <DialogContext.Provider value={{ open, onOpenChange: onOpenChange || (() => {}) }}>
        <View ref={ref} style={[style]} {...props}>
          {children}
        </View>
      </DialogContext.Provider>
    )
  }
)

const DialogTrigger = React.forwardRef<TouchableOpacity, DialogTriggerProps>(
  ({ children, style, ...props }, ref) => {
    const { onOpenChange } = React.useContext(DialogContext)

    return (
      <TouchableOpacity
        ref={ref}
        style={[style]}
        onPress={() => onOpenChange(true)}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    )
  }
)

const DialogContent = React.forwardRef<View, DialogContentProps>(
  ({ children, style, ...props }, ref) => {
    const { open, onOpenChange } = React.useContext(DialogContext)

    return (
      <Modal
        visible={open}
        transparent={true}
        animationType="fade"
        onRequestClose={() => onOpenChange(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.dialogContent, style]} {...props}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => onOpenChange(false)}
              activeOpacity={0.7}
            >
              <Ionicons name="close" size={20} color={colors.mutedForeground} />
            </TouchableOpacity>
            {children}
          </View>
        </View>
      </Modal>
    )
  }
)

const DialogHeader = React.forwardRef<View, DialogHeaderProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.dialogHeader, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const DialogFooter = React.forwardRef<View, DialogFooterProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.dialogFooter, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const DialogTitle = React.forwardRef<Text, DialogTitleProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.dialogTitle, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

const DialogDescription = React.forwardRef<Text, DialogDescriptionProps>(
  ({ children, style, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.dialogDescription, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

// Simplified components for compatibility
const DialogPortal = ({ children }: { children?: React.ReactNode }) => <>{children}</>
const DialogOverlay = View
const DialogClose = TouchableOpacity

Dialog.displayName = "Dialog"
DialogTrigger.displayName = "DialogTrigger"
DialogContent.displayName = "DialogContent"
DialogHeader.displayName = "DialogHeader"
DialogFooter.displayName = "DialogFooter"
DialogTitle.displayName = "DialogTitle"
DialogDescription.displayName = "DialogDescription"

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  dialogContent: {
    backgroundColor: colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 24,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 4,
    borderRadius: 4,
    zIndex: 1,
  },
  dialogHeader: {
    marginBottom: 16,
  },
  dialogFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 16,
  },
  dialogTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.foreground,
    lineHeight: 24,
    marginBottom: 8,
  },
  dialogDescription: {
    fontSize: 14,
    color: colors.mutedForeground,
    lineHeight: 20,
  },
})

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogClose,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
}
