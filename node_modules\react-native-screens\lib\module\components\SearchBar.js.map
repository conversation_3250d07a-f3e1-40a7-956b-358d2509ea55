{"version": 3, "names": ["React", "isSearchBarAvailableForCurrentPlatform", "View", "SearchBarNativeComponent", "Commands", "SearchBarNativeCommands", "NativeSearchBar", "NativeSearchBarCommands", "SearchBar", "Component", "constructor", "props", "nativeSearchBarRef", "createRef", "_callMethodWithRef", "method", "ref", "current", "console", "warn", "blur", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "render", "createElement", "_extends"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SACEC,sCAAsC,QAGjC,sBAAsB;AAC7B,SAASC,IAAI,QAAQ,cAAc;;AAEnC;AACA,OAAOC,wBAAwB,IAC7BC,QAAQ,IAAIC,uBAAuB,QAC9B,oCAAoC;AAE3C,OAAO,MAAMC,eACmB,GAAGH,wBAA+B;AAClE,OAAO,MAAMI,uBAA8C,GACzDF,uBAA8B;AAiBhC,MAAMG,SAAS,SAASR,KAAK,CAACS,SAAS,CAAiB;EAGtDC,WAAWA,CAACC,KAAqB,EAAE;IACjC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,kBAAkB,gBAAGZ,KAAK,CAACa,SAAS,CAAC,CAAC;EAC7C;EAEAC,kBAAkBA,CAACC,MAAwC,EAAE;IAC3D,MAAMC,GAAG,GAAG,IAAI,CAACJ,kBAAkB,CAACK,OAAO;IAC3C,IAAID,GAAG,EAAE;MACPD,MAAM,CAACC,GAAG,CAAC;IACb,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,IAAI,CAACN,kBAAkB,CAACE,GAAG,IAAIT,uBAAuB,CAACa,IAAI,CAACJ,GAAG,CAAC,CAAC;EACnE;EAEAK,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,kBAAkB,CAACE,GAAG,IAAIT,uBAAuB,CAACc,KAAK,CAACL,GAAG,CAAC,CAAC;EACpE;EAEAM,kBAAkBA,CAACC,IAAa,EAAE;IAChC,IAAI,CAACT,kBAAkB,CAACE,GAAG,IACzBT,uBAAuB,CAACe,kBAAkB,CAACN,GAAG,EAAEO,IAAI,CACtD,CAAC;EACH;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACV,kBAAkB,CAACE,GAAG,IAAIT,uBAAuB,CAACiB,SAAS,CAACR,GAAG,CAAC,CAAC;EACxE;EAEAS,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAACZ,kBAAkB,CAACE,GAAG,IAAIT,uBAAuB,CAACkB,OAAO,CAACT,GAAG,EAAEU,IAAI,CAAC,CAAC;EAC5E;EAEAC,YAAYA,CAAA,EAAG;IACb,IAAI,CAACb,kBAAkB,CAACE,GAAG,IAAIT,uBAAuB,CAACoB,YAAY,CAACX,GAAG,CAAC,CAAC;EAC3E;EAEAY,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC3B,sCAAsC,EAAE;MAC3CiB,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;MACD,OAAOjB,IAAI;IACb;IAEA,oBAAOF,KAAA,CAAA6B,aAAA,CAACvB,eAAe,EAAAwB,QAAA,KAAK,IAAI,CAACnB,KAAK;MAAEK,GAAG,EAAE,IAAI,CAACJ;IAAmB,EAAE,CAAC;EAC1E;AACF;AAEA,eAAeJ,SAAS"}