
import { LanguageProvider } from "@/context/LanguageContext"; // Ensure LanguageProvider wraps this
import { ThemeProvider } from "next-themes";

export default function SelectLanguageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    // ThemeProvider and LanguageProvider are already in the root layout,
    // but if this page is ever accessed outside that context, they'd be needed here.
    // For simplicity, assuming root layout handles providers.
    <div className="flex flex-col items-center justify-center min-h-screen bg-background text-foreground p-4">
      {children}
    </div>
  );
}
