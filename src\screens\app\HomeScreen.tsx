import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useLanguage } from '../../context/LanguageContext';
import { useAuth } from '../../context/AuthContext';

const translations = {
  pageTitle: {
    en: 'Welcome to Sabiriya',
    ur: 'صابریہ میں خوش آمدید',
    ar: 'مرحباً بكم في الصابرية',
    hi: 'साबिरिया में आपका स्वागत है',
    ro: 'Sabiriya mein Khush Amdeed',
  },
  pageSubtitle: {
    en: 'Your spiritual journey begins here',
    ur: 'آپ کا روحانی سفر یہاں سے شروع ہوتا ہے',
    ar: 'رحلتكم الروحية تبدأ من هنا',
    hi: 'आपकी आध्यात्मिक यात्रा यहाँ से शुरू होती है',
    ro: 'Aap ka roohani safar yahan se shuru hota hai',
  },
  quickActions: {
    en: 'Quick Actions',
    ur: 'فوری اعمال',
    ar: 'الإجراءات السريعة',
    hi: 'त्वरित कार्य',
    ro: 'Fori Amaal',
  },
};

const quickActionItems = [
  { id: 'duas', title: 'Duas', icon: 'book-outline', screen: 'Duas' },
  { id: 'qibla', title: 'Qibla', icon: 'compass-outline', screen: 'Qibla' },
  { id: 'tasbeeh', title: 'Tasbeeh', icon: 'repeat-outline', screen: 'Tasbeeh' },
  { id: 'announcements', title: 'Announcements', icon: 'megaphone-outline', screen: 'Announcements' },
  { id: 'shajra', title: 'Shajra', icon: 'people-outline', screen: 'Shajra' },
  { id: 'practices', title: 'Practices', icon: 'flower-outline', screen: 'Practices' },
];

export default function HomeScreen() {
  const { language } = useLanguage();
  const { user } = useAuth();
  const navigation = useNavigation();

  const pageTitle = translations.pageTitle[language] || translations.pageTitle.en;
  const pageSubtitle = translations.pageSubtitle[language] || translations.pageSubtitle.en;
  const quickActionsTitle = translations.quickActions[language] || translations.quickActions.en;

  const isRtl = language === 'ur' || language === 'ar';

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <View style={[styles.header, isRtl && styles.rtlHeader]}>
          <Text style={[styles.title, isRtl && styles.rtlText]}>{pageTitle}</Text>
          <Text style={[styles.subtitle, isRtl && styles.rtlText]}>{pageSubtitle}</Text>
        </View>

        {/* Welcome Message */}
        {user && (
          <View style={styles.welcomeCard}>
            <Text style={styles.welcomeText}>
              Welcome, {user.displayName || user.email}
            </Text>
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, isRtl && styles.rtlText]}>
            {quickActionsTitle}
          </Text>
          <View style={styles.quickActionsGrid}>
            {quickActionItems.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={styles.quickActionItem}
                onPress={() => navigation.navigate(item.screen as never)}
              >
                <Ionicons 
                  name={item.icon as keyof typeof Ionicons.glyphMap} 
                  size={32} 
                  color="#14b8a6" 
                />
                <Text style={styles.quickActionText}>{item.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  rtlHeader: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#14b8a6',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  rtlText: {
    textAlign: 'center',
    writingDirection: 'rtl',
  },
  welcomeCard: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  welcomeText: {
    fontSize: 16,
    color: '#374151',
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '48%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center',
  },
});
