
"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  useSidebar,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Sheet, SheetContent, SheetTitle } from '@/components/ui/sheet';
import Image from 'next/image';
import { Home, ScrollText, Settings, Moon, Sun, User, LogIn, LogOut, Flower2, BookMarked, Compass, Info, Repeat, ClipboardList, MessageSquare, BookText as AppBookTextIcon, BookOpen as AppBookOpenIcon, Megaphone, Languages } from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { useEffect, useState } from 'react';
import { SABIRIYA_BASE_COLOR_THEME_KEY, DEFAULT_BASE_COLOR, baseColorThemes } from '@/components/shared/ThemeSelector';
import { useLanguage, type Language } from '@/context/LanguageContext';

const appTitleTranslations: Record<Language, string> = {
  en: "Khanqahe Sabiriya",
  ur: "خانقاہِ صابریہ",
  ro: "Khanqah-e-Sabiriya",
  hi: "खानक़ाह-ए-साबिरिया",
  ar: "خانقاه الصابرية",
};

const sidebarNavTranslations: Record<string, Record<Language, string>> = {
  home: { en: "Home", ur: "مرکزی صفحہ", ro: "Markazi Safha", hi: "मरकज़ी सफ़्हा", ar: "الرئيسية" },
  announcements: { en: "Announcements", ur: "اعلانات", ro: "Aelanaat", hi: "ऐलानात", ar: "الإعلانات" },
  chatWithKhanqah: { en: "Chat with Khanqah", ur: "خانقاہ سے بات کریں", ro: "Khanqah se Baat Karein", hi: "खानक़ाह से बात करें", ar: "تحدث مع الخانقاه" },
  panjSoorah: { en: "Panj Soorah", ur: "پنج سورہ", ro: "Panj Soorah", hi: "पंज सूरह", ar: "السور الخمس" },
  shajra: { en: "Shajra Shareef", ur: "شجرہ شریف", ro: "Shajra Shareef", hi: "शजरह शरीफ़", ar: "الشجرة الشريفة" },
  teachings: { en: "Teachings", ur: "تعلیمات", ro: "Ta'leemaat", hi: "ता'लीमात", ar: "التعاليم" },
  duas: { en: "Dua and Wazaif", ur: "دعا و وظائف", ro: "Dua o Wazaif", hi: "दुआ ओ वज़ाइफ़", ar: "الدعاء والوظائف" },
  practices: { en: "Spiritual Practices", ur: "روحانی اعمال", ro: "Roohani Amaal", hi: "रूहानी आमाल", ar: "الممارسات الروحية" },
  muhasaba: { en: "Muhasaba", ur: "محاسبہ", ro: "Muhasaba", hi: "मुहासबा", ar: "المحاسبة" },
  tasbeeh: { en: "Tasbeeh Counter", ur: "تسبیح کاؤنٹر", ro: "Tasbeeh Counter", hi: "तस्बीह काउंटर", ar: "عداد التسبيح" },
  qibla: { en: "Qibla Direction", ur: "قبلہ رخ", ro: "Qibla Rukh", hi: "क़िब्ला रुख़", ar: "اتجاه القبلة" },
  aboutKhanqah: { en: "About Khanqah", ur: "خانقاہ کا تعارف", ro: "Khanqah Ka Ta'aruf", hi: "खानक़ाह का तआरुफ़", ar: "عن الخانقاه" },
  profile: { en: "Profile", ur: "پروفائل", ro: "Profile", hi: "प्रोफ़ाइल", ar: "الملف الشخصي" },
  settings: { en: "Settings", ur: "سیٹنگز", ro: "Settings", hi: "सेटिंग्स", ar: "الإعدادات" },
  signOut: { en: "Sign Out", ur: "لاگ آؤٹ", ro: "Sign Out", hi: "साइन आउट", ar: "تسجيل الخروج" },
  signIn: { en: "Sign In", ur: "لاگ ان", ro: "Sign In", hi: "साइन इन", ar: "تسجيل الدخول" },
  poweredBy: {
    en: "Powered by Sawad-e-Aazam - Rights Reserved",
    ur: "بخدمت: سوادِ اعظم - جملہ حقوق محفوظ ہیں",
    ro: "Ba-Khidmat: Sawad-e-Aazam - Jumla Huqooq Mehfooz Hain",
    hi: "ब-खिदमत: सवाद-ए-आज़म - जुमला हुक़ूक़ महफ़ूज़ हैं",
    ar: "بخدمة: سواد الأعظم - جميع الحقوق محفوظة"
  },
  myBookmarks: { en: "My Bookmarks", ur: "میرے محفوظات", ro: "Mere Mehfoozaat", hi: "मेरे बुकमार्क्स", ar: "مفضلتي" },
};

const SIDEBAR_WIDTH_MOBILE = '18rem';

export function AppSidebar() {
  const pathname = usePathname();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { isMobile, openMobile, setOpenMobile } = useSidebar();
  const { user, loading, signOutUser } = useAuth();
  const [mounted, setMounted] = useState(false);
  const { language } = useLanguage();

  const navItems = [
    { id: 'home', href: '/home', icon: Home, requiresAuth: false },
    { id: 'announcements', href: '/announcements', icon: Megaphone, requiresAuth: false },
    { id: 'chatWithKhanqah', href: '/chat/khanqah', icon: MessageSquare, requiresAuth: true },
    { id: 'panjSoorah', href: '/panjsoora', icon: BookMarked, requiresAuth: false },
    { id: 'shajra', href: '/shajra', icon: ScrollText, requiresAuth: false },
    { id: 'teachings', href: '/teachings', icon: AppBookTextIcon, requiresAuth: false },
    { id: 'duas', href: '/duas', icon: AppBookOpenIcon, requiresAuth: false },
    { id: 'practices', href: '/practices', icon: Flower2, requiresAuth: false },
    { id: 'muhasaba', href: '/practices/muhasaba', icon: ClipboardList, requiresAuth: false },
    { id: 'tasbeeh', href: '/tasbeeh', icon: Repeat, requiresAuth: false },
    { id: 'qibla', href: '/qibla', icon: Compass, requiresAuth: false, disabled: false },
    { id: 'aboutKhanqah', href: '/about-khanqah', icon: Info, requiresAuth: false }
  ];

  const bottomNavItems = [
      { id: 'profile', href: '/profile', icon: User, requiresAuth: true },
      { id: 'settings', href: '/settings', icon: Settings, requiresAuth: false },
  ];

  useEffect(() => {
    setMounted(true);
  }, []);

  const displayedNavItems = navItems.filter(item => !item.requiresAuth || (item.requiresAuth && user));
  const displayedBottomNavItems = bottomNavItems.filter(item => !item.requiresAuth || (item.requiresAuth && user));

  const toggleLightDark = () => {
    if (!mounted) return;
    let currentBaseColor = localStorage.getItem(SABIRIYA_BASE_COLOR_THEME_KEY) || DEFAULT_BASE_COLOR;
    if (!baseColorThemes.some(t => t.value === currentBaseColor)) {
        currentBaseColor = DEFAULT_BASE_COLOR;
    }
    if (resolvedTheme?.startsWith("dark")) {
      setTheme(`light-${currentBaseColor}`);
    } else {
      setTheme(`dark-${currentBaseColor}`);
    }
  };

  const handleLinkClick = () => {
    if (isMobile && openMobile) {
      setOpenMobile(false);
    }
  };

  const placeholderBookmarks: { href: string; labelKey: string }[] = [];

  if (!mounted) {
    return (
       <Sidebar variant="sidebar" collapsible="icon" className="border-r">
         <SidebarHeader className="p-4 flex items-center gap-2 justify-between">
            <div className="flex items-center gap-2">
                <Image
                  src="/app-icon.png"
                  alt="Khanqahe Sabiriya App Icon"
                  width={40}
                  height={40}
                  className="rounded-sm object-contain"
                  data-ai-hint="app icon dome calligraphy"
                />
                <span className="font-semibold text-lg text-sidebar-foreground group-data-[collapsible=icon]:hidden">Khanqahe Sabiriya</span>
            </div>
         </SidebarHeader>
         <SidebarContent className="p-2 flex flex-col justify-between">
            <div className="animate-pulse">
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
            </div>
         </SidebarContent>
         <SidebarFooter className="p-4 border-t border-sidebar-border flex flex-col items-center gap-2 group-data-[collapsible=icon]:flex-row group-data-[collapsible=icon]:justify-center">
            <div className="h-10 w-10 bg-muted rounded"></div>
         </SidebarFooter>
       </Sidebar>
    );
  }

  const isDarkMode = resolvedTheme?.startsWith("dark");
  const currentAppTitle = appTitleTranslations[language] || appTitleTranslations.en;
  const currentPoweredByText = sidebarNavTranslations['poweredBy']?.[language] || sidebarNavTranslations['poweredBy']?.['en'];

  const sidebarContent = (
    <>
      <SidebarHeader className={cn(
        "p-4 flex items-center gap-2 justify-between",
        isMobile && "bg-primary text-primary-foreground"
      )}>
         <div className="flex items-center gap-2"> {/* Wrapper for logo and title */}
            <Link href="/home" onClick={handleLinkClick} aria-label="Go to Home">
              <Image
                src="/app-icon.png"
                alt="Khanqahe Sabiriya App Icon"
                width={40}
                height={40}
                className="rounded-sm object-contain"
                data-ai-hint="app icon dome calligraphy"
              />
            </Link>
            {isMobile ? (
              // For mobile, SheetTitle provides the accessible name for the Sheet (Dialog)
              // It also serves as the visible title.
              <SheetTitle 
                className={cn(
                  "font-semibold text-lg", 
                  "text-primary-foreground", // Mobile sheet title color
                  (language === 'ur' || language === 'ar') && "font-arabic"
                )}
              >
                {currentAppTitle}
              </SheetTitle>
            ) : (
              // For desktop, the title is part of a link if needed or just a span
              <Link href="/home" onClick={handleLinkClick}>
                <span 
                  className={cn(
                    "font-semibold text-lg",
                    "text-sidebar-foreground", // Desktop sidebar title color
                    "group-data-[collapsible=icon]:hidden", // Hide on desktop collapsed
                    (language === 'ur' || language === 'ar') && "font-arabic"
                  )}
                >
                  {currentAppTitle}
                </span>
              </Link>
            )}
         </div>
        {/* SidebarTrigger was removed from here previously */}
      </SidebarHeader>
      <SidebarContent className="p-2 flex flex-col justify-between">
        <div>
          <SidebarMenu>
            {displayedNavItems.map((item) => {
              const isActive = item.href === '/home'
                              ? pathname === item.href
                              : pathname.startsWith(item.href);
              const label = sidebarNavTranslations[item.id]?.[language] || sidebarNavTranslations[item.id]?.['en'] || item.id;
              return (
                <SidebarMenuItem key={item.href}>
                  <Link href={item.disabled ? "#" : item.href} legacyBehavior passHref>
                    <SidebarMenuButton
                      onClick={handleLinkClick}
                      isActive={isActive && !item.disabled}
                      className={cn(
                        "w-full justify-start relative overflow-hidden",
                        isActive && !item.disabled
                          ? isMobile ? "bg-primary text-primary-foreground" : "bg-sidebar-accent text-sidebar-accent-foreground"
                          : isMobile ? "hover:bg-primary/80 hover:text-primary-foreground" : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                        item.disabled && "opacity-50 cursor-not-allowed",
                      )}
                      tooltip={{children: label + (item.disabled ? " (Coming Soon)" : ""), className: "bg-popover text-popover-foreground"}}
                       disabled={item.disabled}
                    >
                      {isActive && !item.disabled && !isMobile && (
                        <span className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-[hsl(var(--sidebar-primary))] to-[hsl(var(--sidebar-accent))]"></span>
                      )}
                      <item.icon className="h-5 w-5" />
                      <span className={cn("group-data-[collapsible=icon]:hidden ml-1", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}>{label}</span>
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>

          <div className="group-data-[collapsible=icon]:hidden">
            {placeholderBookmarks.length > 0 && (
              <>
                <SidebarSeparator className="my-3" />
                <div className="px-2 mb-3">
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 flex items-center">
                    <BookMarked className="h-4 w-4 mr-1.5 text-primary" /> {sidebarNavTranslations['myBookmarks']?.[language] || "My Bookmarks"}
                  </h3>
                  <SidebarMenu>
                    {placeholderBookmarks.map(bookmark => (
                      <SidebarMenuItem key={bookmark.href}>
                        <Link href={bookmark.href} passHref legacyBehavior>
                          <SidebarMenuButton onClick={handleLinkClick} size="sm" className="w-full justify-start hover:bg-sidebar-accent/50">
                              <span className="truncate text-xs">{sidebarNavTranslations[bookmark.labelKey]?.[language] || bookmark.labelKey}</span>
                          </SidebarMenuButton>
                        </Link>
                      </SidebarMenuItem>
                    ))}
                    <p className="text-xs text-muted-foreground px-2 pt-1">More bookmarks coming soon.</p>
                  </SidebarMenu>
                </div>
              </>
            )}
          </div>
        </div>

        <SidebarMenu className="mt-auto">
           {displayedBottomNavItems.map((item) => {
             const isActive = pathname.startsWith(item.href);
             const label = sidebarNavTranslations[item.id]?.[language] || sidebarNavTranslations[item.id]?.['en'] || item.id;
             return (
               <SidebarMenuItem key={item.href}>
                <Link href={item.href} legacyBehavior passHref>
                  <SidebarMenuButton
                    onClick={handleLinkClick}
                    isActive={isActive}
                    className={cn(
                      "w-full justify-start relative overflow-hidden",
                       isActive
                        ? isMobile ? "bg-primary text-primary-foreground" : "bg-sidebar-accent text-sidebar-accent-foreground"
                        : isMobile ? "hover:bg-primary/80 hover:text-primary-foreground" : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    )}
                    tooltip={{children: label, className: "bg-popover text-popover-foreground"}}
                  >
                    {isActive && !isMobile && (
                      <span className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-[hsl(var(--sidebar-primary))] to-[hsl(var(--sidebar-accent))]"></span>
                    )}
                    <item.icon className="h-5 w-5" />
                    <span className={cn("group-data-[collapsible=icon]:hidden ml-1", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}>{label}</span>
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
             );
           })}

          {!loading && user && (
             <SidebarMenuItem>
                <SidebarMenuButton
                    onClick={() => { signOutUser(); handleLinkClick(); }}
                    className={cn("w-full justify-start", isMobile ? "hover:bg-primary/80 hover:text-primary-foreground" : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}
                    tooltip={{children: sidebarNavTranslations['signOut']?.[language] || "Sign Out", className: "bg-popover text-popover-foreground"}}
                >
                    <LogOut className="h-5 w-5" />
                    <span className={cn("group-data-[collapsible=icon]:hidden ml-1", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}>{sidebarNavTranslations['signOut']?.[language] || "Sign Out"}</span>
                </SidebarMenuButton>
            </SidebarMenuItem>
          )}
          {!loading && !user && (
            <SidebarMenuItem>
              <Link href="/login" legacyBehavior passHref>
                <SidebarMenuButton
                  onClick={handleLinkClick}
                  className={cn("w-full justify-start", isMobile ? "hover:bg-primary/80 hover:text-primary-foreground" : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}
                  tooltip={{children: sidebarNavTranslations['signIn']?.[language] || "Sign In", className: "bg-popover text-popover-foreground"}}
                >
                  <LogIn className="h-5 w-5" />
                  <span className={cn("group-data-[collapsible=icon]:hidden ml-1", (language === 'ur' || language === 'ar' || language === 'hi') && "font-arabic")}>{sidebarNavTranslations['signIn']?.[language] || "Sign In"}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className={cn("p-4 border-t flex flex-col items-center gap-2 group-data-[collapsible=icon]:justify-between", isMobile ? "bg-primary/10 border-primary/20" : "border-sidebar-border")}>
        <div className="group-data-[collapsible=icon]:hidden text-center w-full mb-2">
            <Link href="https://www.sawadeazam.org" target="_blank" rel="noopener noreferrer" className="inline-block">
                <Image
                    src="/sawad-e-azam-logo.png"
                    alt="Sawad-e-Aazam Logo"
                    width={80} 
                    height={40} 
                    className="object-contain mx-auto hover:opacity-80 transition-opacity"
                    data-ai-hint="logo sawad-e-azam mosque quran"
                />
            </Link>
            <p className={cn("text-xs mt-1", isMobile ? "text-primary/80" : "text-muted-foreground", (language === 'ur' || language === 'ar') && "font-arabic")}>
                {currentPoweredByText}
            </p>
            <Link href="https://www.sawadeazam.org" target="_blank" rel="noopener noreferrer" className={cn("text-xs hover:underline", isMobile ? "text-primary/80 hover:text-primary" : "text-muted-foreground hover:text-primary", (language === 'ur' || language === 'ar') && "font-arabic")}>
                www.sawadeazam.org
            </Link>
        </div>
        <div className="flex items-center justify-center gap-2 w-full group-data-[collapsible=icon]:flex-col">
            <Button
                variant="ghost"
                size="icon"
                onClick={toggleLightDark}
                className={cn("w-full justify-center group-data-[collapsible=icon]:w-auto", isMobile ? "text-primary/90 hover:bg-primary/20" : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground", "hover:shadow-[0_0_15px_hsl(var(--primary))]")}
                aria-label="Toggle theme"
            >
                <Sun className={cn("h-5 w-5", isDarkMode ? "hidden" : "block")} />
                <Moon className={cn("h-5 w-5", isDarkMode ? "block" : "hidden")} />
            </Button>
        </div>
      </SidebarFooter>
    </>
  );

  if (isMobile) {
    return (
      <Sheet open={openMobile} onOpenChange={setOpenMobile}>
        <SheetContent
          data-sidebar="sidebar"
          data-mobile="true"
          className="w-[--sidebar-width] p-0 flex flex-col bg-sidebar text-sidebar-foreground [&>button]:hidden"
          style={{ '--sidebar-width': SIDEBAR_WIDTH_MOBILE } as React.CSSProperties}
          side="left"
        >
          {sidebarContent}
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sidebar variant="sidebar" collapsible="icon" className="border-r">
      {sidebarContent}
    </Sidebar>
  );
}

    