
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
  Modal,
  SafeAreaView,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { useLanguage, type Language } from '../../context/LanguageContext';

const appTitleTranslations: Record<Language, string> = {
  en: "Khanqahe Sabiriya",
  ur: "خانقاہِ صابریہ",
  ro: "Khanqah-e-Sabiriya",
  hi: "खानक़ाह-ए-साबिरिया",
  ar: "خانقاه الصابرية",
};

const sidebarNavTranslations: Record<string, Record<Language, string>> = {
  home: { en: "Home", ur: "مرکزی صفحہ", ro: "Markazi Safha", hi: "मरकज़ी सफ़्हा", ar: "الرئيسية" },
  duas: { en: "Du<PERSON> and <PERSON>azai<PERSON>", ur: "دعا و وظائف", ro: "<PERSON><PERSON> o Wazaif", hi: "दुआ ओ वज़ाइफ़", ar: "الدعاء والوظائف" },
  teachings: { en: "Teachings", ur: "تعلیمات", ro: "Ta'leemaat", hi: "ता'लीमات", ar: "التعاليم" },
  chat: { en: "AI Chat", ur: "AI چیٹ", ro: "AI Chat", hi: "AI चैट", ar: "دردشة الذكي" },
  qibla: { en: "Qibla Direction", ur: "قبلہ رخ", ro: "Qibla Rukh", hi: "क़िब्ला रुख़", ar: "اتجاه القبلة" },
  profile: { en: "Profile", ur: "پروفائل", ro: "Profile", hi: "प्रोफ़ाइल", ar: "الملف الشخصي" },
  settings: { en: "Settings", ur: "سیٹنگز", ro: "Settings", hi: "सेटिंग्स", ar: "الإعدادات" },
  signOut: { en: "Sign Out", ur: "لاگ آؤٹ", ro: "Sign Out", hi: "साइन आउट", ar: "تسجيل الخروج" },
  signIn: { en: "Sign In", ur: "لاگ ان", ro: "Sign In", hi: "साइन इन", ar: "تسجيل الدخول" },
  poweredBy: {
    en: "Powered by Sawad-e-Aazam",
    ur: "بخدمت: سوادِ اعظم",
    ro: "Ba-Khidmat: Sawad-e-Aazam",
    hi: "ब-खिदमत: सवाद-ए-आज़म",
    ar: "بخدمة: سواد الأعظم"
  },
};

interface AppSidebarProps {
  visible: boolean;
  onClose: () => void;
}

export function AppSidebar({ visible, onClose }: AppSidebarProps) {
  const { user, loading, signOutUser } = useAuth();
  const { language } = useLanguage();

  const navItems = [
    { id: 'home', icon: 'home-outline', requiresAuth: false },
    { id: 'duas', icon: 'book-outline', requiresAuth: false },
    { id: 'teachings', icon: 'library-outline', requiresAuth: false },
    { id: 'chat', icon: 'chatbubble-outline', requiresAuth: false },
    { id: 'qibla', icon: 'compass-outline', requiresAuth: false },
  ];

  const bottomNavItems = [
    { id: 'profile', icon: 'person-outline', requiresAuth: true },
    { id: 'settings', icon: 'settings-outline', requiresAuth: false },
  ];

  const displayedNavItems = navItems.filter(item => !item.requiresAuth || (item.requiresAuth && user));
  const displayedBottomNavItems = bottomNavItems.filter(item => !item.requiresAuth || (item.requiresAuth && user));

  const currentAppTitle = appTitleTranslations[language] || appTitleTranslations.en;
  const currentPoweredByText = sidebarNavTranslations['poweredBy']?.[language] || sidebarNavTranslations['poweredBy']?.['en'];

  const handleSawadPress = () => {
    Linking.openURL('https://www.sawadeazam.org');
  };

  const handleSignOut = () => {
    signOutUser();
    onClose();
  };

  return (
    <Modal visible={visible} animationType="slide" transparent={false}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.logoContainer}>
            <Image
              source={require('../../../assets/icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <Text style={[
              styles.appTitle,
              (language === 'ur' || language === 'ar') && styles.arabicText
            ]}>
              {currentAppTitle}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#64748b" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {displayedNavItems.map((item) => {
            const label = sidebarNavTranslations[item.id]?.[language] || sidebarNavTranslations[item.id]?.['en'] || item.id;
            return (
              <View key={item.id}>
                <TouchableOpacity
                  style={styles.navItem}
                  onPress={() => {
                    // Handle navigation here
                    onClose();
                  }}
                >
                <Ionicons
                  name={item.icon as any}
                  size={20}
                  color="#14b8a6"
                  style={styles.navIcon}
                />
                <Text style={[
                  styles.navText,
                  (language === 'ur' || language === 'ar') && styles.arabicText
                ]}>
                  {label}
                </Text>
              </TouchableOpacity>
              </View>
            );
          })}

          {!loading && user && (
            <TouchableOpacity style={styles.navItem} onPress={handleSignOut}>
              <Ionicons
                name="log-out-outline"
                size={20}
                color="#ef4444"
                style={styles.navIcon}
              />
              <Text style={[
                styles.navText,
                styles.signOutText,
                (language === 'ur' || language === 'ar') && styles.arabicText
              ]}>
                {sidebarNavTranslations['signOut']?.[language] || "Sign Out"}
              </Text>
            </TouchableOpacity>
          )}

          {!loading && !user && (
            <TouchableOpacity style={styles.navItem} onPress={onClose}>
              <Ionicons
                name="log-in-outline"
                size={20}
                color="#14b8a6"
                style={styles.navIcon}
              />
              <Text style={[
                styles.navText,
                (language === 'ur' || language === 'ar') && styles.arabicText
              ]}>
                {sidebarNavTranslations['signIn']?.[language] || "Sign In"}
              </Text>
            </TouchableOpacity>
          )}
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.sawadContainer} onPress={handleSawadPress}>
            <Text style={[
              styles.poweredByText,
              (language === 'ur' || language === 'ar') && styles.arabicText
            ]}>
              {currentPoweredByText}
            </Text>
            <Text style={styles.websiteText}>www.sawadeazam.org</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#14b8a6',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  logo: {
    width: 32,
    height: 32,
  },
  appTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  arabicText: {
    fontFamily: 'System', // You can add custom Arabic font here
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 4,
    borderRadius: 8,
    backgroundColor: '#f8fafc',
  },
  navIcon: {
    marginRight: 12,
  },
  navText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0f172a',
  },
  signOutText: {
    color: '#ef4444',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    backgroundColor: '#f8fafc',
  },
  sawadContainer: {
    alignItems: 'center',
  },
  poweredByText: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 4,
  },
  websiteText: {
    fontSize: 12,
    color: '#14b8a6',
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
});