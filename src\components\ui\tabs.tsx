import * as React from "react"
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TouchableOpacityProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  muted: '#f1f5f9',
  mutedForeground: '#64748b',
  background: '#ffffff',
  foreground: '#0f172a',
  primary: '#14b8a6',
}

export interface TabsProps extends ViewProps {
  value?: string
  onValueChange?: (value: string) => void
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TabsListProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

export interface TabsTriggerProps extends TouchableOpacityProps {
  value: string
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

export interface TabsContentProps extends ViewProps {
  value: string
  children?: React.ReactNode
  style?: ViewStyle
}

const TabsContext = React.createContext<{
  value?: string
  onValueChange?: (value: string) => void
}>({})

const Tabs = React.forwardRef<View, TabsProps>(
  ({ value, onValueChange, children, style, ...props }, ref) => {
    return (
      <TabsContext.Provider value={{ value, onValueChange }}>
        <View ref={ref} style={[style]} {...props}>
          {children}
        </View>
      </TabsContext.Provider>
    )
  }
)

const TabsList = React.forwardRef<View, TabsListProps>(
  ({ children, style, ...props }, ref) => (
    <View
      ref={ref}
      style={[styles.tabsList, style]}
      {...props}
    >
      {children}
    </View>
  )
)

const TabsTrigger = React.forwardRef<TouchableOpacity, TabsTriggerProps>(
  ({ value, children, style, textStyle, ...props }, ref) => {
    const { value: selectedValue, onValueChange } = React.useContext(TabsContext)
    const isActive = selectedValue === value

    const handlePress = () => {
      onValueChange?.(value)
    }

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.tabsTrigger,
          isActive && styles.tabsTriggerActive,
          style,
        ]}
        onPress={handlePress}
        activeOpacity={0.7}
        {...props}
      >
        <Text
          style={[
            styles.tabsTriggerText,
            isActive && styles.tabsTriggerTextActive,
            textStyle,
          ]}
        >
          {children}
        </Text>
      </TouchableOpacity>
    )
  }
)

const TabsContent = React.forwardRef<View, TabsContentProps>(
  ({ value, children, style, ...props }, ref) => {
    const { value: selectedValue } = React.useContext(TabsContext)

    if (selectedValue !== value) {
      return null
    }

    return (
      <View
        ref={ref}
        style={[styles.tabsContent, style]}
        {...props}
      >
        {children}
      </View>
    )
  }
)

Tabs.displayName = "Tabs"
TabsList.displayName = "TabsList"
TabsTrigger.displayName = "TabsTrigger"
TabsContent.displayName = "TabsContent"

const styles = StyleSheet.create({
  tabsList: {
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    backgroundColor: colors.muted,
    padding: 4,
  },
  tabsTrigger: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  tabsTriggerActive: {
    backgroundColor: colors.background,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabsTriggerText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.mutedForeground,
  },
  tabsTriggerTextActive: {
    color: colors.foreground,
  },
  tabsContent: {
    marginTop: 8,
  },
})

export { Tabs, TabsList, TabsTrigger, TabsContent }
