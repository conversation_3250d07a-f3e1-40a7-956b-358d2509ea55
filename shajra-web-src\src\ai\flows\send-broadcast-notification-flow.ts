
'use server';
/**
 * @fileOverview Genkit flow to send FCM notifications for admin broadcasts.
 *
 * - sendBroadcastNotification - Sends FCM notifications to users for a new broadcast.
 * - SendBroadcastNotificationInput - Input type for the flow.
 * - SendBroadcastNotificationOutput - Output type for the flow.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';
import admin from '@/lib/firebaseAdmin'; // Import initialized Firebase Admin SDK
import { type Message } from 'firebase-admin/messaging';

const SendBroadcastNotificationInputSchema = z.object({
  messageText: z.string().min(1, { message: "Message text cannot be empty." }).describe('The content of the broadcast message.'),
  title: z.string().optional().describe('Optional title for the notification.'),
});
export type SendBroadcastNotificationInput = z.infer<typeof SendBroadcastNotificationInputSchema>;

const SendBroadcastNotificationOutputSchema = z.object({
  successCount: z.number().describe('Number of messages successfully sent.'),
  failureCount: z.number().describe('Number of messages that failed to send.'),
  details: z.string().optional().describe('Details of the operation.'),
});
export type SendBroadcastNotificationOutput = z.infer<typeof SendBroadcastNotificationOutputSchema>;

// Placeholder function to fetch FCM tokens
// In a real app, this would query Firestore for user FCM tokens
async function getAllFcmTokens(): Promise<string[]> {
  console.log("Fetching FCM tokens (placeholder)...");
  // Example: Query a 'fcm_tokens' collection where each document has a 'token' field
  // const tokensSnapshot = await admin.firestore().collection('fcm_tokens').where('active', '==', true).get();
  // const tokens = tokensSnapshot.docs.map(doc => doc.data().token);
  // return tokens.filter(token => token); // Filter out any undefined/null tokens

  // For demonstration, returning a dummy token. Replace with actual token retrieval.
  // Ensure these tokens are valid for testing.
  return [
    // "YOUR_DEVICE_FCM_TOKEN_1_FOR_TESTING",
    // "YOUR_DEVICE_FCM_TOKEN_2_FOR_TESTING"
  ];
}

export async function sendBroadcastNotification(input: SendBroadcastNotificationInput): Promise<SendBroadcastNotificationOutput> {
  return sendBroadcastNotificationFlow(input);
}

const sendBroadcastNotificationFlow = ai.defineFlow(
  {
    name: 'sendBroadcastNotificationFlow',
    inputSchema: SendBroadcastNotificationInputSchema,
    outputSchema: SendBroadcastNotificationOutputSchema,
  },
  async (input) => {
    const { messageText, title } = input;

    const tokens = await getAllFcmTokens();

    if (tokens.length === 0) {
      console.log("No FCM tokens found to send notifications.");
      return { successCount: 0, failureCount: 0, details: "No FCM tokens found." };
    }

    const notificationPayload: Message = {
      notification: {
        title: title || 'New Announcement',
        body: messageText,
      },
      // Common fields for Android and APNS (iOS) for sound
      android: {
        notification: {
          sound: 'default', // Default notification sound
        },
      },
      apns: {
        payload: {
          aps: {
            sound: 'default', // Default notification sound
          },
        },
      },
      // You can add custom data to the payload if needed
      // data: {
      //   navigateTo: '/announcements', // Example custom data
      // },
      tokens: tokens, // Send to multiple devices
    };

    try {
      console.log(`Attempting to send notifications to ${tokens.length} tokens.`);
      const response = await admin.messaging().sendEachForMulticast({ tokens, ...notificationPayload });
      // const response = await admin.messaging().sendMulticast(notificationPayload); // Alternative way

      const successCount = response.successCount;
      const failureCount = response.failureCount;

      console.log(`Successfully sent ${successCount} messages.`);
      if (failureCount > 0) {
        console.error(`Failed to send ${failureCount} messages.`);
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            console.error(`Token[${idx}]: ${tokens[idx]}, Error: ${resp.error?.message}`);
          }
        });
      }
      return { successCount, failureCount, details: `Sent: ${successCount}, Failed: ${failureCount}` };

    } catch (error) {
      console.error('Error sending FCM message via Genkit flow:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error sending FCM.';
      // It's better to throw or return a structured error for the caller to handle
      // For now, just returning failure counts
      return { successCount: 0, failureCount: tokens.length, details: `Error: ${errorMessage}` };
    }
  }
);
