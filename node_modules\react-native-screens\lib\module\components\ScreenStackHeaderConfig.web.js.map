{"version": 3, "names": ["Image", "View", "React", "ScreenStackHeaderBackButtonImage", "props", "createElement", "_extends", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.web.tsx"], "mappings": ";AAAA,SAASA,KAAK,EAAcC,IAAI,QAAmB,cAAc;AACjE,OAAOC,KAAK,MAAM,OAAO;AAOzB,OAAO,MAAMC,gCAAgC,GAC3CC,KAAiB,iBAEjBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,qBACHC,KAAA,CAAAG,aAAA,CAACL,KAAK,EAAAM,QAAA;EAACC,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKJ,KAAK,CAAG,CACpD,CACP;AAED,OAAO,MAAMK,0BAA0B,GACrCL,KAAyC,iBACzBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMM,yBAAyB,GACpCN,KAAyC,iBACzBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMO,2BAA2B,GACtCP,KAAyC,iBACzBF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMQ,8BAA8B,GACzCR,KAA2D,iBAC3CF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMS,uBAAuB,GAClCT,KAA4D,iBAC5CF,KAAA,CAAAG,aAAA,CAACJ,IAAI,EAAKG,KAAQ,CAAC;AAErC,OAAO,MAAMU,wBAEZ,GAAGb,IAAI"}