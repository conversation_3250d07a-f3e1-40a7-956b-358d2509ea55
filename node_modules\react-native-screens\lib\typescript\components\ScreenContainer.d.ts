import React from 'react';
import { ScreenContainerProps } from 'react-native-screens';
export declare const NativeScreenContainer: React.ComponentType<ScreenContainerProps>;
export declare const NativeScreenNavigationContainer: React.ComponentType<ScreenContainerProps>;
declare function ScreenContainer(props: ScreenContainerProps): React.JSX.Element;
export default ScreenContainer;
//# sourceMappingURL=ScreenContainer.d.ts.map