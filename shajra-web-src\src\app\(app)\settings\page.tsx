
"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ExternalLink, Info, Bell, Palette, Moon, Sun, Settings as SettingsIcon, FontIcon } from 'lucide-react';
import { useTheme } from 'next-themes';
import PageTitle from '@/components/shared/PageTitle';
import { ThemeSelector, SABIRIYA_BASE_COLOR_THEME_KEY, DEFAULT_BASE_COLOR, baseColorThemes } from '@/components/shared/ThemeSelector';
import { LanguageSelector } from '@/components/shared/LanguageSelector'; // Corrected import path
import { useLanguage, type Language } from '@/context/LanguageContext';
import { useFontSize, type FontSize } from '@/context/FontSizeContext';
import { cn } from '@/lib/utils';

type SettingsPageContent = {
  pageTitle: string;
  pageSubtitle: string;
  appearanceTitle: string;
  darkModeLabel: string;
  lightModeLabel: string;
  fontSizeLabel: string;
  fontSizeSmall: string;
  fontSizeMedium: string;
  fontSizeLarge: string;
  contentTitle: string;
  languageLabel: string;
  languageGlobalNote: string;
  notificationsTitle: string;
  notificationsLabel: string;
  notificationsDescription: string;
  notificationsUiOnly: string;
  aboutTitle: string;
  aboutUsLink: string;
  versionText: string;
};

const translations: Record<Language, SettingsPageContent> = {
  en: {
    pageTitle: "Settings",
    pageSubtitle: "Customize your app experience.",
    appearanceTitle: "Appearance",
    darkModeLabel: "Dark Mode",
    lightModeLabel: "Light Mode",
    fontSizeLabel: "Font Size",
    fontSizeSmall: "Small",
    fontSizeMedium: "Medium (Default)",
    fontSizeLarge: "Large",
    contentTitle: "Content",
    languageLabel: "Language",
    languageGlobalNote: "Language is controlled globally from the header.",
    notificationsTitle: "Notifications",
    notificationsLabel: "Enable Notifications",
    notificationsDescription: "Manage your app notification preferences.",
    notificationsUiOnly: "(This is a UI preference only and does not enable actual push notifications yet.)",
    aboutTitle: "About",
    aboutUsLink: "About Us (sawadeazam.org)",
    versionText: "Version 1.0.0 (Prototype)"
  },
  ur: {
    pageTitle: "سیٹنگز",
    pageSubtitle: "اپنی ایپ کے تجربے کو حسب ضرورت بنائیں۔",
    appearanceTitle: "ظاہری شکل",
    darkModeLabel: "ڈارک موڈ",
    lightModeLabel: "لائٹ موڈ",
    fontSizeLabel: "فونٹ سائز",
    fontSizeSmall: "چھوٹا",
    fontSizeMedium: "درمیانہ (پہلے سے طے شدہ)",
    fontSizeLarge: "بڑا",
    contentTitle: "مواد",
    languageLabel: "زبان",
    languageGlobalNote: "زبان کو ہیڈر سے عالمی سطح پر کنٹرول کیا جاتا ہے۔",
    notificationsTitle: "اطلاعات",
    notificationsLabel: "اطلاعات فعال کریں",
    notificationsDescription: "اپنی ایپ کی اطلاعاتی ترجیحات کا نظم کریں۔",
    notificationsUiOnly: "(یہ صرف UI کی ترجیح ہے اور ابھی تک اصل پش اطلاعات کو فعال نہیں کرتی ہے۔)",
    aboutTitle: "ہمارے بارے میں",
    aboutUsLink: "ہمارے بارے میں (sawadeazam.org)",
    versionText: "ورژن 1.0.0 (پروٹوٹائپ)"
  },
  ro: {
    pageTitle: "Settings",
    pageSubtitle: "Apni app ke tajrube ko hasb-e-zaroorat banayein.",
    appearanceTitle: "Zahiri Shakal",
    darkModeLabel: "Dark Mode",
    lightModeLabel: "Light Mode",
    fontSizeLabel: "Font Size",
    fontSizeSmall: "Chhota",
    fontSizeMedium: "Darmiyana (Pehle se Tay Shuda)",
    fontSizeLarge: "Bada",
    contentTitle: "Mawad",
    languageLabel: "Zabaan",
    languageGlobalNote: "Zabaan ko header se aalmi satah par control kiya jaata hai.",
    notificationsTitle: "Itla'aat",
    notificationsLabel: "Itla'aat Fa'aal Karein",
    notificationsDescription: "Apni app ki itla'aati tarjeehaat ka nazm karein.",
    notificationsUiOnly: "(Yeh sirf UI ki tarjeeh hai aur abhi tak asal push itla'aat ko fa'aal nahin karti hai.)",
    aboutTitle: "Hamare Bare Mein",
    aboutUsLink: "Hamare Bare Mein (sawadeazam.org)",
    versionText: "Version 1.0.0 (Prototype)"
  },
  hi: {
    pageTitle: "सेटिंग्स",
    pageSubtitle: "अपने ऐप के अनुभव को अनुकूलित करें।",
    appearanceTitle: "दिखावट",
    darkModeLabel: "डार्क मोड",
    lightModeLabel: "लाइट मोड",
    fontSizeLabel: "फ़ॉन्ट साइज़",
    fontSizeSmall: "छोटा",
    fontSizeMedium: "मध्यम (डिफ़ॉल्ट)",
    fontSizeLarge: "बड़ा",
    contentTitle: "सामग्री",
    languageLabel: "भाषा",
    languageGlobalNote: "भाषा को हेडर से विश्व स्तर पर नियंत्रित किया जाता है।",
    notificationsTitle: "सूचनाएं",
    notificationsLabel: "सूचनाएं सक्षम करें",
    notificationsDescription: "अपने ऐप की सूचना प्राथमिकताओं को प्रबंधित करें।",
    notificationsUiOnly: "(यह केवल यूआई प्राथमिकता है और अभी तक वास्तविक पुश सूचनाओं को सक्षम नहीं करती है।)",
    aboutTitle: "हमारे बारे में",
    aboutUsLink: "हमारे बारे में (sawadeazam.org)",
    versionText: "संस्करण 1.0.0 (प्रोटोटाइप)"
  },
  ar: {
    pageTitle: "الإعدادات",
    pageSubtitle: "قم بتخصيص تجربة تطبيقك.",
    appearanceTitle: "المظهر",
    darkModeLabel: "الوضع الداكن",
    lightModeLabel: "الوضع الفاتح",
    fontSizeLabel: "حجم الخط",
    fontSizeSmall: "صغير",
    fontSizeMedium: "متوسط (افتراضي)",
    fontSizeLarge: "كبير",
    contentTitle: "المحتوى",
    languageLabel: "اللغة",
    languageGlobalNote: "يتم التحكم في اللغة عالميًا من الرأس.",
    notificationsTitle: "الإشعارات",
    notificationsLabel: "تمكين الإشعارات",
    notificationsDescription: "إدارة تفضيلات إشعارات التطبيق الخاص بك.",
    notificationsUiOnly: "(هذا تفضيل لواجهة المستخدم فقط ولا يمكّن إشعارات الدفع الفعلية حتى الآن.)",
    aboutTitle: "حول",
    aboutUsLink: "معلومات عنا (sawadeazam.org)",
    versionText: "الإصدار 1.0.0 (نموذج أولي)"
  }
};


export default function SettingsPage() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const { language } = useLanguage();
  const { fontSize, setFontSize } = useFontSize();
  const [notificationsEnabled, setNotificationsEnabled] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('sabiriya-notifications-enabled') === 'true';
    }
    return true; // Default to true if localStorage is not available
  });

  const content = translations[language] || translations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';


  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sabiriya-notifications-enabled', String(notificationsEnabled));
    }
  }, [notificationsEnabled]);
  
  const toggleLightDark = () => {
    if (!mounted) return;

    let currentBaseColor = DEFAULT_BASE_COLOR;
    const currentFullTheme = theme; 

    if (currentFullTheme && currentFullTheme.includes('-')) {
        const parts = currentFullTheme.split('-');
         if (parts.length === 2 && baseColorThemes.some(b => b.value === parts[1])) {
            currentBaseColor = parts[1];
        }
    } else {
        currentBaseColor = localStorage.getItem(SABIRIYA_BASE_COLOR_THEME_KEY) || DEFAULT_BASE_COLOR;
    }
     if (!baseColorThemes.some(t => t.value === currentBaseColor)) {
        currentBaseColor = DEFAULT_BASE_COLOR;
    }


    if (resolvedTheme?.startsWith("dark")) {
      setTheme(`light-${currentBaseColor}`);
    } else {
      setTheme(`dark-${currentBaseColor}`);
    }
  };

  if (!mounted) {
    return null; 
  }

  const isDarkMode = resolvedTheme?.startsWith("dark");

  return (
    <div className={cn("space-y-8 max-w-2xl mx-auto", fontClass)} lang={language} dir={isRtl ? 'rtl' : 'ltr'}>
      <PageTitle title={content.pageTitle} subtitle={content.pageSubtitle} className={cn("text-center sm:text-left", isRtl && "sm:text-right")} />

      <Card className="shadow-lg">
        <CardHeader className={cn(isRtl && "text-right")}>
          <h2 className={cn("text-xl font-semibold flex items-center", isRtl && "flex-row-reverse justify-end")}><Palette className={cn("h-5 w-5 text-primary", isRtl ? "ml-2" : "mr-2")} /> {content.appearanceTitle}</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="dark-mode-toggle" className="text-md">
              {isDarkMode ? content.darkModeLabel : content.lightModeLabel}
            </Label>
            <div className="flex items-center gap-2">
              <ThemeSelector /> 
              <Button 
                onClick={toggleLightDark} 
                variant="outline" 
                size="icon" 
                id="dark-mode-toggle" 
                aria-label="Toggle light/dark mode"
                className="hover:shadow-[0_0_15px_hsl(var(--primary))]"
              >
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="font-size" className="text-md">{content.fontSizeLabel}</Label>
            <Select value={fontSize} onValueChange={(value) => setFontSize(value as FontSize)}>
              <SelectTrigger id="font-size" className="w-full">
                <SelectValue placeholder="Select font size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sm">{content.fontSizeSmall}</SelectItem>
                <SelectItem value="base">{content.fontSizeMedium}</SelectItem>
                <SelectItem value="lg">{content.fontSizeLarge}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
      
      <Card className="shadow-lg">
        <CardHeader className={cn(isRtl && "text-right")}>
           <h2 className={cn("text-xl font-semibold flex items-center", isRtl && "flex-row-reverse justify-end")}><SettingsIcon className={cn("h-5 w-5 text-primary", isRtl ? "ml-2" : "mr-2")} /> {content.contentTitle}</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="language-selector" className="text-md">{content.languageLabel}</Label>
            <LanguageSelector buttonClassName="w-full justify-start"/>
            <p className="text-xs text-muted-foreground">{content.languageGlobalNote}</p>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg">
        <CardHeader className={cn(isRtl && "text-right")}>
          <h2 className={cn("text-xl font-semibold flex items-center", isRtl && "flex-row-reverse justify-end")}><Bell className={cn("h-5 w-5 text-primary", isRtl ? "ml-2" : "mr-2")} /> {content.notificationsTitle}</h2>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <Label htmlFor="notifications" className="text-md">{content.notificationsLabel}</Label>
            <Switch
              id="notifications"
              checked={notificationsEnabled}
              onCheckedChange={setNotificationsEnabled}
              aria-label="Toggle notifications"
            />
          </div>
           <p className="text-xs text-muted-foreground mt-1">{content.notificationsDescription}</p>
           <p className="text-xs text-destructive mt-1">{content.notificationsUiOnly}</p>
        </CardContent>
      </Card>

      <Card className="shadow-lg">
        <CardHeader className={cn(isRtl && "text-right")}>
          <h2 className={cn("text-xl font-semibold flex items-center", isRtl && "flex-row-reverse justify-end")}><Info className={cn("h-5 w-5 text-primary", isRtl ? "ml-2" : "mr-2")} /> {content.aboutTitle}</h2>
        </CardHeader>
        <CardContent>
          <a href="https://www.sawadeazam.org" target="_blank" rel="noopener noreferrer" className="w-full">
            <Button variant="link" className={cn("w-full text-primary", fontClass)}>
              {content.aboutUsLink} <ExternalLink className={cn("h-4 w-4", isRtl ? "mr-2" : "ml-2")} />
            </Button>
          </a>
          <p className="text-xs text-muted-foreground mt-2 text-center">
            {content.versionText}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
