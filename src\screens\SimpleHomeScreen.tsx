import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useLanguage } from '../context/LanguageContext';
import { useFontSize } from '../context/FontSizeContext';
import { useAuth } from '../context/AuthContext';

export default function SimpleHomeScreen() {
  const { language, setLanguage } = useLanguage();
  const { fontSize, setFontSize } = useFontSize();
  const { user, loading } = useAuth();

  const handleLanguageToggle = () => {
    const languages = ['en', 'ur', 'ar', 'hi', 'ro'];
    const currentIndex = languages.indexOf(language);
    const nextIndex = (currentIndex + 1) % languages.length;
    setLanguage(languages[nextIndex] as any);
  };

  const handleFontSizeToggle = () => {
    const sizes = ['sm', 'base', 'lg'];
    const currentIndex = sizes.indexOf(fontSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    setFontSize(sizes[nextIndex] as any);
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { fontSize: fontSize === 'sm' ? 20 : fontSize === 'lg' ? 28 : 24 }]}>
        Sabiriya: Spiritual Guide
      </Text>
      
      <Text style={styles.subtitle}>
        Context Providers Working! ✅
      </Text>

      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>Language: {language}</Text>
        <Text style={styles.infoText}>Font Size: {fontSize}</Text>
        <Text style={styles.infoText}>Auth Loading: {loading ? 'Yes' : 'No'}</Text>
        <Text style={styles.infoText}>User: {user ? 'Logged In' : 'Not Logged In'}</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={handleLanguageToggle}>
          <Text style={styles.buttonText}>Toggle Language</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleFontSizeToggle}>
          <Text style={styles.buttonText}>Toggle Font Size</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#14b8a6',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 30,
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    borderRadius: 10,
    marginBottom: 30,
    width: '100%',
  },
  infoText: {
    color: 'white',
    fontSize: 14,
    marginBottom: 5,
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
  },
  button: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});
