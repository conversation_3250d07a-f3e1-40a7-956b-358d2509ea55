import { NextResponse } from 'next/server';
import { explainSufiTerm, type ExplainSufiTermInput } from '@/ai/flows/explain-sufi-term-flow';
import { z } from 'zod';

const ExplainSufiTermRequestSchema = z.object({
  term: z.string().min(1, { message: "Term cannot be empty." }),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validationResult = ExplainSufiTermRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid input.', details: validationResult.error.flatten() }, { status: 400 });
    }

    const inputData: ExplainSufiTermInput = { term: validationResult.data.term };
    const result = await explainSufiTerm(inputData);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error explaining Sufi term:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: 'Failed to explain Sufi term.', details: errorMessage }, { status: 500 });
  }
}
