
"use client"; // Mark as client component for auth and state

import { duasData, type Dua } from '@/data/duas';
import { Card, CardContent, CardDescription, CardHeader, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, PlayCircle, Info, BookOpen, Heart } from 'lucide-react';
import DigitalTasbeeh from '@/components/shared/DigitalTasbeeh';
import { Separator } from '@/components/ui/separator';
// import { notFound } from 'next/navigation'; // notFound can only be used in Server Components
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext'; // For potential favorite functionality
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import PageTitle from '@/components/shared/PageTitle';
import { motion } from 'framer-motion';


// Simulate fetching a single dua by slug
const getDuaBySlug = async (slug: string): Promise<Dua | null> => {
  // await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay
  const dua = duasData.find(d => d.slug === slug);
  return dua || null;
};

export default function DuaDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { duaSlug } = params;
  const [dua, setDua] = useState<Dua | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false); // Placeholder for favorite state

  const { user } = useAuth(); // Get user for favorite functionality

  useEffect(() => {
    if (typeof duaSlug === 'string') {
      const fetchDua = async () => {
        setLoading(true);
        const fetchedDua = await getDuaBySlug(duaSlug);
        if (!fetchedDua) {
          // Handle not found, e.g. redirect or show error
          // For now, we'll just log it, in a real app use router.replace('/404') or similar
          console.error("Dua not found");
          router.replace('/duas'); // Or a proper 404 page
        }
        setDua(fetchedDua);
        setLoading(false);
        // Here you would also fetch favorite status if user is logged in
        // e.g., checkFirestoreFavorite(user?.uid, fetchedDua?.id).then(setIsFavorite);
      };
      fetchDua();
    } else {
      setLoading(false);
      router.replace('/duas'); // Invalid slug
    }
  }, [duaSlug, router, user]);

  if (loading) {
    return <div className="flex justify-center items-center min-h-[200px]"><LoadingSpinner size={32}/></div>;
  }

  if (!dua) {
    // This case should be handled by the redirect in useEffect, but as a fallback:
    return <div className="text-center py-10">Dua not found. <Link href="/duas" className="text-primary hover:underline">Return to Duas</Link></div>;
  }

  // Mapping to requested field names
  const dua_title_urdu = dua.title;
  const dua_text_arabic = dua.arabicText;
  const dua_translation_urdu = dua.urduTranslation;
  const recitation_instructions_urdu = dua.instructions;
  const benefits_urdu = dua.benefits;

  const toggleFavorite = async () => {
    if (!user) {
      router.push('/login?redirect=/duas/' + duaSlug); // Redirect to login if not authenticated
      return;
    }
    // Placeholder for Firestore logic
    console.log("Toggling favorite for user:", user.uid, "dua:", dua.id);
    setIsFavorite(!isFavorite);
    // await updateFavoriteInFirestore(user.uid, dua.id, !isFavorite);
  };


  return (
    <div className="space-y-6">
       <div className="flex justify-between items-start mb-4">
          <div>
            <Link href="/duas" className="text-sm text-primary hover:underline mb-2 inline-flex items-center">
            <ArrowRight className="h-4 w-4 mr-1 transform rotate-180" /> Back to Duas
            </Link>
            <PageTitle title={dua_title_urdu} className="mb-0" />
            {dua.recitationTime && (
                <CardDescription className="text-md text-muted-foreground pt-1">{/* Using CardDescription for styling, semantically part of title context */}
                    Recitation Time: {dua.recitationTime}
                </CardDescription>
            )}
        </div>
        {user && (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleFavorite} 
            aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"} 
            className="mt-2 hover:shadow-[0_0_15px_hsl(var(--primary))]"
          >
            <motion.div
              initial={false}
              animate={{ scale: isFavorite ? 1.2 : 1 }}
              transition={{ type: "spring", stiffness: 300, damping: 10 }}
            >
              <Heart 
                className={`h-6 w-6 transition-colors duration-300 ${isFavorite ? "fill-red-500 text-red-500" : "text-muted-foreground"}`} 
              />
            </motion.div>
          </Button>
        )}
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <h2 id="arabic-text-heading" className="text-xl font-semibold mb-2 text-foreground flex items-center">
            <BookOpen className="h-5 w-5 mr-2 text-primary" /> Arabic Text (dua_text_arabic)
          </h2>
        </CardHeader>
        <CardContent className="p-6 space-y-6 pt-0">
          <section aria-labelledby="arabic-text-heading">
            <p lang="ar" dir="rtl" className="font-arabic text-2xl leading-relaxed text-foreground text-right bg-muted/30 p-4 rounded-md">
              {dua_text_arabic}
            </p>
          </section>

          {dua.transliteration && (
             <section aria-labelledby="transliteration-heading">
                <h2 id="transliteration-heading" className="text-xl font-semibold mb-2 text-foreground">Transliteration</h2>
                <p className="text-lg text-muted-foreground italic p-4 border-l-4 border-primary bg-muted/30 rounded-md">{dua.transliteration}</p>
            </section>
          )}

          <section aria-labelledby="urdu-translation-heading">
            <h2 id="urdu-translation-heading" className="text-xl font-semibold mb-2 text-foreground">Urdu Translation (dua_translation_urdu)</h2>
            <p lang="ur" dir="rtl" className="font-arabic text-xl leading-relaxed text-foreground text-right p-4 border-l-4 border-accent bg-muted/30 rounded-md">
              {dua_translation_urdu}
            </p>
          </section>
          
          <Separator />

          <section aria-labelledby="instructions-heading">
            <h2 id="instructions-heading" className="text-xl font-semibold mb-2 text-foreground flex items-center">
              <Info className="h-5 w-5 mr-2 text-primary" /> Instructions (recitation_instructions_urdu)
            </h2>
            <p className="text-foreground">{recitation_instructions_urdu}</p>
          </section>

          <section aria-labelledby="benefits-heading">
            <h2 id="benefits-heading" className="text-xl font-semibold mb-2 text-foreground">Benefits (benefits_urdu)</h2>
            <p className="text-foreground">{benefits_urdu}</p>
          </section>

          {dua.audioUrl && ( 
            <Button variant="outline" className="w-full sm:w-auto" disabled>
              <PlayCircle className="h-5 w-5 mr-2" /> Play Audio (Placeholder)
            </Button>
          )}
        </CardContent>
        {dua.hasTasbeeh && (
          <>
            <Separator />
            <CardFooter className="p-6 flex flex-col items-center">
              <h2 className="text-xl font-semibold mb-4 text-foreground">Digital Tasbeeh</h2>
              <DigitalTasbeeh />
            </CardFooter>
          </>
        )}
      </Card>
    </div>
  );
}
