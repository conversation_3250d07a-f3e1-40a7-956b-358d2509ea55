
"use client";

import { useState } from 'react';
import Link from 'next/link';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/AuthContext';
import AuthFormCard from '@/components/auth/AuthFormCard';
import { Loader2, CheckCircle } from 'lucide-react';
import ErrorMessage from '@/components/shared/ErrorMessage';
import type { AuthError } from 'firebase/auth';

const PasswordResetSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

type PasswordResetFormData = z.infer<typeof PasswordResetSchema>;

export default function PasswordResetPage() {
  const { sendPasswordReset, loading: authLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm<PasswordResetFormData>({
    resolver: zodResolver(PasswordResetSchema),
  });

  const handlePasswordReset: SubmitHandler<PasswordResetFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);
    setEmailSent(false);
    const result = await sendPasswordReset(data.email);
    if (result && 'code' in result) {
      setError((result as AuthError).message || 'Failed to send password reset email.');
    } else {
      setEmailSent(true);
    }
    setIsSubmitting(false);
  };

  const isLoading = authLoading || isSubmitting;

  if (emailSent) {
    return (
      <AuthFormCard title="Check Your Email">
        <div className="text-center space-y-4">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
          <p className="text-foreground">
            A password reset link has been sent to your email address. Please check your inbox (and spam folder).
          </p>
          <Button asChild variant="link">
            <Link href="/login">Back to Login</Link>
          </Button>
        </div>
      </AuthFormCard>
    );
  }

  return (
    <AuthFormCard
      title="Reset Password"
      description="Enter your email to receive a password reset link."
      footerContent={
        <p>
          Remembered your password?{' '}
          <Link href="/login" className="font-medium text-primary hover:underline">
            Login
          </Link>
        </p>
      }
    >
      <form onSubmit={handleSubmit(handlePasswordReset)} className="space-y-4">
        {error && <ErrorMessage message={error} />}
        <div className="space-y-1">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" {...register('email')} placeholder="<EMAIL>" />
          {errors.email && <p className="text-xs text-destructive">{errors.email.message}</p>}
        </div>
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Send Reset Link
        </Button>
      </form>
    </AuthFormCard>
  );
}
