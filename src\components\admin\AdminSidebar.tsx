
"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import { SheetTitle } from '@/components/ui/sheet';
import Image from 'next/image';
import { LayoutDashboard, ListOrdered, BookText, MessageSquare, Moon, Sun, LogOut } from 'lucide-react';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { LanguageSelector } from '@/components/shared/LanguageSelector';
import { cn } from '@/lib/utils';
import { useAuth } from '@/context/AuthContext';
import { useEffect, useState } from 'react';
import { SABIRIYA_BASE_COLOR_THEME_KEY, DEFAULT_BASE_COLOR, baseColorThemes } from '@/components/shared/ThemeSelector';


const navItems = [
  { href: '/admin/dashboard', label: 'Dashboard', icon: LayoutDashboard },
  { href: '/admin/manage-duas', label: 'Manage Duas', icon: ListOrdered },
  { href: '/admin/user-chats', label: 'User Chats', icon: MessageSquare },
  { href: '/admin/manage-teachings', label: 'Manage Teachings', icon: BookText, disabled: true },
];


export function AdminSidebar() {
  const pathname = usePathname();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { isMobile, openMobile: openMobileSidebar, setOpenMobile: setOpenMobileSidebar, state: sidebarState } = useSidebar();
  const { user, signOutUser } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleLightDark = () => {
    if (!mounted) return;

    let currentBaseColor = DEFAULT_BASE_COLOR;
    const currentFullTheme = theme;

    if (currentFullTheme && currentFullTheme.includes('-')) {
        const parts = currentFullTheme.split('-');
         if (parts.length === 2 && baseColorThemes.some(b => b.value === parts[1])) {
            currentBaseColor = parts[1];
        }
    } else {
        currentBaseColor = localStorage.getItem(SABIRIYA_BASE_COLOR_THEME_KEY) || DEFAULT_BASE_COLOR;
    }
     if (!baseColorThemes.some(t => t.value === currentBaseColor)) {
        currentBaseColor = DEFAULT_BASE_COLOR;
    }

    if (resolvedTheme?.startsWith("dark")) {
      setTheme(`light-${currentBaseColor}`);
    } else {
      setTheme(`dark-${currentBaseColor}`);
    }
  };

  const handleLinkClick = () => {
    if (isMobile && openMobileSidebar) {
      setOpenMobileSidebar(false);
    }
  };


  if (!mounted) {
    return (
       <Sidebar variant="sidebar" collapsible="icon" className="border-r">
         <SidebarHeader className="p-4 flex items-center gap-2 justify-between">
            <div className="flex items-center gap-2">
                 <Image
                  src="/sawad-e-azam-logo.png" 
                  alt="Khanqahe Sabiriya Admin Logo Placeholder"
                  width={80}
                  height={40}
                  className="rounded-sm object-contain"
                  data-ai-hint="logo sawad-e-azam mosque quran"
                />
                <span className="font-semibold text-lg text-sidebar-foreground group-data-[collapsible=icon]:hidden">Admin Panel</span>
            </div>
         </SidebarHeader>
         <SidebarContent className="p-2 flex flex-col justify-between">
            <div className="animate-pulse">
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
                <div className="h-8 bg-muted rounded mb-2 w-full"></div>
                 <div className="h-8 bg-muted rounded mb-2 w-full"></div>
            </div>
         </SidebarContent>
         <SidebarFooter className="p-4 border-t border-sidebar-border flex flex-col items-center gap-2 group-data-[collapsible=icon]:flex-row group-data-[collapsible=icon]:justify-center">
            <div className="h-10 w-10 bg-muted rounded"></div>
         </SidebarFooter>
       </Sidebar>
    );
  }

  const isDarkMode = resolvedTheme?.startsWith("dark");

  return (
    <Sidebar variant="sidebar" collapsible="icon" className="border-r">
      <SidebarHeader className="p-4 flex items-center gap-2 justify-between">
         <Link href="/admin/dashboard" className="flex items-center gap-2">
             <Image
              src="/sawad-e-azam-logo.png" 
              alt="Khanqahe Sabiriya Admin Logo"
              width={80} 
              height={40} 
              className="rounded-sm object-contain"
              data-ai-hint="logo sawad-e-azam mosque quran"
            />
            {isMobile ? (
              <SheetTitle asChild>
                <span className="font-semibold text-lg text-sidebar-foreground group-data-[collapsible=icon]:hidden">Admin Panel</span>
              </SheetTitle>
            ) : (
              <span className="font-semibold text-lg text-sidebar-foreground group-data-[collapsible=icon]:hidden">Admin Panel</span>
            )}
          </Link>
        <div className="md:hidden">
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent className="p-2 flex flex-col justify-between">
        <SidebarMenu>
          {navItems.map((item) => {
            const isActive = item.href === '/admin/dashboard'
                             ? pathname === item.href
                             : pathname.startsWith(item.href);
            return (
              <SidebarMenuItem key={item.href}>
                <Link href={item.disabled ? "#" : item.href} legacyBehavior passHref>
                  <SidebarMenuButton
                    onClick={handleLinkClick}
                    isActive={isActive && !item.disabled}
                    className={cn(
                      "w-full justify-start relative overflow-hidden",
                      isActive && !item.disabled
                        ? "bg-sidebar-accent text-sidebar-accent-foreground"
                        : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                      item.disabled && "opacity-50 cursor-not-allowed"
                    )}
                    tooltip={{children: item.label + (item.disabled ? " (Coming Soon)" : ""), className: "bg-popover text-popover-foreground"}}
                    disabled={item.disabled}
                  >
                    {isActive && !item.disabled && (
                      <span className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-[hsl(var(--sidebar-primary))] to-[hsl(var(--sidebar-accent))]"></span>
                    )}
                    <item.icon className="h-5 w-5" />
                    <span className="group-data-[collapsible=icon]:hidden ml-1">{item.label}</span>
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>

        <SidebarMenu className="mt-auto">
            {user && (
             <SidebarMenuItem>
                <SidebarMenuButton
                    onClick={() => { signOutUser(); handleLinkClick(); }}
                    className="w-full justify-start hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    tooltip={{children: "Sign Out", className: "bg-popover text-popover-foreground"}}
                >
                    <LogOut className="h-5 w-5" />
                    <span className="group-data-[collapsible=icon]:hidden">Sign Out</span>
                </SidebarMenuButton>
            </SidebarMenuItem>
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter className="p-4 border-t border-sidebar-border flex flex-col items-center gap-2 group-data-[collapsible=icon]:flex-row group-data-[collapsible=icon]:justify-center">
        {/* LanguageSelector removed from AdminSidebar footer as it's now in AppHeader */}
        <Button
            variant="ghost"
            size="icon"
            onClick={toggleLightDark}
            className="w-full justify-center group-data-[collapsible=icon]:w-auto hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_15px_hsl(var(--primary))]"
            aria-label="Toggle theme"
          >
            <Sun className={cn("h-5 w-5", isDarkMode ? "hidden" : "block")} />
            <Moon className={cn("h-5 w-5", isDarkMode ? "block" : "hidden")} />
            <span className="sr-only group-data-[collapsible=icon]:hidden">Toggle Theme</span>
            <span className="group-data-[collapsible=icon]:hidden ml-2">Toggle Theme</span>
          </Button>
      </SidebarFooter>
    </Sidebar>
  );
}
