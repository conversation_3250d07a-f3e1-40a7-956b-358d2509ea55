import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { duasData } from '../../data/duas';
import { useLanguage } from '../../context/LanguageContext';

export default function DuasScreen() {
  const { language } = useLanguage();
  const navigation = useNavigation();

  const renderDuaItem = ({ item }: { item: typeof duasData[0] }) => (
    <TouchableOpacity
      style={styles.duaItem}
      onPress={() => navigation.navigate('DuaDetail' as never, { duaSlug: item.slug } as never)}
    >
      <View style={styles.duaContent}>
        <Text style={styles.duaTitle}>{item.title}</Text>
        <Text style={styles.duaArabic} numberOfLines={2}>
          {item.arabicText}
        </Text>
        <Text style={styles.duaUrdu} numberOfLines={2}>
          {item.urduTranslation}
        </Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color="#64748b" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Duas</Text>
        <Text style={styles.headerSubtitle}>Islamic Prayers & Supplications</Text>
      </View>
      
      <FlatList
        data={duasData}
        renderItem={renderDuaItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#14b8a6',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  listContent: {
    padding: 16,
  },
  duaItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  duaContent: {
    flex: 1,
  },
  duaTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  duaArabic: {
    fontSize: 14,
    color: '#374151',
    textAlign: 'right',
    marginBottom: 4,
    fontFamily: 'System', // You can add Arabic font here
  },
  duaUrdu: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'right',
  },
});
