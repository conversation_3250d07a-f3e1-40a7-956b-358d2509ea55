
// Import the functions you need from the SDKs you need
import { initializeApp, getApp, getApps, type FirebaseApp } from "firebase/app";
import { getFirestore, type Firestore } from "firebase/firestore";
import { getStorage, type FirebaseStorage } from "firebase/storage";
import { getAuth, type Auth } from "firebase/auth";
import { getMessaging, type Messaging } from "firebase/messaging"; // Added for FCM

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  // measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID, // Uncomment if you use Analytics
};

// Initialize Firebase
let app: FirebaseApp | null = null;
let db: Firestore | null = null;
let storage: FirebaseStorage | null = null;
let auth: Auth | null = null;
let messaging: Messaging | null = null;

// Ensure Firebase is initialized only on the client-side and if config is valid
if (typeof window !== 'undefined') {
  if (firebaseConfig.apiKey && firebaseConfig.projectId) {
    if (!getApps().length) {
      try {
        app = initializeApp(firebaseConfig);
      } catch (e) {
        console.error("Firebase app initialization failed:", e);
        // app remains null
      }
    } else {
      app = getApp();
    }

    if (app) {
      try {
        auth = getAuth(app);
        db = getFirestore(app);
        storage = getStorage(app);
        // Only initialize messaging if sender ID is present to avoid errors if not configured
        if (firebaseConfig.messagingSenderId) {
          messaging = getMessaging(app);
        }
      } catch (e) {
        console.error("Error initializing Firebase services (auth, db, storage, messaging):", e);
        // services (auth, db, etc.) might remain null
      }
    } else {
      // This else block might not be reached if initializeApp throws before this point
      // due to invalid config, but it's good for clarity.
      console.error(
        "Firebase app could not be initialized. This usually means the Firebase config is missing or incomplete. " +
        "Please ensure NEXT_PUBLIC_FIREBASE_API_KEY and other NEXT_PUBLIC_FIREBASE_... variables are correctly set in your .env.local file, " +
        "and that you have restarted your development server."
      );
    }
  } else {
    console.error(
      "Firebase Error: Critical Firebase configuration (apiKey or projectId) is missing or undefined. " +
      "Firebase will not be initialized. " +
      "Please ensure all NEXT_PUBLIC_FIREBASE_... variables are set in your .env.local file and you have restarted your development server."
    );
  }
}

export { app, db, storage, auth, messaging };
