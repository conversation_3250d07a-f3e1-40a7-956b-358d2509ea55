import { NextResponse } from 'next/server';
import { answerContentQuestion, type AnswerContentQuestionInput } from '@/ai/flows/answer-content-question-flow';
import { z } from 'zod';

const AnswerContentQuestionRequestSchema = z.object({
  question: z.string().min(1, { message: "Question cannot be empty." }),
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validationResult = AnswerContentQuestionRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid input.', details: validationResult.error.flatten() }, { status: 400 });
    }

    const inputData: AnswerContentQuestionInput = { question: validationResult.data.question };
    const result = await answerContentQuestion(inputData);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error answering content question:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: 'Failed to answer question.', details: errorMessage }, { status: 500 });
  }
}
