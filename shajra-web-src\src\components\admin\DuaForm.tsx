
"use client";

import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import type { Dua } from '@/data/duas'; // Assuming Dua type is exported
import { useEffect } from 'react';

// Helper to generate a simple slug (kebab-case)
const generateSlug = (title: string) => {
  return title
    .toLowerCase()
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/[^\w-]+/g, ''); // Remove all non-word chars
};

// Define the Zod schema for the form
const DuaFormSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters." }).max(100),
  slug: z.string().min(3, { message: "Slug must be at least 3 characters." }),
  arabicText: z.string().min(10, { message: "Arabic text is required." }),
  urduTranslation: z.string().min(10, { message: "Urdu translation is required." }),
  transliteration: z.string().optional(),
  instructions: z.string().min(5, { message: "Instructions are required." }),
  benefits: z.string().min(5, { message: "Benefits are required." }),
  audioUrl: z.string().url({ message: "Please enter a valid URL for audio." }).optional().or(z.literal('')),
  hasTasbeeh: z.boolean().default(false),
  recitationTime: z.string().optional(),
});

export type DuaFormData = z.infer<typeof DuaFormSchema>;

type DuaFormProps = {
  onSubmit: (data: DuaFormData, originalDuaId?: string) => void;
  onCancel: () => void;
  initialData?: Dua | null;
  isEditing?: boolean;
};

export default function DuaForm({ onSubmit, onCancel, initialData, isEditing = false }: DuaFormProps) {
  const form = useForm<DuaFormData>({
    resolver: zodResolver(DuaFormSchema),
    defaultValues: initialData ? {
      ...initialData,
      audioUrl: initialData.audioUrl || "", // Ensure audioUrl is a string
      transliteration: initialData.transliteration || "",
      recitationTime: initialData.recitationTime || "",
    } : {
      title: "",
      slug: "",
      arabicText: "",
      urduTranslation: "",
      transliteration: "",
      instructions: "",
      benefits: "",
      audioUrl: "",
      hasTasbeeh: false,
      recitationTime: "",
    },
  });

  const watchedTitle = form.watch("title");

  useEffect(() => {
    if (watchedTitle && !isEditing && !form.getValues("slug")) {
      form.setValue("slug", generateSlug(watchedTitle), { shouldValidate: true });
    }
  }, [watchedTitle, form, isEditing]);


  const handleSubmitForm: SubmitHandler<DuaFormData> = (data) => {
    onSubmit(data, initialData?.id);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmitForm)} className="space-y-4 max-h-[70vh] overflow-y-auto p-1">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title (Urdu/English)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Dua for Protection" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Slug (auto-generated from title for new duas)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., dua-for-protection" {...field} disabled={isEditing} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="arabicText"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Arabic Text</FormLabel>
              <FormControl>
                <Textarea lang="ar" dir="rtl" placeholder="Enter Arabic text here..." className="font-arabic min-h-[100px]" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="urduTranslation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Urdu Translation</FormLabel>
              <FormControl>
                <Textarea lang="ur" dir="rtl" placeholder="Enter Urdu translation here..." className="font-arabic min-h-[100px]" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="transliteration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Transliteration (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Bismillahillazi la yadurru..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="instructions"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Recitation Instructions</FormLabel>
              <FormControl>
                <Textarea placeholder="e.g., Recite 3 times in the morning..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="benefits"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Benefits</FormLabel>
              <FormControl>
                <Textarea placeholder="e.g., Protection from harm..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="audioUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Audio URL (Optional, e.g., Firebase Storage URL)</FormLabel>
              <FormControl>
                <Input type="url" placeholder="https://firebasestorage.googleapis.com/..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="recitationTime"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Recitation Time (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Morning & Evening, After Fajr" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="hasTasbeeh"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
              <div className="space-y-0.5">
                <FormLabel>Has Tasbeeh Counter?</FormLabel>
                <p className="text-xs text-muted-foreground">
                  Should a digital tasbeeh be shown for this dua?
                </p>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            {isEditing ? 'Save Changes' : 'Create Dua'}
          </Button>
        </div>
      </form>
    </Form>
  );
}

