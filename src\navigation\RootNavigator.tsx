import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../context/AuthContext';

// Import navigators
import AuthNavigator from './AuthNavigator';
import AppNavigator from './AppNavigator';

// Import test screen for development
import SimpleHomeScreen from '../screens/SimpleHomeScreen';

export type RootStackParamList = {
  Auth: undefined;
  App: undefined;
  TestHome: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function RootNavigator() {
  const { user, loading } = useAuth();

  if (loading) {
    return null; // Loading is handled by AuthProvider
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {user ? (
        // User is logged in - show main app
        <Stack.Screen name="App" component={AppNavigator} />
      ) : (
        // User is not logged in - show auth flow
        <>
          <Stack.Screen name="Auth" component={AuthNavigator} />
          <Stack.Screen name="TestHome" component={SimpleHomeScreen} />
        </>
      )}
    </Stack.Navigator>
  );
}
