import PersonalizedPracticeForm from './PersonalizedPracticeForm';
import { <PERSON><PERSON>es<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

export default function RecommendationsPage() {
  return (
    <div className="space-y-6">
      <CardHeader className="px-0">
        <Link href="/practices" className="text-sm text-primary hover:underline mb-2 inline-flex items-center">
          <ArrowRight className="h-4 w-4 mr-1 transform rotate-180" /> Back to Practices
        </Link>
        <CardTitle className="text-3xl font-bold text-foreground">Personalized Practice Recommendations</CardTitle>
        <CardDescription className="text-lg text-muted-foreground">
          Discover spiritual practices tailored to your journey.
        </CardDescription>
      </CardHeader>
      <PersonalizedPracticeForm />
    </div>
  );
}
