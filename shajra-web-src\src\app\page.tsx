
"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
// import MosqueSilhouetteIcon from '@/components/icons/MosqueSilhouetteIcon'; // Removed
import Image from 'next/image'; // Added
import { Loader2 } from 'lucide-react';

export default function SplashScreen() {
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      const languageSelected = localStorage.getItem('sabiriya-app-language');

      if (languageSelected) {
        // If language is set, check if onboarding is complete.
        // const onboardingComplete = localStorage.getItem('sabiriyaOnboardingComplete');
        // if (onboardingComplete === 'true') {
        router.replace('/home');
        // } else {
        //   router.replace('/onboarding/1');
        // }
      } else {
        // If language is not set, always go to language selection first.
        router.replace('/select-language');
      }
    }, 2500); // Show splash for 2.5 seconds

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-background text-foreground p-8">
      <Image
        src="/app-icon.png" // Path to your new image in the public folder
        alt="Sabiriya App Icon"
        width={112} // Adjust as needed (e.g., 112 for w-28)
        height={112} // Adjust as needed (e.g., 112 for h-28)
        className="mb-6"
        priority
        data-ai-hint="app icon dome calligraphy"
      />
      <h1 className="text-3xl sm:text-4xl font-bold mb-2 text-center">Sabiriya</h1>
      <p className="text-lg sm:text-xl text-muted-foreground text-center">Khanqahe Ahle Bayt Silsila Chishtiya Sabiriya</p>
      <Loader2 className="h-8 w-8 animate-spin text-primary mt-8" />
    </div>
  );
}
