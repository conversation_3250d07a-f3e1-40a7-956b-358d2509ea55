import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';

// Import main screens
import HomeScreen from '@/screens/app/HomeScreen';
import DuasScreen from '@/screens/app/DuasScreen';
import TeachingsScreen from '@/screens/app/TeachingsScreen';
import ChatScreen from '@/screens/app/ChatScreen';
import SettingsScreen from '@/screens/app/SettingsScreen';

// Import detail screens
import DuaDetailScreen from '@/screens/app/DuaDetailScreen';
import TeachingDetailScreen from '@/screens/app/TeachingDetailScreen';
import QiblaScreen from '@/screens/app/QiblaScreen';
import TasbeehScreen from '@/screens/app/TasbeehScreen';
import AnnouncementsScreen from '@/screens/app/AnnouncementsScreen';
import ShajraScreen from '@/screens/app/ShajraScreen';
import PracticesScreen from '@/screens/app/PracticesScreen';
import ProfileScreen from '@/screens/app/ProfileScreen';

export type AppTabParamList = {
  Home: undefined;
  Duas: undefined;
  Teachings: undefined;
  Chat: undefined;
  Settings: undefined;
};

export type AppStackParamList = {
  MainTabs: undefined;
  DuaDetail: { duaSlug: string };
  TeachingDetail: { teachingSlug: string };
  Qibla: undefined;
  Tasbeeh: undefined;
  Announcements: undefined;
  Shajra: undefined;
  Practices: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<AppTabParamList>();
const Stack = createNativeStackNavigator<AppStackParamList>();

function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Duas') {
            iconName = focused ? 'book' : 'book-outline';
          } else if (route.name === 'Teachings') {
            iconName = focused ? 'school' : 'school-outline';
          } else if (route.name === 'Chat') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#14b8a6',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Duas" component={DuasScreen} />
      <Tab.Screen name="Teachings" component={TeachingsScreen} />
      <Tab.Screen name="Chat" component={ChatScreen} />
      <Tab.Screen name="Settings" component={SettingsScreen} />
    </Tab.Navigator>
  );
}

export default function AppNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen name="DuaDetail" component={DuaDetailScreen} />
      <Stack.Screen name="TeachingDetail" component={TeachingDetailScreen} />
      <Stack.Screen name="Qibla" component={QiblaScreen} />
      <Stack.Screen name="Tasbeeh" component={TasbeehScreen} />
      <Stack.Screen name="Announcements" component={AnnouncementsScreen} />
      <Stack.Screen name="Shajra" component={ShajraScreen} />
      <Stack.Screen name="Practices" component={PracticesScreen} />
      <Stack.Screen name="Profile" component={ProfileScreen} />
    </Stack.Navigator>
  );
}
