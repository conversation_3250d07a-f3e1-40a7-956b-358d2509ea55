
"use client";

import DigitalTasbeeh from '@/components/shared/DigitalTasbeeh';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Lightbulb, BookHeart, X, Star } from 'lucide-react';

// Main Zikr Text (Arabic, Roman Urdu, Hindi)
const zikrTextArabic = "يَا هُوَ، يَا مَنْ هُوَ، يَا مَنْ لَيْسَ لَهُ إِلَّا هُوَ، حَقّ، حَقّ، حَقّ";
const zikrTextRomanUrdu = "Ya <PERSON>, Ya <PERSON>, Ya Man Laisa Lahu Illa Hu, Haqq, Haqq, Haqq";
const zikrTextHindi = "या हू, या मन हू, या मन लैसा लहू इल्ला हू, हक़्क़, हक़्क़, हक़्क़"; // Corrected "लैस" to "लैसा"

// Specific Recitations (Arabic)
const DUROOD_AHLULBAYT_AR = "اَللّٰھُمَّ صَلِّ عَلٰی سَیِّدِنَا وَ مَوْلَانَا مُحَمَّدٍ وَّ عَلٰی سَیِّدِنَا عَلِیٍّ وَّ عَلٰی سَیِّدَتِنَا فَاطِمَةَ وَ عَلٰی سَیِّدَتِنَا زَیْنَبَ وَ عَلٰی سَیِّدِنَا حَسَنٍ وَّ عَلٰی سَیِّدِنَا حُسَیْنٍ وَّ عَلٰی اٰلِہٖ وَ صَحْبِہٖ وَ بَارِکْ وَ سَلِّمْ۔";
const DUA_E_NOORI_AR = "اَللّٰھُمَّ اجْعَلْ لِّیْ نُوْرًا فِیْ قَلْبِیْ، وَ نُوْرًا فِیْ قَبْرِیْ، وَ نُوْرًا فِیْ سَمْعِیْ، وَ نُوْرًا فِیْ بَصَرِیْ، وَ نُوْرًا فِیْ شَعْرِیْ، وَ نُوْرًا فِیْ بَشَرِیْ، وَ نُوْرًا فِیْ لَحْمِیْ، وَ نُوْرًا فِیْ دَمِیْ، وَ نُوْرًا فِیْ عِظَامِیْ، وَ نُوْرًا مِّنْۢ بَیْنِ یَدَیَّ، وَ نُوْرًا مِّنْ خَلْفِیْ، وَ نُوْرًا عَنْ یَّمِیْنِیْ، وَ نُوْرًا عَنْ شِمَالِیْ، وَ نُوْرًا مِّنْ فَوْقِیْ، وَ نُوْرًا مِّنْ تَحْتِیْ، وَاجْعَلْنِیْ نُوْرًا، وَ سَلِّمْنِیْ حَقًّا۔";
const TASBEEH_MALAIKA_AR = "سُبْحَانَ ٱللَّهِ وَبِحَمْدِهِ، سُبْحَانَ ٱللَّهِ ٱلْعَظِيمِ وَبِحَمْدِهِ اَستَغفِراللہ";

// Roman Urdu Transliterations
const DUROOD_AHLULBAYT_RO = "Allaahumma Salli 'Alaa Sayyidinaa Wa Maulaanaa Muhammadinw Wa 'Alaa Sayyidinaa 'Aliyyinw Wa 'Alaa Sayyidatinaa Faatimata Wa 'Alaa Sayyidatinaa Zainaba Wa 'Alaa Sayyidinaa Hasaninw Wa 'Alaa Sayyidinaa Husaininw Wa 'Alaa Aalihee Wa Sahbihee Wa Baarik Wa Sallim.";
const DUA_E_NOORI_RO = "Allaahummaj'al Lee Nooran Fee Qalbee, Wa Nooran Fee Qabree, Wa Nooran Fee Sam'ee, Wa Nooran Fee Basaree, Wa Nooran Fee Sha'ree, Wa Nooran Fee Basharee, Wa Nooran Fee Lahmee, Wa Nooran Fee Damee, Wa Nooran Fee 'Izaamee, Wa Nooran Mim Bain Yadayya, Wa Nooran Min Khalfee, Wa Nooran 'An Yameenee, Wa Nooran 'An Shimaalee, Wa Nooran Min Fawqee, Wa Nooran Min Tahtee, Waj'alnee Nooraa, Wa Sallimnee Haqqaa.";
const TASBEEH_MALAIKA_RO = "Subhaanallaahi Wa Bihamdihee, Subhaanallaahil 'Azeemi Wa Bihamdihee Astaghfirullah.";

// Hindi Transliterations
const DUROOD_AHLULBAYT_HI = "अल्लाहुम्म सल्लि 'अला सय्यिदिना व मौलाना मुहम्मदिन व 'अला सय्यिदिना 'अलिय्यिन व 'अला सय्यिदतिना फ़ातिमा व 'अला सय्यिदतिना ज़ैनब व 'अला सय्यिदिना हसनिन व 'अला सय्यिदिना हुसैनिन व 'अला आलिही व सह्बिही व बारिक व सल्लिम।";
const DUA_E_NOORI_HI = "अल्लाहुम्मज-अल ली नूरन फ़ी क़ल्बी, व नूरन फ़ी क़बरी, व नूरन फ़ी समई, व नूरन फ़ी बसरी, व नूरन फ़ी शअरी, व नूरन फ़ी बशरी, व नूरन फ़ी लह्मी, व नूरन फ़ी दमी, व नूरन फ़ी इज़ामी, व नूरन मिम-बैनि यदय-य, व नूरन मिन ख़लफ़ी, व नूरन अन यमीनी, व नूरन अन शिमाली, व नूरन मिन फ़ौक़ी, व नूरन मिन तहती, वज-अलनी नूरन, व सल्लिमनी हक़्क़न।";
const TASBEEH_MALAIKA_HI = "सुब्हानल्लाहि व बिहम्दिही, सुब्हानल्लाहिल अज़ीमि व बिहम्दिही अस्तग़फ़िरुल्लाह।";


type CardTranslations = {
  pageTitle: string;
  zikrAzeemTitle: string;
  duaNooriButtonLabel: string;
  duroodAhleBaytButtonLabel: string;
  tasbeehMalaikaButtonLabel: string;
  duaNooriCardTitle: string;
  duroodAhleBaytCardTitle: string;
  tasbeehMalaikaCardTitle: string;
  closeButtonSrText: string;
};

const translations: Record<Language, CardTranslations> = {
  en: {
    pageTitle: "Tasbeeh Counter",
    zikrAzeemTitle: "The Grand Zikr",
    duaNooriButtonLabel: "Dua-e-Noori",
    duroodAhleBaytButtonLabel: "Durood-e-Ahle Bayt",
    tasbeehMalaikaButtonLabel: "Tasbeeh Malaika",
    duaNooriCardTitle: "Dua-e-Noori (Prayer of Light)",
    duroodAhleBaytCardTitle: "Durood-e-Ahle Bayt (Salutations upon the Household)",
    tasbeehMalaikaCardTitle: "Tasbeeh Malaika (Angelic Praise)",
    closeButtonSrText: "Close Card",
  },
  ur: {
    pageTitle: "تسبیح کاؤنٹر",
    zikrAzeemTitle: "ذکرِ عظیم",
    duaNooriButtonLabel: "دعائے نوری",
    duroodAhleBaytButtonLabel: "درودِ اہلِ بیت",
    tasbeehMalaikaButtonLabel: "تسبیح ملائکہ",
    duaNooriCardTitle: "دعائے نوری",
    duroodAhleBaytCardTitle: "درودِ اہلِ بیت (علیہم السلام)",
    tasbeehMalaikaCardTitle: "تسبیح ملائکہ",
    closeButtonSrText: "کارڈ بند کریں",
  },
  ro: {
    pageTitle: "Tasbeeh Counter",
    zikrAzeemTitle: "Zikr-e-Azeem",
    duaNooriButtonLabel: "Dua-e-Noori",
    duroodAhleBaytButtonLabel: "Durood-e-Ahle Bayt",
    tasbeehMalaikaButtonLabel: "Tasbeeh Malaika",
    duaNooriCardTitle: "Dua-e-Noori",
    duroodAhleBaytCardTitle: "Durood-e-Ahle Bayt (Alaihimus Salam)",
    tasbeehMalaikaCardTitle: "Tasbeeh Malaika",
    closeButtonSrText: "Card Band Karein",
  },
  hi: {
    pageTitle: "तस्बीह काउंटर",
    zikrAzeemTitle: "ज़िक्र-ए-अज़ीम",
    duaNooriButtonLabel: "दुआ-ए-नूरी",
    duroodAhleBaytButtonLabel: "दुरूद-ए-अहले बैत",
    tasbeehMalaikaButtonLabel: "तस्बीह मलाइका",
    duaNooriCardTitle: "दुआ-ए-नूरी",
    duroodAhleBaytCardTitle: "दुरूद-ए-अहले बैत (अलैहिमुस सलाम)",
    tasbeehMalaikaCardTitle: "तस्बीह मलाइका",
    closeButtonSrText: "कार्ड बंद करें",
  },
  ar: {
    pageTitle: "عداد التسبيح",
    zikrAzeemTitle: "الذكر العظيم",
    duaNooriButtonLabel: "دعاء النور",
    duroodAhleBaytButtonLabel: "درود أهل البيت",
    tasbeehMalaikaButtonLabel: "تسبيح الملائكة",
    duaNooriCardTitle: "دعاء النور",
    duroodAhleBaytCardTitle: "درود أهل البيت (عليهم السلام)",
    tasbeehMalaikaCardTitle: "تسبيح الملائكة",
    closeButtonSrText: "أغلق البطاقة",
  },
};

export default function TasbeehPage() {
  const { language } = useLanguage();
  const [activeCard, setActiveCard] = useState<string | null>(null);

  const currentTranslations = translations[language] || translations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  const toggleCard = (cardId: string) => {
    setActiveCard(prev => (prev === cardId ? null : cardId));
  };

  const cardContentVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.4, ease: "easeOut" } },
    exit: { opacity: 0, y: -20, scale: 0.95, transition: { duration: 0.3, ease: "easeIn" } },
  };
  
  const zikrTextVariants = {
    hidden: { opacity: 0, y: 10, scale: 0.98 },
    visible: { opacity: 1, y: 0, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
    exit: { opacity: 0, y: -10, scale: 0.98, transition: { duration: 0.2, ease: "easeIn" } },
  };

  const getZikrText = () => {
    switch (language) {
      case 'ro': return zikrTextRomanUrdu;
      case 'hi': return zikrTextHindi;
      case 'ur':
      case 'ar':
      case 'en': 
      default: return zikrTextArabic;
    }
  };
  const zikrLang = (language === 'ur' || language === 'ar' || language === 'en') ? 'ar' : language;
  const zikrDir = (language === 'ur' || language === 'ar' || language === 'en') ? 'rtl' : 'ltr';

  const getRecitationContent = (type: 'noori' | 'durood' | 'malaika') => {
    let arabicText = "";
    let romanUrduText = "";
    let hindiText = "";

    if (type === 'noori') {
      arabicText = DUA_E_NOORI_AR;
      romanUrduText = DUA_E_NOORI_RO;
      hindiText = DUA_E_NOORI_HI;
    } else if (type === 'durood') {
      arabicText = DUROOD_AHLULBAYT_AR;
      romanUrduText = DUROOD_AHLULBAYT_RO;
      hindiText = DUROOD_AHLULBAYT_HI;
    } else if (type === 'malaika') {
      arabicText = TASBEEH_MALAIKA_AR;
      romanUrduText = TASBEEH_MALAIKA_RO;
      hindiText = TASBEEH_MALAIKA_HI;
    }

    if (language === 'ro') {
      return <p className="text-lg leading-relaxed text-foreground/90 mt-3 font-sans text-center p-2 bg-muted/10 rounded">{romanUrduText}</p>;
    }
    if (language === 'hi') {
      return <p className="text-lg leading-relaxed text-foreground/90 mt-3 font-sans text-center p-2 bg-muted/10 rounded" lang="hi">{hindiText}</p>;
    }
    // For 'en', 'ur', 'ar', or if transliteration is missing, show Arabic.
    return (
      <div lang="ar" dir="rtl" className="font-arabic text-xl md:text-2xl leading-relaxed p-4 bg-muted/20 rounded-md text-center">
        {arabicText}
      </div>
    );
  };

  const recitations = [
    { id: 'noori', labelKey: 'duaNooriButtonLabel', titleKey: 'duaNooriCardTitle', icon: Lightbulb, colorClasses: "bg-amber-400 hover:bg-amber-500 dark:bg-amber-500 dark:hover:bg-amber-600 text-amber-900 dark:text-amber-900", shadowClasses: "shadow-[0_0_15px_theme(colors.amber.300)] dark:shadow-[0_0_15px_theme(colors.amber.400)]" },
    { id: 'durood', labelKey: 'duroodAhleBaytButtonLabel', titleKey: 'duroodAhleBaytCardTitle', icon: BookHeart, colorClasses: "bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white", shadowClasses: "shadow-[0_0_15px_theme(colors.emerald.400)] dark:shadow-[0_0_15px_theme(colors.emerald.500)]" },
    { id: 'malaika', labelKey: 'tasbeehMalaikaButtonLabel', titleKey: 'tasbeehMalaikaCardTitle', icon: Star, colorClasses: "bg-pink-500 hover:bg-pink-600 dark:bg-pink-600 dark:hover:bg-pink-700 text-white", shadowClasses: "shadow-[0_0_15px_theme(colors.pink.400)] dark:shadow-[0_0_15px_theme(colors.pink.500)]" },
  ];

  return (
    <div className={cn("flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-4 space-y-6", fontClass)}>
      
      <div className={cn("flex flex-col items-center justify-center gap-3 mb-4", fontClass)}>
        <div className="flex flex-row items-start justify-center gap-4">
          {recitations.map(rec => {
            const Icon = rec.icon;
            return (
              <div key={rec.id} className="flex flex-col items-center gap-1 text-center">
                <Button
                  onClick={() => toggleCard(rec.id)}
                  variant="default"
                  size="icon"
                  className={cn(
                    "rounded-full w-16 h-16 p-0 flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-110",
                    rec.colorClasses,
                    "shadow-xl hover:shadow-lg",
                    rec.shadowClasses,
                    activeCard === rec.id && "ring-4 ring-offset-2 ring-primary dark:ring-primary ring-offset-background"
                  )}
                  aria-label={currentTranslations[rec.labelKey as keyof CardTranslations]}
                >
                  <Icon className="h-7 w-7" />
                </Button>
                <p className={cn("text-xs text-muted-foreground", fontClass)}>{currentTranslations[rec.labelKey as keyof CardTranslations]}</p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Container for Zikr text OR Dua/Durood/Tasbeeh Malaika cards */}
      <div className="w-full max-w-lg mx-auto mb-2 relative flex items-start justify-center min-h-[120px]"> {/* Adjusted min-height */}
        <AnimatePresence mode="wait">
          {activeCard === null && (
            <motion.div
              key="zikr-text-display"
              variants={zikrTextVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="w-full"
            >
               <h2 className={cn("text-lg font-semibold mb-3 text-muted-foreground", fontClass, isRtl ? "text-right" : "text-center")}>{currentTranslations.zikrAzeemTitle}</h2>
              <div
                className={cn("font-arabic text-3xl md:text-4xl text-primary text-center leading-relaxed shadow-sm p-3 rounded-md bg-card/50",
                  (language === 'ro' || language === 'hi') && 'font-sans'
                )}
                lang={zikrLang}
                dir={zikrDir}
              >
                {getZikrText()}
              </div>
            </motion.div>
          )}

          {recitations.map(rec => (
            activeCard === rec.id && (
              <motion.div
                key={`${rec.id}-card-display`}
                variants={cardContentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="w-full max-w-lg" 
              >
                <Card className="shadow-xl glass-effect">
                  <CardHeader className={cn("flex flex-row items-center justify-between pb-2", isRtl && "text-right")}>
                    <CardTitle className={cn("text-xl", fontClass)}>
                      {currentTranslations[rec.titleKey as keyof CardTranslations]}
                    </CardTitle>
                    <Button variant="ghost" size="icon" onClick={() => setActiveCard(null)} className="h-7 w-7">
                      <X className="h-4 w-4"/>
                      <span className="sr-only">{currentTranslations.closeButtonSrText}</span>
                    </Button>
                  </CardHeader>
                  <CardContent>
                    {getRecitationContent(rec.id as 'noori' | 'durood' | 'malaika')}
                  </CardContent>
                </Card>
              </motion.div>
            )
          ))}
        </AnimatePresence>
      </div>

      {/* Main Tasbeeh Counter - always visible */}
      <Card className="w-full max-w-xs sm:max-w-sm mx-auto shadow-xl glass-effect">
        <CardContent className="p-6">
          <DigitalTasbeeh storageKey="dedicatedTasbeehCounter_v2" initialCount={0} />
        </CardContent>
      </Card>
    </div>
  );
}
