export type Dua = {
  id: string;
  slug: string;
  title: string;
  arabicText: string;
  urduTranslation: string;
  transliteration?: string;
  instructions: string;
  benefits: string;
  audioUrl?: string; // Placeholder for audio file URL
  hasTasbeeh?: boolean;
  recitationTime?: string;
};

export const duasData: Dua[] = [
  {
    id: '1',
    slug: 'protection-calamities-enemies',
    title: 'Dua for protection from calamities and enemies',
    arabicText: 'بِسْمِ اللّٰهِ الَّذِيْ لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْاَرْضِ وَلَا فِي السَّمَاۗءِ وَهُوَ السَّمِيْعُ الْعَلِيْمُ',
    urduTranslation: 'اللہ کے نام سے جس کے نام کے ساتھ زمین و آسمان میں کوئی چیز نقصان نہیں پہنچا سکتی، اور وہ سننے والا، جاننے والا ہے۔',
    transliteration: "Bismillahillazi la yadurru ma'asmihi shai'un fil ardi wa la fis-sama'i wa Huwas Sami'ul 'Aleem.",
    instructions: 'Recite 3 times in the morning and evening.',
    benefits: 'Protection from all harm, calamities, and enemies.',
    recitationTime: 'Morning & Evening',
    hasTasbeeh: false,
  },
  {
    id: '2',
    slug: 'protection-enemy-evil-war',
    title: "Dua for protection from enemy's evil and in war",
    arabicText: 'اَللّٰهُمَّ اِنَّا نَجْعَلُكَ فِيْ نُحُوْرِهِمْ وَنَعُوْذُبِكَ مِنْ شُرُوْرِهِمْ',
    urduTranslation: 'اے اللہ! ہم تجھے ان کے سینوں میں ڈالتے ہیں اور ان کے شر سے تیری پناہ مانگتے ہیں۔',
    transliteration: "Allahumma inna naj'aluka fi nuhurihim wa na'uzubika min shururihim.",
    instructions: 'Recite frequently when facing an enemy or in times of conflict.',
    benefits: "Seeks Allah's help against enemies and their evil plans.",
    recitationTime: 'As needed',
    hasTasbeeh: false,
  },
  {
    id: '3',
    slug: 'tasbeeh-malaika',
    title: 'Tasbeeh Malaika (Angelic Praise)',
    arabicText: 'سُبْحَانَ ذِي الْمُلْكِ وَالْمَلَكُوْتِ، سُبْحَانَ ذِي الْعِزَّةِ وَالْعَظَمَةِ وَالْهَيْبَةِ وَالْقُدْرَةِ وَالْكِبْرِيَاءِ وَالْجَبَرُوْتِ، سُبْحَانَ الْمَلِكِ الْحَيِّ الَّذِيْ لَا يَنَامُ وَلَا يَمُوْتُ، سُبُّوْحٌ قُدُّوْسٌ رَبُّنَا وَرَبُّ الْمَلَاۗئِكَةِ وَالرُّوْحِ',
    urduTranslation: 'پاک ہے وہ ذات جو ملک اور بادشاہی کا مالک ہے۔ پاک ہے وہ ذات جو عزت، عظمت، ہیبت، قدرت، بڑائی اور جبروت کا مالک ہے۔ پاک ہے وہ بادشاہ جو زندہ ہے، نہ سوتا ہے نہ مرتا ہے۔ وہ بہت پاکیزہ، بہت مقدس ہے، ہمارا رب اور فرشتوں اور روح کا رب۔',
    transliteration: "Subhana zil mulki wal malakut. Subhana zil izzati wal azamati wal haybati wal qudrati wal kibriya'i wal jabarut. Subhanal malikil hayyil lazi la yanamu wa la yamut. Subbuhun quddusun Rabbuna wa Rabbul mala'ikati war Ruh.",
    instructions: 'Recite daily, especially after Fajr prayer.',
    benefits: 'A powerful praise of Allah, recited by angels. Brings immense blessings and spiritual elevation.',
    recitationTime: 'Daily, esp. After Fajr',
    hasTasbeeh: true, // Example: can be counted
  },
  {
    id: '4',
    slug: 'dua-e-noori',
    title: 'Dua-e-Noori',
    arabicText: 'اللَّهُمَّ نَوِّرْ قَلْبِي بِنُوْرِكَ يَا نُوْرَ النُّوْرِ وَ يَا مُنَوِّرَ النُّوْرِ، اَللّٰهُمَّ اجْعَلْ فِيْ قَلْبِيْ نُوْرًا...',
    urduTranslation: 'اے اللہ! میرے دل کو اپنے نور سے منور فرما، اے نور کے نور اور اے نور کو روشن کرنے والے! اے اللہ میرے دل میں نور پیدا فرما... (مختصر)',
    instructions: 'Part of Shagle Noori practice. Recited with specific intentions.',
    benefits: 'Invokes divine light (Noor) in the heart and soul.',
    recitationTime: 'During Shagle Noori',
    hasTasbeeh: false,
  }
];
