{"version": 3, "names": ["React", "ReanimatedHeaderHeightContext", "useReanimatedHeaderHeight", "height", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["reanimated/useReanimatedHeaderHeight.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,6BAA6B,MAAM,iCAAiC;AAE3E,eAAe,SAASC,yBAAyBA,CAAA,EAAG;EAClD,MAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,6BAA6B,CAAC;EAE9D,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,6KACF,CAAC;EACH;EAEA,OAAOH,MAAM;AACf"}