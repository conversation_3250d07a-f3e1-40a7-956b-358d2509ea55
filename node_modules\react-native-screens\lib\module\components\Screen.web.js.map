{"version": 3, "names": ["Animated", "View", "React", "screensEnabled", "InnerScreen", "NativeScreen", "Component", "render", "active", "activityState", "style", "enabled", "rest", "props", "undefined", "createElement", "_extends", "hidden", "display", "Screen", "createAnimatedComponent", "ScreenContext", "createContext"], "sourceRoot": "../../../src", "sources": ["components/Screen.web.tsx"], "mappings": ";AACA,SAASA,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,cAAc,QAAQ,SAAS;AAExC,OAAO,MAAMC,WAAW,GAAGH,IAAI;;AAE/B;AACA;AACA,OAAO,MAAMI,YAAY,SAASH,KAAK,CAACI,SAAS,CAAc;EAC7DC,MAAMA,CAAA,EAAgB;IACpB,IAAI;MACFC,MAAM;MACNC,aAAa;MACbC,KAAK;MACLC,OAAO,GAAGR,cAAc,CAAC,CAAC;MAC1B,GAAGS;IACL,CAAC,GAAG,IAAI,CAACC,KAAK;IAEd,IAAIF,OAAO,EAAE;MACX,IAAIH,MAAM,KAAKM,SAAS,IAAIL,aAAa,KAAKK,SAAS,EAAE;QACvDL,aAAa,GAAGD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;;MACA,oBACEN,KAAA,CAAAa,aAAA,CAACd;MACC;MAAA,EAAAe,QAAA;QACAC,MAAM,EAAER,aAAa,KAAK,CAAE;QAC5BC,KAAK,EAAE,CAACA,KAAK,EAAE;UAAEQ,OAAO,EAAET,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;QAAO,CAAC;MAAE,GAC/DG,IAAI,CACT,CAAC;IAEN;IAEA,oBAAOV,KAAA,CAAAa,aAAA,CAACd,IAAI,EAAKW,IAAO,CAAC;EAC3B;AACF;AAEA,MAAMO,MAAM,GAAGnB,QAAQ,CAACoB,uBAAuB,CAACf,YAAY,CAAC;AAE7D,OAAO,MAAMgB,aAAa,gBAAGnB,KAAK,CAACoB,aAAa,CAACH,MAAM,CAAC;AAExD,eAAeA,MAAM"}