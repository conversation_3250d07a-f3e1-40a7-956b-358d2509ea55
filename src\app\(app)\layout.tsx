
"use client";

import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { AppHeader } from '@/components/layout/AppHeader';
import { ThemeProvider } from 'next-themes';
import { AuthProvider } from '@/context/AuthContext'; // Import AuthProvider
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import FirebaseMessagingListener from '@/components/shared/FirebaseMessagingListener'; // Import the listener

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  return (
    // ThemeProvider, LanguageProvider, FontSizeProvider are now in RootLayout
    // AuthProvider should wrap the part of the app that needs auth state
    <AuthProvider> 
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <AppSidebar />
          <div className="flex flex-1 flex-col">
            <AppHeader />
            <AnimatePresence mode="wait">
              <motion.main
                key={pathname} // Key is important for AnimatePresence to detect route changes
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="flex-1 p-4 sm:p-6 lg:p-8 bg-background overflow-y-auto"
              >
                {children}
              </motion.main>
            </AnimatePresence>
          </div>
        </div>
        <FirebaseMessagingListener /> {/* Mount the listener here */}
      </SidebarProvider>
    </AuthProvider>
  );
}
