import * as React from "react"
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  border: '#e2e8f0',
}

export interface SeparatorProps extends ViewProps {
  orientation?: 'horizontal' | 'vertical'
  style?: ViewStyle
}

const Separator = React.forwardRef<View, SeparatorProps>(
  ({ orientation = 'horizontal', style, ...props }, ref) => {
    const separatorStyle = orientation === 'horizontal'
      ? styles.horizontal
      : styles.vertical

    return (
      <View
        ref={ref}
        style={[separatorStyle, style]}
        {...props}
      />
    )
  }
)

Separator.displayName = "Separator"

const styles = StyleSheet.create({
  horizontal: {
    height: 1,
    width: '100%',
    backgroundColor: colors.border,
  },
  vertical: {
    width: 1,
    height: '100%',
    backgroundColor: colors.border,
  },
})

export { Separator }
