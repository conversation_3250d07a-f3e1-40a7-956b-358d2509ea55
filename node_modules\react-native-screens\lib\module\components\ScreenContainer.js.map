{"version": 3, "names": ["Platform", "View", "React", "isNativePlatformSupported", "screensEnabled", "ScreenContainerNativeComponent", "ScreenNavigationContainerNativeComponent", "NativeScreenContainer", "OS", "NativeScreenNavigationContainer", "ScreenContainer", "props", "enabled", "hasTwoStates", "rest", "ScreenNavigationContainer", "createElement"], "sourceRoot": "../../../src", "sources": ["components/ScreenContainer.tsx"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,IAAI,QAAQ,cAAc;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,yBAAyB,EAAEC,cAAc,QAAQ,SAAS;;AAEnE;AACA,OAAOC,8BAA8B,MAAM,0CAA0C;AACrF,OAAOC,wCAAwC,MAAM,oDAAoD;AAEzG,OAAO,MAAMC,qBAAgE,GAC3EP,QAAQ,CAACQ,EAAE,KAAK,KAAK,GAAIH,8BAA8B,GAAWJ,IAAI;AACxE,OAAO,MAAMQ,+BAA0E,GACrFT,QAAQ,CAACQ,EAAE,KAAK,KAAK,GAChBF,wCAAwC,GACzCL,IAAI;AAEV,SAASS,eAAeA,CAACC,KAA2B,EAAE;EACpD,MAAM;IAAEC,OAAO,GAAGR,cAAc,CAAC,CAAC;IAAES,YAAY;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EAEnE,IAAIC,OAAO,IAAIT,yBAAyB,EAAE;IACxC,IAAIU,YAAY,EAAE;MAChB,MAAME,yBAAyB,GAC7Bf,QAAQ,CAACQ,EAAE,KAAK,KAAK,GACjBC,+BAA+B,GAC/BF,qBAAqB;MAC3B,oBAAOL,KAAA,CAAAc,aAAA,CAACD,yBAAyB,EAAKD,IAAO,CAAC;IAChD;IACA,oBAAOZ,KAAA,CAAAc,aAAA,CAACT,qBAAqB,EAAKO,IAAO,CAAC;EAC5C;EACA,oBAAOZ,KAAA,CAAAc,aAAA,CAACf,IAAI,EAAKa,IAAO,CAAC;AAC3B;AAEA,eAAeJ,eAAe"}