
"use client";

import { useState } from 'react';
import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import Link from 'next/link';
import { ListOrdered, BookText, Send, Loader2 } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
// import { collection, addDoc, serverTimestamp } from 'firebase/firestore'; // For actual Firestore
// import { db } from '@/lib/firebaseClient'; // For actual Firestore

export default function AdminDashboardPage() {
  const [broadcastMessage, setBroadcastMessage] = useState('');
  const [isSendingBroadcast, setIsSendingBroadcast] = useState(false);
  const { toast } = useToast();

  const handleSendBroadcast = async () => {
    if (!broadcastMessage.trim()) {
      toast({
        title: "Error",
        description: "Message cannot be empty.",
        variant: "destructive",
      });
      return;
    }
    setIsSendingBroadcast(true);
    try {
      // Simulate Firestore addDoc
      console.log("Simulating sending broadcast:", {
        messageText: broadcastMessage,
        timestamp: new Date(), // In a real app, use serverTimestamp()
        sentBy: 'Admin',
      });

      // Actual Firestore code would be:
      // await addDoc(collection(db, 'admin_broadcasts'), {
      //   messageText: broadcastMessage,
      //   timestamp: serverTimestamp(),
      //   sentBy: 'Admin',
      //   // Optional: title: "Admin Broadcast" 
      // });

      toast({
        title: "Success",
        description: "Broadcast message sent (simulated).",
      });
      setBroadcastMessage(''); // Clear textarea after sending
    } catch (error) {
      console.error("Error sending broadcast:", error);
      toast({
        title: "Error",
        description: "Failed to send broadcast message.",
        variant: "destructive",
      });
    } finally {
      setIsSendingBroadcast(false);
    }
  };

  return (
    <div className="space-y-8">
      <PageTitle title="Admin Dashboard" subtitle="Welcome to the Sabiriya App Management Panel." />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-lg">
          <CardHeader>
            <h2 className="text-xl font-semibold flex items-center"><ListOrdered className="h-6 w-6 mr-2 text-primary"/> Manage Duas & Wazaif</h2>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">Create, edit, and delete Duas and Wazaif.</p>
            <Link href="/admin/manage-duas" passHref>
              <Button>Go to Duas</Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader>
            <h2 className="text-xl font-semibold flex items-center"><BookText className="h-6 w-6 mr-2 text-primary"/> Manage Teachings</h2>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">Manage spiritual teachings and categories.</p>
            <Button disabled>Go to Teachings (Coming Soon)</Button>
          </CardContent>
        </Card>
      </div>

      {/* Send Broadcast Message Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <h2 className="text-xl font-semibold flex items-center">
            <Send className="h-6 w-6 mr-2 text-primary" /> Send Broadcast Message
          </h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Type your broadcast message here..."
            value={broadcastMessage}
            onChange={(e) => setBroadcastMessage(e.target.value)}
            rows={4}
            disabled={isSendingBroadcast}
            className="bg-background/70"
          />
          <Button onClick={handleSendBroadcast} disabled={isSendingBroadcast}>
            {isSendingBroadcast ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Sending...
              </>
            ) : (
              "Send Message"
            )}
          </Button>
        </CardContent>
      </Card>

       <Card className="mt-8 shadow-lg">
          <CardHeader>
            <h2 className="text-xl font-semibold">Admin Guidelines</h2>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside space-y-1 text-muted-foreground">
              <li>Use this panel to manage the content of the Sabiriya application.</li>
              <li>Ensure all information entered is accurate and authentic.</li>
              <li>Regularly review content for any necessary updates.</li>
              <li>The admin role check is currently a placeholder (<code className="bg-muted p-1 rounded text-xs"><EMAIL></code>). For production, secure role-based access control using Firebase Custom Claims must be implemented.</li>
            </ul>
          </CardContent>
        </Card>
    </div>
  );
}
