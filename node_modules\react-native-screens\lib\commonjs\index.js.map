{"version": 3, "names": ["_types", "require", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_core", "_Screen", "_interopRequireWildcard", "_ScreenContainer", "_ScreenStack", "_interopRequireDefault", "_ScreenStackHeaderConfig", "_SearchBar", "_FullWindowOverlay", "_NativeScreensModule", "_GHContext", "_utils", "_useTransitionProgress", "obj", "__esModule", "default", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "i", "set"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAAC,MAAA,CAAAC,IAAA,CAAAH,MAAA,EAAAI,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAL,MAAA,CAAAK,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAb,MAAA,CAAAK,GAAA;IAAA;EAAA;AAAA;AAKA,IAAAS,KAAA,GAAAb,OAAA;AAWA,IAAAc,OAAA,GAAAC,uBAAA,CAAAf,OAAA;AAOA,IAAAgB,gBAAA,GAAAD,uBAAA,CAAAf,OAAA;AAMA,IAAAiB,YAAA,GAAAC,sBAAA,CAAAlB,OAAA;AAEA,IAAAmB,wBAAA,GAAAnB,OAAA;AAUA,IAAAoB,UAAA,GAAAL,uBAAA,CAAAf,OAAA;AAMA,IAAAqB,kBAAA,GAAAH,sBAAA,CAAAlB,OAAA;AAKA,IAAAsB,oBAAA,GAAAJ,sBAAA,CAAAlB,OAAA;AAKA,IAAAuB,UAAA,GAAAvB,OAAA;AAKA,IAAAwB,MAAA,GAAAxB,OAAA;AASA,IAAAyB,sBAAA,GAAAP,sBAAA,CAAAlB,OAAA;AAA2E,SAAAkB,uBAAAQ,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAf,wBAAAe,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAH,UAAA,SAAAG,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAF,OAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAC,GAAA,CAAAJ,CAAA,UAAAG,CAAA,CAAArB,GAAA,CAAAkB,CAAA,OAAAK,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAApC,MAAA,CAAAS,cAAA,IAAAT,MAAA,CAAAqC,wBAAA,WAAAC,CAAA,IAAAT,CAAA,oBAAAS,CAAA,IAAAtC,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAuB,CAAA,EAAAS,CAAA,SAAAC,CAAA,GAAAH,CAAA,GAAApC,MAAA,CAAAqC,wBAAA,CAAAR,CAAA,EAAAS,CAAA,UAAAC,CAAA,KAAAA,CAAA,CAAA5B,GAAA,IAAA4B,CAAA,CAAAC,GAAA,IAAAxC,MAAA,CAAAS,cAAA,CAAAyB,CAAA,EAAAI,CAAA,EAAAC,CAAA,IAAAL,CAAA,CAAAI,CAAA,IAAAT,CAAA,CAAAS,CAAA,YAAAJ,CAAA,CAAAP,OAAA,GAAAE,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAQ,GAAA,CAAAX,CAAA,EAAAK,CAAA,GAAAA,CAAA"}