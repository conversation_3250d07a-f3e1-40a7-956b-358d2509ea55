{"version": 3, "names": ["React", "Image", "StyleSheet", "ScreenStackHeaderConfigNativeComponent", "ScreenStackHeaderSubviewNativeComponent", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "ScreenStackHeaderBackButtonImage", "props", "createElement", "type", "style", "styles", "headerSubview", "_extends", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "create", "position", "top", "right", "flexDirection", "alignItems", "justifyContent"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAMzB,SAASC,KAAK,EAAcC,UAAU,QAAmB,cAAc;;AAEvE;AACA,OAAOC,sCAAsC,MAAM,kDAAkD;AACrG,OAAOC,uCAAuC,MAAM,mDAAmD;AAEvG,OAAO,MAAMC,uBAA0E,GACrFF,sCAA6C;AAC/C,OAAO,MAAMG,wBAEZ,GAAGF,uCAA8C;AAElD,OAAO,MAAMG,gCAAgC,GAC3CC,KAAiB,iBAEjBR,KAAA,CAAAS,aAAA,CAACH,wBAAwB;EAACI,IAAI,EAAC,MAAM;EAACC,KAAK,EAAEC,MAAM,CAACC;AAAc,gBAChEb,KAAA,CAAAS,aAAA,CAACR,KAAK,EAAAa,QAAA;EAACC,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKR,KAAK,CAAG,CAChC,CAC3B;AAED,OAAO,MAAMS,0BAA0B,GACrCT,KAAyC,iBAEzCR,KAAA,CAAAS,aAAA,CAACH,wBAAwB,EAAAQ,QAAA,KACnBN,KAAK;EACTE,IAAI,EAAC,OAAO;EACZC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAED,OAAO,MAAMK,yBAAyB,GACpCV,KAAyC,iBAEzCR,KAAA,CAAAS,aAAA,CAACH,wBAAwB,EAAAQ,QAAA,KACnBN,KAAK;EACTE,IAAI,EAAC,MAAM;EACXC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAED,OAAO,MAAMM,2BAA2B,GACtCX,KAAyC,iBAEzCR,KAAA,CAAAS,aAAA,CAACH,wBAAwB,EAAAQ,QAAA,KACnBN,KAAK;EACTE,IAAI,EAAC,QAAQ;EACbC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAED,OAAO,MAAMO,8BAA8B,GACzCZ,KAA8C,iBAE9CR,KAAA,CAAAS,aAAA,CAACH,wBAAwB,EAAAQ,QAAA,KACnBN,KAAK;EACTE,IAAI,EAAC,WAAW;EAChBC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAED,MAAMD,MAAM,GAAGV,UAAU,CAACmB,MAAM,CAAC;EAC/BR,aAAa,EAAE;IACbS,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC"}