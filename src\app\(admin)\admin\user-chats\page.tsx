
"use client";

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/context/AuthContext';
import { db } from '@/lib/firebaseClient';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  addDoc,
  serverTimestamp,
  doc,
  setDoc,
  type Timestamp,
  type DocumentData,
  type Unsubscribe
} from 'firebase/firestore';
import ChatMessage, { type ChatMessageProps as AppChatMessage } from '@/components/chat/ChatMessage';
import ChatInput from '@/components/chat/ChatInput';
import PageTitle from '@/components/shared/PageTitle';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { List, MessageCircle, Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

// **IMPORTANT: Replace this with the actual Firebase UID of the admin account in your project**
const KHANQAH_ADMIN_UID = "KHANQAH_ADMIN_ACCOUNT_UID"; 
const KHANQAH_ADMIN_NAME = "Sabiriya Khanqah"; // Display name for the admin
const KHANQAH_ADMIN_AVATAR = "https://placehold.co/100x100/7dd3fc/0f172a.png?text=SK"; // Placeholder

interface Message {
  id: string;
  senderId: string;
  text: string;
  timestamp: Timestamp | null;
}

interface ChatRoom {
  id: string;
  participants: string[];
  participantInfo: Record<string, { displayName: string; photoURL: string }>;
  lastMessageText?: string;
  lastMessageTimestamp?: Timestamp;
  otherParticipant: { // Convenience field
    uid: string;
    displayName: string;
    photoURL: string;
  };
}

export default function AdminUserChatsPage() {
  const { user: adminUser, loading: authLoading } = useAuth(); // Assuming this admin is logged in via Firebase Auth
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [selectedChatRoomId, setSelectedChatRoomId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoadingChats, setIsLoadingChats] = useState(true);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSending, setIsSending] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!adminUser || adminUser.uid !== KHANQAH_ADMIN_UID) {
        // Basic check, though layout should handle admin role properly
        // setError("Access denied. You are not authorized to view this page.");
        // setIsLoadingChats(false);
        return;
    }

    setIsLoadingChats(true);
    const chatsRef = collection(db, 'chats');
    const q = query(
      chatsRef,
      where('participants', 'array-contains', KHANQAH_ADMIN_UID),
      orderBy('lastMessageTimestamp', 'desc')
    );

    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const rooms: ChatRoom[] = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data() as DocumentData;
          const otherParticipantUid = data.participants.find((p: string) => p !== KHANQAH_ADMIN_UID);
          
          if (otherParticipantUid && data.participantInfo && data.participantInfo[otherParticipantUid]) {
            rooms.push({
              id: doc.id,
              participants: data.participants,
              participantInfo: data.participantInfo,
              lastMessageText: data.lastMessageText || "No messages yet",
              lastMessageTimestamp: data.lastMessageTimestamp,
              otherParticipant: {
                uid: otherParticipantUid,
                displayName: data.participantInfo[otherParticipantUid]?.displayName || "User",
                photoURL: data.participantInfo[otherParticipantUid]?.photoURL || `https://placehold.co/100x100?text=${(data.participantInfo[otherParticipantUid]?.displayName || "U")[0].toUpperCase()}`,
              }
            });
          }
        });
        setChatRooms(rooms);
        setIsLoadingChats(false);
        setError(null);
      },
      (err) => {
        console.error("Error fetching chat rooms: ", err);
        setError("Failed to load chat rooms.");
        setIsLoadingChats(false);
      }
    );
    return () => unsubscribe();
  }, [adminUser]);

  useEffect(() => {
    if (!selectedChatRoomId) {
      setMessages([]);
      return;
    }

    setIsLoadingMessages(true);
    const messagesRef = collection(db, 'chats', selectedChatRoomId, 'messages');
    const q = query(messagesRef, orderBy('timestamp', 'asc'));

    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const fetchedMessages: Message[] = [];
        querySnapshot.forEach((doc) => {
          fetchedMessages.push({ id: doc.id, ...doc.data() } as Message);
        });
        setMessages(fetchedMessages);
        setIsLoadingMessages(false);
      },
      (err) => {
        console.error("Error fetching messages: ", err);
        setError("Failed to load messages for this chat.");
        setIsLoadingMessages(false);
      }
    );
    return () => unsubscribe();
  }, [selectedChatRoomId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSelectChatRoom = (roomId: string) => {
    setSelectedChatRoomId(roomId);
    // Optionally, reset unread count for admin here
    // const chatDocRef = doc(db, 'chats', roomId);
    // updateDoc(chatDocRef, { [`unreadCount.${KHANQAH_ADMIN_UID}`]: 0 });
  };

  const handleAdminSendMessage = async (messageText: string) => {
    if (!selectedChatRoomId || !messageText.trim() || !adminUser) return;
    setIsSending(true);

    const messagesRef = collection(db, 'chats', selectedChatRoomId, 'messages');
    const chatDocRef = doc(db, 'chats', selectedChatRoomId);
    const selectedRoom = chatRooms.find(room => room.id === selectedChatRoomId);
    if (!selectedRoom) return;

    const otherParticipantUid = selectedRoom.otherParticipant.uid;

    try {
      await addDoc(messagesRef, {
        senderId: KHANQAH_ADMIN_UID,
        text: messageText.trim(),
        timestamp: serverTimestamp(),
      });

      await setDoc(chatDocRef, {
        lastMessageText: messageText.trim(),
        lastMessageTimestamp: serverTimestamp(),
        lastMessageSenderId: KHANQAH_ADMIN_UID,
        [`unreadCount.${otherParticipantUid}`]: (selectedRoom.participantInfo[otherParticipantUid]?.unreadCount || 0) + 1,
        [`unreadCount.${KHANQAH_ADMIN_UID}`]: 0
      }, { merge: true });

    } catch (err) {
      console.error("Error sending message: ", err);
      setError("Failed to send message.");
    } finally {
      setIsSending(false);
    }
  };
  
  const getOtherParticipant = (room: ChatRoom) => {
    return room.participantInfo[room.otherParticipant.uid] || { displayName: 'User', photoURL: '' };
  };


  if (authLoading) {
    return <div className="flex justify-center items-center min-h-[200px]"><LoadingSpinner size={32}/></div>;
  }

  if (!adminUser) {
     return <ErrorMessage message="You must be logged in as an admin to view this page." />;
  }
  // A more robust admin check from AuthContext or custom claims would be ideal here
  // if (adminUser.uid !== KHANQAH_ADMIN_UID && !checkAdminStatus(adminUser)) { 
  //    return <ErrorMessage message="Access Denied. You are not authorized." />;
  // }


  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-var(--header-height,4rem)-var(--footer-height,0rem)-2rem)] gap-4">
      {/* Chat List Panel */}
      <Card className="w-full md:w-1/3 lg:w-1/4 shadow-lg flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center"><List className="mr-2 h-6 w-6 text-primary"/> User Chats</CardTitle>
        </CardHeader>
        <ScrollArea className="flex-grow">
          <CardContent className="p-0">
            {isLoadingChats && <div className="p-4 text-center"><LoadingSpinner /> Loading chats...</div>}
            {error && !isLoadingChats && <div className="p-4"><ErrorMessage message={error} /></div>}
            {!isLoadingChats && chatRooms.length === 0 && (
              <p className="p-4 text-center text-muted-foreground">No active chats found.</p>
            )}
            {chatRooms.map((room) => {
              const otherP = getOtherParticipant(room);
              return (
                <Button
                  key={room.id}
                  variant={selectedChatRoomId === room.id ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start p-3 h-auto rounded-none border-b",
                    selectedChatRoomId === room.id && "bg-primary/10"
                  )}
                  onClick={() => handleSelectChatRoom(room.id)}
                >
                  <Avatar className="h-9 w-9 mr-3">
                    <AvatarImage src={otherP.photoURL} alt={otherP.displayName} />
                    <AvatarFallback>{otherP.displayName.substring(0,1).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 text-left">
                    <p className="font-semibold text-sm truncate text-foreground">{otherP.displayName}</p>
                    <p className="text-xs text-muted-foreground truncate">
                      {room.lastMessageText}
                    </p>
                     {room.lastMessageTimestamp && (
                        <p className="text-xs text-muted-foreground/70 mt-0.5">
                            {formatDistanceToNow(room.lastMessageTimestamp.toDate(), { addSuffix: true })}
                        </p>
                    )}
                  </div>
                </Button>
              );
            })}
          </CardContent>
        </ScrollArea>
      </Card>

      {/* Chat View Panel */}
      <Card className="flex-1 shadow-lg flex flex-col">
        {!selectedChatRoomId ? (
          <div className="flex-1 flex flex-col items-center justify-center text-center p-6">
            <MessageCircle className="h-16 w-16 text-muted-foreground mb-4" />
            <p className="text-muted-foreground">Select a chat to view messages.</p>
             <p className="text-xs text-muted-foreground mt-1">(Admin UID: {KHANQAH_ADMIN_UID})</p>
          </div>
        ) : (
          <>
            <CardHeader className="border-b">
              <CardTitle className="flex items-center">
                 <Avatar className="h-9 w-9 mr-3">
                    <AvatarImage src={chatRooms.find(r=>r.id === selectedChatRoomId)?.otherParticipant.photoURL} />
                    <AvatarFallback>{chatRooms.find(r=>r.id === selectedChatRoomId)?.otherParticipant.displayName.substring(0,1).toUpperCase()}</AvatarFallback>
                  </Avatar>
                {chatRooms.find(r=>r.id === selectedChatRoomId)?.otherParticipant.displayName || "User"}
              </CardTitle>
            </CardHeader>
            <ScrollArea className="flex-grow p-4 bg-background/70">
              {isLoadingMessages && <div className="text-center"><LoadingSpinner /> Loading messages...</div>}
              {!isLoadingMessages && messages.length === 0 && (
                <p className="text-center text-muted-foreground py-10">No messages in this chat yet. Start the conversation!</p>
              )}
              <div className="space-y-1">
                {messages.map((msg) => (
                  <ChatMessage
                    key={msg.id}
                    messageId={msg.id}
                    text={msg.text}
                    timestamp={msg.timestamp?.toDate() || new Date()}
                    senderId={msg.senderId}
                    senderName={msg.senderId === KHANQAH_ADMIN_UID ? KHANQAH_ADMIN_NAME : chatRooms.find(r=>r.id === selectedChatRoomId)?.otherParticipant.displayName || "User"}
                    senderAvatar={msg.senderId === KHANQAH_ADMIN_UID ? KHANQAH_ADMIN_AVATAR : chatRooms.find(r=>r.id === selectedChatRoomId)?.otherParticipant.photoURL}
                    isCurrentUser={msg.senderId === KHANQAH_ADMIN_UID}
                  />
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            <ChatInput
              onSendMessage={handleAdminSendMessage}
              placeholder="Type your reply..."
              isLoading={isSending}
              className="border-t"
            />
          </>
        )}
      </Card>
    </div>
  );
}
