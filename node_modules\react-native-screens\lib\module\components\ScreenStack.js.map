{"version": 3, "names": ["React", "freezeEnabled", "DelayedFreeze", "ScreenStackNativeComponent", "NativeScreenStack", "ScreenStack", "props", "children", "gestureDetectorBridge", "rest", "ref", "useRef", "size", "Children", "count", "childrenWithFreeze", "map", "child", "index", "key", "descriptor", "descriptors", "isFreezeEnabled", "options", "freezeOnBlur", "createElement", "freeze", "useEffect", "current", "stackUseEffectCallback", "_extends"], "sourceRoot": "../../../src", "sources": ["components/ScreenStack.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAA2BC,aAAa,QAAQ,sBAAsB;AACtE,OAAOC,aAAa,MAAM,yBAAyB;;AAEnD;AACA,OAAOC,0BAA0B,MAAM,sCAAsC;AAC7E,MAAMC,iBAAwD,GAC5DD,0BAAiC;AAEnC,SAASE,WAAWA,CAACC,KAAuB,EAAE;EAC5C,MAAM;IAAEC,QAAQ;IAAEC,qBAAqB;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EAC1D,MAAMI,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,IAAI,GAAGZ,KAAK,CAACa,QAAQ,CAACC,KAAK,CAACP,QAAQ,CAAC;EAC3C;EACA,MAAMQ,kBAAkB,GAAGf,KAAK,CAACa,QAAQ,CAACG,GAAG,CAACT,QAAQ,EAAE,CAACU,KAAK,EAAEC,KAAK,KAAK;IACxE;IACA,MAAM;MAAEZ,KAAK;MAAEa;IAAI,CAAC,GAAGF,KAAK;IAC5B,MAAMG,UAAU,GAAGd,KAAK,EAAEc,UAAU,IAAId,KAAK,EAAEe,WAAW,GAAGF,GAAG,CAAC;IACjE,MAAMG,eAAe,GACnBF,UAAU,EAAEG,OAAO,EAAEC,YAAY,IAAIvB,aAAa,CAAC,CAAC;IAEtD,oBACED,KAAA,CAAAyB,aAAA,CAACvB,aAAa;MAACwB,MAAM,EAAEJ,eAAe,IAAIV,IAAI,GAAGM,KAAK,GAAG;IAAE,GACxDD,KACY,CAAC;EAEpB,CAAC,CAAC;EAEFjB,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAInB,qBAAqB,EAAE;MACzBA,qBAAqB,CAACoB,OAAO,CAACC,sBAAsB,CAACnB,GAAG,CAAC;IAC3D;EACF,CAAC,CAAC;EACF,oBACEV,KAAA,CAAAyB,aAAA,CAACrB,iBAAiB,EAAA0B,QAAA,KAAKrB,IAAI;IAAEC,GAAG,EAAEA;EAAI,IACnCK,kBACgB,CAAC;AAExB;AAEA,eAAeV,WAAW"}