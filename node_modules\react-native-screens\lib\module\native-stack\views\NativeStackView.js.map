{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warnOnce", "ScreenStack", "ScreenContext", "GHContext", "StackActions", "useTheme", "useSafeAreaFrame", "useSafeAreaInsets", "HeaderConfig", "SafeAreaProviderCompat", "getDefaultHeaderHeight", "getStatusBarHeight", "HeaderHeightContext", "AnimatedHeaderHeightContext", "isAndroid", "OS", "Container", "__DEV__", "DebugContainer", "props", "stackPresentation", "rest", "createElement", "MaybeNestedStack", "_ref", "options", "route", "children", "colors", "headerShown", "contentStyle", "Screen", "useContext", "isHeaderInModal", "headerShownPreviousRef", "useRef", "useEffect", "current", "name", "content", "style", "styles", "container", "backgroundColor", "background", "collapsable", "dimensions", "topInset", "top", "isStatusBarTranslucent", "statusBarTranslucent", "statusBarHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headerLargeTitle", "headerHeight", "enabled", "isNativeStack", "absoluteFill", "Provider", "value", "_extends", "RouteView", "_ref2", "descriptors", "index", "navigation", "stateKey", "screensRefs", "render", "renderScene", "key", "gestureEnabled", "hideKeyboardOnSwipe", "homeIndicatorHidden", "sheetAllowedDetents", "sheetLargestUndimmedDetent", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "nativeBackButtonDismissalEnabled", "navigationBarColor", "navigationBarHidden", "replaceAnimation", "screenOrientation", "statusBarAnimation", "statusBarColor", "statusBarHidden", "statusBarStyle", "swipeDirection", "transitionDuration", "freezeOnBlur", "customAnimationOnSwipe", "fullScreenSwipeEnabled", "gestureResponseDistance", "stackAnimation", "undefined", "defaultHeaderHeight", "parentHeaderHeight", "isHeaderInPush", "staticHeaderHeight", "cachedAnimatedHeaderHeight", "animatedHeaderHeight", "Value", "useNativeDriver", "dark", "screenRef", "ref", "onHeaderBackButtonClicked", "dispatch", "pop", "source", "target", "onWillAppear", "emit", "type", "data", "closing", "onWillDisappear", "onAppear", "onDisappear", "onHeaderHeightChange", "e", "nativeEvent", "setValue", "onDismissed", "dismissCount", "onGestureCancel", "NativeStackViewInner", "_ref3", "state", "routes", "currentRouteKey", "goBackGesture", "transitionAnimation", "screenEdgeGesture", "gestureDetectorBridge", "stackUseEffectCallback", "_stackRef", "ScreenGestureDetector", "console", "warn", "map", "NativeStackView", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/NativeStackView.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAC9E;AACA;AACA,OAAOC,YAAY,MAAM,iDAAiD;AAC1E,OAAOC,QAAQ,MAAM,WAAW;AAChC,SACEC,WAAW,EAEXC,aAAa,EACbC,SAAS,QAEJ,sBAAsB;AAC7B,SAEEC,YAAY,EAEZC,QAAQ,QAIH,0BAA0B;AACjC,SACEC,gBAAgB,EAChBC,iBAAiB,QACZ,gCAAgC;AAQvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,2BAA2B,MAAM,sCAAsC;AAE9E,MAAMC,SAAS,GAAGlB,QAAQ,CAACmB,EAAE,KAAK,SAAS;AAE3C,IAAIC,SAAS,GAAGlB,IAAI;AAEpB,IAAImB,OAAO,EAAE;EACX,MAAMC,cAAc,GAClBC,KAAgE,IAC7D;IACH,MAAM;MAAEC,iBAAiB;MAAE,GAAGC;IAAK,CAAC,GAAGF,KAAK;IAC5C,IAAIvB,QAAQ,CAACmB,EAAE,KAAK,KAAK,IAAIK,iBAAiB,KAAK,MAAM,EAAE;MACzD,oBACE1B,KAAA,CAAA4B,aAAA,CAACvB,YAAY,qBACXL,KAAA,CAAA4B,aAAA,CAACxB,IAAI,EAAKuB,IAAO,CACL,CAAC;IAEnB;IACA,oBAAO3B,KAAA,CAAA4B,aAAA,CAACxB,IAAI,EAAKuB,IAAO,CAAC;EAC3B,CAAC;EACD;EACAL,SAAS,GAAGE,cAAc;AAC5B;AAEA,MAAMK,gBAAgB,GAAGC,IAAA,IAUnB;EAAA,IAVoB;IACxBC,OAAO;IACPC,KAAK;IACLN,iBAAiB;IACjBO;EAMF,CAAC,GAAAH,IAAA;EACC,MAAM;IAAEI;EAAO,CAAC,GAAGvB,QAAQ,CAAC,CAAC;EAC7B,MAAM;IAAEwB,WAAW,GAAG,IAAI;IAAEC;EAAa,CAAC,GAAGL,OAAO;EAEpD,MAAMM,MAAM,GAAGrC,KAAK,CAACsC,UAAU,CAAC9B,aAAa,CAAC;EAE9C,MAAM+B,eAAe,GAAGnB,SAAS,GAC7B,KAAK,GACLM,iBAAiB,KAAK,MAAM,IAAIS,WAAW,KAAK,IAAI;EAExD,MAAMK,sBAAsB,GAAGxC,KAAK,CAACyC,MAAM,CAACN,WAAW,CAAC;EAExDnC,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpBpC,QAAQ,CACN,CAACc,SAAS,IACRM,iBAAiB,KAAK,MAAM,IAC5Bc,sBAAsB,CAACG,OAAO,KAAKR,WAAW,EAC/C,6IAA4IH,KAAK,CAACY,IAAK,IAC1J,CAAC;IAEDJ,sBAAsB,CAACG,OAAO,GAAGR,WAAW;EAC9C,CAAC,EAAE,CAACA,WAAW,EAAET,iBAAiB,EAAEM,KAAK,CAACY,IAAI,CAAC,CAAC;EAEhD,MAAMC,OAAO,gBACX7C,KAAA,CAAA4B,aAAA,CAACN,SAAS;IACRwB,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChBtB,iBAAiB,KAAK,kBAAkB,IACtCA,iBAAiB,KAAK,2BAA2B,IAAI;MACnDuB,eAAe,EAAEf,MAAM,CAACgB;IAC1B,CAAC,EACHd,YAAY;IAEd;IAAA;IACAV,iBAAiB,EAAEA;IACnB;IACA;IACA;IAAA;IACAyB,WAAW,EAAE;EAAM,GAClBlB,QACQ,CACZ;EAED,MAAMmB,UAAU,GAAGxC,gBAAgB,CAAC,CAAC;EACrC,MAAMyC,QAAQ,GAAGxC,iBAAiB,CAAC,CAAC,CAACyC,GAAG;EACxC,MAAMC,sBAAsB,GAAGxB,OAAO,CAACyB,oBAAoB,IAAI,KAAK;EACpE,MAAMC,eAAe,GAAGxC,kBAAkB,CACxCoC,QAAQ,EACRD,UAAU,EACVG,sBACF,CAAC;EAED,MAAMG,cAAc,GAAG3B,OAAO,CAAC4B,gBAAgB,IAAI,KAAK;EAExD,MAAMC,YAAY,GAAG5C,sBAAsB,CACzCoC,UAAU,EACVK,eAAe,EACf/B,iBAAiB,EACjBgC,cACF,CAAC;EAED,IAAInB,eAAe,EAAE;IACnB,oBACEvC,KAAA,CAAA4B,aAAA,CAACrB,WAAW;MAACuC,KAAK,EAAEC,MAAM,CAACC;IAAU,gBACnChD,KAAA,CAAA4B,aAAA,CAACS,MAAM;MACLwB,OAAO;MACPC,aAAa;MACbJ,cAAc,EAAEA,cAAe;MAC/BZ,KAAK,EAAE3C,UAAU,CAAC4D;IAAa,gBAC/B/D,KAAA,CAAA4B,aAAA,CAACV,mBAAmB,CAAC8C,QAAQ;MAACC,KAAK,EAAEL;IAAa,gBAChD5D,KAAA,CAAA4B,aAAA,CAACd,YAAY,EAAAoD,QAAA,KAAKnC,OAAO;MAAEC,KAAK,EAAEA;IAAM,EAAE,CAAC,EAC1Ca,OAC2B,CACxB,CACG,CAAC;EAElB;EACA,OAAOA,OAAO;AAChB,CAAC;AASD,MAAMsB,SAAS,GAAGC,KAAA,IAcZ;EAAA,IAda;IACjBC,WAAW;IACXrC,KAAK;IACLsC,KAAK;IACLC,UAAU;IACVC,QAAQ;IACRC;EAQF,CAAC,GAAAL,KAAA;EACC,MAAM;IAAErC,OAAO;IAAE2C,MAAM,EAAEC;EAAY,CAAC,GAAGN,WAAW,CAACrC,KAAK,CAAC4C,GAAG,CAAC;EAC/D,MAAM;IACJC,cAAc;IACd1C,WAAW;IACX2C,mBAAmB;IACnBC,mBAAmB;IACnBC,mBAAmB,GAAG,OAAO;IAC7BC,0BAA0B,GAAG,KAAK;IAClCC,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,gCAAgC,GAAG,KAAK;IACxCC,kBAAkB;IAClBC,mBAAmB;IACnBC,gBAAgB,GAAG,KAAK;IACxBC,iBAAiB;IACjBC,kBAAkB;IAClBC,cAAc;IACdC,eAAe;IACfC,cAAc;IACdrC,oBAAoB;IACpBsC,cAAc,GAAG,YAAY;IAC7BC,kBAAkB;IAClBC;EACF,CAAC,GAAGjE,OAAO;EAEX,IAAI;IACFkE,sBAAsB;IACtBC,sBAAsB;IACtBC,uBAAuB;IACvBC,cAAc;IACd1E,iBAAiB,GAAG;EACtB,CAAC,GAAGK,OAAO;EAEX,IAAI+D,cAAc,KAAK,UAAU,EAAE;IACjC;IACA;IACA;IACA;IACA;IACA,IAAII,sBAAsB,KAAKG,SAAS,EAAE;MACxCH,sBAAsB,GAAG,IAAI;IAC/B;IACA,IAAID,sBAAsB,KAAKI,SAAS,EAAE;MACxCJ,sBAAsB,GAAG,IAAI;IAC/B;IACA,IAAIG,cAAc,KAAKC,SAAS,EAAE;MAChCD,cAAc,GAAG,mBAAmB;IACtC;EACF;EAEA,IAAI9B,KAAK,KAAK,CAAC,EAAE;IACf;IACA;IACA5C,iBAAiB,GAAG,MAAM;EAC5B;EAEA,MAAM0B,UAAU,GAAGxC,gBAAgB,CAAC,CAAC;EACrC,MAAMyC,QAAQ,GAAGxC,iBAAiB,CAAC,CAAC,CAACyC,GAAG;EACxC,MAAMC,sBAAsB,GAAGxB,OAAO,CAACyB,oBAAoB,IAAI,KAAK;EACpE,MAAMC,eAAe,GAAGxC,kBAAkB,CACxCoC,QAAQ,EACRD,UAAU,EACVG,sBACF,CAAC;EAED,MAAMG,cAAc,GAAG3B,OAAO,CAAC4B,gBAAgB,IAAI,KAAK;EAExD,MAAM2C,mBAAmB,GAAGtF,sBAAsB,CAChDoC,UAAU,EACVK,eAAe,EACf/B,iBAAiB,EACjBgC,cACF,CAAC;EAED,MAAM6C,kBAAkB,GAAGvG,KAAK,CAACsC,UAAU,CAACpB,mBAAmB,CAAC;EAChE,MAAMsF,cAAc,GAAGpF,SAAS,GAC5Be,WAAW,GACXT,iBAAiB,KAAK,MAAM,IAAIS,WAAW,KAAK,KAAK;EAEzD,MAAMsE,kBAAkB,GACtBD,cAAc,KAAK,KAAK,GAAGF,mBAAmB,GAAGC,kBAAkB,IAAI,CAAC;;EAE1E;EACA;EACA;EACA,MAAMG,0BAA0B,GAAG1G,KAAK,CAACyC,MAAM,CAAC6D,mBAAmB,CAAC;EACpE,MAAMK,oBAAoB,GAAG3G,KAAK,CAACyC,MAAM,CACvC,IAAIxC,QAAQ,CAAC2G,KAAK,CAACH,kBAAkB,EAAE;IACrCI,eAAe,EAAE;EACnB,CAAC,CACH,CAAC,CAAClE,OAAO;EAET,MAAMN,MAAM,GAAGrC,KAAK,CAACsC,UAAU,CAAC9B,aAAa,CAAC;EAC9C,MAAM;IAAEsG;EAAK,CAAC,GAAGnG,QAAQ,CAAC,CAAC;EAE3B,MAAMoG,SAAS,GAAG/G,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EACpCzC,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB+B,WAAW,CAAC9B,OAAO,CAACX,KAAK,CAAC4C,GAAG,CAAC,GAAGmC,SAAS;IAC1C,OAAO,MAAM;MACX;MACA,OAAOtC,WAAW,CAAC9B,OAAO,CAACX,KAAK,CAAC4C,GAAG,CAAC;IACvC,CAAC;EACH,CAAC,CAAC;EAEF,oBACE5E,KAAA,CAAA4B,aAAA,CAACS,MAAM;IACLuC,GAAG,EAAE5C,KAAK,CAAC4C,GAAI;IACfoC,GAAG,EAAED,SAAU;IACflD,OAAO;IACPC,aAAa;IACbJ,cAAc,EAAEA,cAAe;IAC/BZ,KAAK,EAAE3C,UAAU,CAAC4D,YAAa;IAC/BiB,mBAAmB,EAAEA,mBAAoB;IACzCC,0BAA0B,EAAEA,0BAA2B;IACvDC,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAkB;IACrCC,8BAA8B,EAAEA,8BAA+B;IAC/Da,sBAAsB,EAAEA,sBAAuB;IAC/CD,YAAY,EAAEA,YAAa;IAC3BE,sBAAsB,EAAEA,sBAAuB;IAC/CpB,mBAAmB,EAAEA,mBAAoB;IACzCC,mBAAmB,EAAEA,mBAAoB;IACzCF,cAAc,EAAEzD,SAAS,GAAG,KAAK,GAAGyD,cAAe;IACnDsB,uBAAuB,EAAEA,uBAAwB;IACjDd,gCAAgC,EAAEA,gCAAiC;IACnEC,kBAAkB,EAAEA,kBAAmB;IACvCC,mBAAmB,EAAEA,mBAAoB;IACzCC,gBAAgB,EAAEA,gBAAiB;IACnCC,iBAAiB,EAAEA,iBAAkB;IACrCW,cAAc,EAAEA,cAAe;IAC/B1E,iBAAiB,EAAEA,iBAAkB;IACrCgE,kBAAkB,EAAEA,kBAAmB;IACvCC,cAAc,EAAEA,cAAe;IAC/BC,eAAe,EAAEA,eAAgB;IACjCC,cAAc,EAAEA,cAAc,KAAKiB,IAAI,GAAG,OAAO,GAAG,MAAM,CAAE;IAC5DtD,oBAAoB,EAAEA,oBAAqB;IAC3CsC,cAAc,EAAEA,cAAe;IAC/BC,kBAAkB,EAAEA,kBAAmB;IACvCkB,yBAAyB,EAAEA,CAAA,KAAM;MAC/B1C,UAAU,CAAC2C,QAAQ,CAAC;QAClB,GAAGxG,YAAY,CAACyG,GAAG,CAAC,CAAC;QACrBC,MAAM,EAAEpF,KAAK,CAAC4C,GAAG;QACjByC,MAAM,EAAE7C;MACV,CAAC,CAAC;IACJ,CAAE;IACF8C,YAAY,EAAEA,CAAA,KAAM;MAClB/C,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE;UAAEC,OAAO,EAAE;QAAM,CAAC;QACxBL,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;IACJ,CAAE;IACF+C,eAAe,EAAEA,CAAA,KAAM;MACrBpD,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC;QACvBL,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;IACJ,CAAE;IACFgD,QAAQ,EAAEA,CAAA,KAAM;MACdrD,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,QAAQ;QACdH,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;MACFL,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UAAEC,OAAO,EAAE;QAAM,CAAC;QACxBL,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;IACJ,CAAE;IACFiD,WAAW,EAAEA,CAAA,KAAM;MACjBtD,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC;QACvBL,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;IACJ,CAAE;IACFkD,oBAAoB,EAAEC,CAAC,IAAI;MACzB,MAAMnE,YAAY,GAAGmE,CAAC,CAACC,WAAW,CAACpE,YAAY;MAE/C,IAAI8C,0BAA0B,CAAC/D,OAAO,KAAKiB,YAAY,EAAE;QACvD;QACA;QACA;QACA;QACA+C,oBAAoB,CAACsB,QAAQ,CAACrE,YAAY,CAAC;QAC3C8C,0BAA0B,CAAC/D,OAAO,GAAGiB,YAAY;MACnD;IACF,CAAE;IACFsE,WAAW,EAAEH,CAAC,IAAI;MAChBxD,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,SAAS;QACfH,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;MAEF,MAAMuD,YAAY,GAChBJ,CAAC,CAACC,WAAW,CAACG,YAAY,GAAG,CAAC,GAAGJ,CAAC,CAACC,WAAW,CAACG,YAAY,GAAG,CAAC;MAEjE5D,UAAU,CAAC2C,QAAQ,CAAC;QAClB,GAAGxG,YAAY,CAACyG,GAAG,CAACgB,YAAY,CAAC;QACjCf,MAAM,EAAEpF,KAAK,CAAC4C,GAAG;QACjByC,MAAM,EAAE7C;MACV,CAAC,CAAC;IACJ,CAAE;IACF4D,eAAe,EAAEA,CAAA,KAAM;MACrB7D,UAAU,CAACgD,IAAI,CAAC;QACdC,IAAI,EAAE,eAAe;QACrBH,MAAM,EAAErF,KAAK,CAAC4C;MAChB,CAAC,CAAC;IACJ;EAAE,gBACF5E,KAAA,CAAA4B,aAAA,CAACT,2BAA2B,CAAC6C,QAAQ;IAACC,KAAK,EAAE0C;EAAqB,gBAChE3G,KAAA,CAAA4B,aAAA,CAACV,mBAAmB,CAAC8C,QAAQ;IAACC,KAAK,EAAEwC;EAAmB,gBACtDzG,KAAA,CAAA4B,aAAA,CAACC,gBAAgB;IACfE,OAAO,EAAEA,OAAQ;IACjBC,KAAK,EAAEA,KAAM;IACbN,iBAAiB,EAAEA;EAAkB,GACpCiD,WAAW,CAAC,CACG,CAAC,eAInB3E,KAAA,CAAA4B,aAAA,CAACd,YAAY,EAAAoD,QAAA,KACPnC,OAAO;IACXC,KAAK,EAAEA,KAAM;IACbG,WAAW,EAAEqE;EAAe,EAC7B,CAC2B,CACM,CAChC,CAAC;AAEb,CAAC;AAQD,SAAS6B,oBAAoBA,CAAAC,KAAA,EAIN;EAAA,IAJO;IAC5BC,KAAK;IACLhE,UAAU;IACVF;EACK,CAAC,GAAAiE,KAAA;EACN,MAAM;IAAE1D,GAAG;IAAE4D;EAAO,CAAC,GAAGD,KAAK;EAE7B,MAAME,eAAe,GAAGD,MAAM,CAACD,KAAK,CAACjE,KAAK,CAAC,CAACM,GAAG;EAC/C,MAAM;IAAE8D,aAAa;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC7DvE,WAAW,CAACoE,eAAe,CAAC,CAAC1G,OAAO;EACtC,MAAM8G,qBAAqB,GAAG7I,KAAK,CAACyC,MAAM,CAAwB;IAChEqG,sBAAsB,EAAEC,SAAS,IAAI;MACnC;IAAA;EAEJ,CAAC,CAAC;EAKF,MAAMtE,WAAW,GAAGzE,KAAK,CAACyC,MAAM,CAAY,CAAC,CAAC,CAAC;EAC/C,MAAMuG,qBAAqB,GAAGhJ,KAAK,CAACsC,UAAU,CAAC7B,SAAS,CAAC;EAEzDT,KAAK,CAAC0C,SAAS,CAAC,MAAM;IACpB,IACEsG,qBAAqB,CAACpG,IAAI,KAAK,WAAW,IAC1C8F,aAAa,KAAKrC,SAAS,EAC3B;MACA4C,OAAO,CAACC,IAAI,CACV,8IACF,CAAC;IACH;EACF,CAAC,EAAE,CAACF,qBAAqB,CAACpG,IAAI,EAAE8F,aAAa,CAAC,CAAC;EAE/C,oBACE1I,KAAA,CAAA4B,aAAA,CAACoH,qBAAqB;IACpBH,qBAAqB,EAAEA,qBAAsB;IAC7CH,aAAa,EAAEA,aAAc;IAC7BC,mBAAmB,EAAEA,mBAAoB;IACzCC,iBAAiB,EAAEA,iBAAiB,IAAI,KAAM;IAC9CnE,WAAW,EAAEA,WAAY;IACzBgE,eAAe,EAAEA;EAAgB,gBACjCzI,KAAA,CAAA4B,aAAA,CAACrB,WAAW;IACVuC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxB6F,qBAAqB,EAAEA;EAAsB,GAC5CL,MAAM,CAACW,GAAG,CAAC,CAACnH,KAAK,EAAEsC,KAAK,kBACvBtE,KAAA,CAAA4B,aAAA,CAACuC,SAAS;IACRS,GAAG,EAAE5C,KAAK,CAAC4C,GAAI;IACfP,WAAW,EAAEA,WAAY;IACzBrC,KAAK,EAAEA,KAAM;IACbsC,KAAK,EAAEA,KAAM;IACbC,UAAU,EAAEA,UAAW;IACvBC,QAAQ,EAAEI,GAAI;IACdH,WAAW,EAAEA;EAAY,CAC1B,CACF,CACU,CACQ,CAAC;AAE5B;AAEA,eAAe,SAAS2E,eAAeA,CAAC3H,KAAY,EAAE;EACpD,oBACEzB,KAAA,CAAA4B,aAAA,CAACb,sBAAsB,qBACrBf,KAAA,CAAA4B,aAAA,CAACyG,oBAAoB,EAAK5G,KAAQ,CACZ,CAAC;AAE7B;AAEA,MAAMsB,MAAM,GAAG5C,UAAU,CAACkJ,MAAM,CAAC;EAC/BrG,SAAS,EAAE;IACTsG,IAAI,EAAE;EACR;AACF,CAAC,CAAC"}