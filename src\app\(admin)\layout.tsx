
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AdminSidebar } from '@/components/admin/AdminSidebar'; 
import { AppHeader } from '@/components/layout/AppHeader'; // Re-use for mobile toggle if needed
import { ThemeProvider } from 'next-themes';
import { AuthProvider, useAuth } from '@/context/AuthContext';
import { checkAdminStatus } from '@/lib/adminAuth';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import PageTitle from '@/components/shared/PageTitle';

function AdminAreaLayout({ children }: { children: React.ReactNode }) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isAdmin, setIsAdmin] = useState(false);
  const [statusChecked, setStatusChecked] = useState(false);

  useEffect(() => {
    if (!authLoading) {
      if (!user) {
        router.replace(`/login?redirect=${window.location.pathname}`);
      } else {
        const adminStatus = checkAdminStatus(user);
        setIsAdmin(adminStatus);
        if (!adminStatus) {
          // router.replace('/home'); // Or a dedicated "access denied" page
        }
      }
      setStatusChecked(true);
    }
  }, [user, authLoading, router]);

  if (authLoading || !statusChecked) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background">
        <LoadingSpinner size={48} />
        <p className="mt-4 text-muted-foreground">Checking authentication status...</p>
      </div>
    );
  }

  if (!user) {
    // This state should ideally be caught by the redirect in useEffect
    // but can be a fallback or if redirect hasn't completed.
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background">
         <PageTitle title="Redirecting to Login" />
        <LoadingSpinner size={48} />
      </div>
    );
  }
  
  if (!isAdmin) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-background p-4 text-center">
        <PageTitle title="Access Denied" subtitle="You do not have permission to view this page."/>
        <p className="mb-4">This area is restricted to administrators only.</p>
        <Button onClick={() => router.push('/home')}>Go to Home</Button>
        <p className="mt-4 text-sm text-muted-foreground">
          (Admin check is currently a placeholder: <code className="bg-muted p-1 rounded"><EMAIL></code>)
        </p>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <AdminSidebar />
        <div className="flex flex-1 flex-col">
          {/* AppHeader can be used for the mobile sidebar trigger if the design matches */}
           <header className="sticky top-0 z-40 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6 md:hidden shadow-sm">
            <SidebarTrigger />
            <Link href="/admin/dashboard" className="flex items-center gap-2 text-lg font-semibold">
              <MosqueSilhouetteIcon className="h-6 w-6 text-primary" />
              <span>Admin Panel</span>
            </Link>
          </header>
          <main className="flex-1 p-4 sm:p-6 lg:p-8 bg-background overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}


// This outer layout ensures AuthProvider and ThemeProvider wrap the AdminAreaLayout
export default function AdminRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <AuthProvider>
        <AdminAreaLayout>{children}</AdminAreaLayout>
      </AuthProvider>
    </ThemeProvider>
  );
}
