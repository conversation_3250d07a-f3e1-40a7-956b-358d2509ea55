import * as React from "react"
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
  PanResponder,
  Animated,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  secondary: '#f1f5f9',
  background: '#ffffff',
  border: '#e2e8f0',
}

export interface SliderProps extends ViewProps {
  value?: number[]
  onValueChange?: (value: number[]) => void
  min?: number
  max?: number
  step?: number
  disabled?: boolean
  style?: ViewStyle
}

const Slider = React.forwardRef<View, SliderProps>(
  ({
    value = [0],
    onValueChange,
    min = 0,
    max = 100,
    step = 1,
    disabled = false,
    style,
    ...props
  }, ref) => {
    const [sliderWidth, setSliderWidth] = React.useState(0)
    const translateX = React.useRef(new Animated.Value(0)).current
    const currentValue = value[0] || 0

    React.useEffect(() => {
      if (sliderWidth > 0) {
        const percentage = (currentValue - min) / (max - min)
        const newTranslateX = percentage * (sliderWidth - 20) // 20 is thumb width
        Animated.timing(translateX, {
          toValue: newTranslateX,
          duration: 150,
          useNativeDriver: false,
        }).start()
      }
    }, [currentValue, min, max, sliderWidth, translateX])

    const updateValue = (newTranslateX: number) => {
      const percentage = newTranslateX / (sliderWidth - 20)
      const newValue = min + percentage * (max - min)
      const steppedValue = Math.round(newValue / step) * step
      const clampedValue = Math.max(min, Math.min(max, steppedValue))
      onValueChange?.([clampedValue])
    }

    const panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => !disabled,
      onMoveShouldSetPanResponder: () => !disabled,
      onPanResponderGrant: () => {
        translateX.setOffset(translateX._value)
        translateX.setValue(0)
      },
      onPanResponderMove: (_, gestureState) => {
        if (disabled) return
        const newValue = Math.max(0, Math.min(sliderWidth - 20, gestureState.dx))
        translateX.setValue(newValue)
      },
      onPanResponderRelease: (_, gestureState) => {
        if (disabled) return
        translateX.flattenOffset()
        const finalValue = Math.max(0, Math.min(sliderWidth - 20, translateX._value))
        updateValue(finalValue)
      },
    })

    const handleLayout = (event: any) => {
      setSliderWidth(event.nativeEvent.layout.width)
    }

    const rangeWidth = translateX.interpolate({
      inputRange: [0, sliderWidth - 20],
      outputRange: [10, sliderWidth - 10],
      extrapolate: 'clamp',
    })

    return (
      <View
        ref={ref}
        style={[styles.slider, style]}
        onLayout={handleLayout}
        {...props}
      >
        <View style={styles.track}>
          <Animated.View style={[styles.range, { width: rangeWidth }]} />
        </View>
        <Animated.View
          style={[
            styles.thumb,
            {
              transform: [{ translateX }],
            },
          ]}
          {...panResponder.panHandlers}
        />
      </View>
    )
  }
)

Slider.displayName = "Slider"

const styles = StyleSheet.create({
  slider: {
    height: 20,
    width: '100%',
    justifyContent: 'center',
    position: 'relative',
  },
  track: {
    height: 8,
    backgroundColor: colors.secondary,
    borderRadius: 4,
    position: 'absolute',
    width: '100%',
  },
  range: {
    height: 8,
    backgroundColor: colors.primary,
    borderRadius: 4,
    position: 'absolute',
  },
  thumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: colors.background,
    borderWidth: 2,
    borderColor: colors.primary,
    position: 'absolute',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
})

export { Slider }
