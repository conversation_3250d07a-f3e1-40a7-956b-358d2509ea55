"use client";

import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { BookOpen, ArrowRight } from 'lucide-react';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import React from 'react';

type SurahInfo = {
  id: string; // This will be used as the slug
  arabicName: string;
  englishName: string;
  urduName: string;
  romanUrduName: string;
  hindiName: string;
};

const surahs: SurahInfo[] = [
  { id: 'yaseen', arabicName: 'سورة يس', englishName: 'Surah Yaseen', urduName: 'سورہ یٰسین', romanUrduName: 'Surah Yaseen', hindiName: 'सूरह यासीन' },
  { id: 'al-mulk', arabicName: 'سورة الملك', englishName: 'Surah Al-<PERSON>lk', urduName: 'سورہ الملک', romanUrduName: 'Surah Al-Mulk', hindiName: 'सूरह अल-मुल्क' },
  { id: 'ar-rahman', arabicName: 'سورة الرحمن', englishName: 'Surah Ar-Rahman', urduName: 'سورہ الرحمٰن', romanUrduName: 'Surah Ar-Rahman', hindiName: 'सूरह अर-रहमान' },
  { id: 'al-waqiah', arabicName: 'سورة الواقعة', englishName: "Surah Al-Waqi'ah", urduName: 'سورہ الواقعہ', romanUrduName: "Surah Al-Waqi'ah", hindiName: 'सूरह अल-वाक़िआ' },
  { id: 'al-muzzammil', arabicName: 'سورة المزمل', englishName: 'Surah Al-Muzzammil', urduName: 'سورہ المزمل', romanUrduName: 'Surah Al-Muzzammil', hindiName: 'सूरह अल-मुज़्ज़म्मिल' },
];

const pageTranslations = {
  title: {
    en: "Panj Soorah (The Five Surahs)",
    ur: "پنج سورہ (پانچ سورتیں)",
    ro: "Panj Soorah (Paanch Surtein)",
    hi: "पंज सूरह (पांच सूरतें)",
    ar: "السور الخمس",
  },
  subtitle: {
    en: "Select a Surah to read its blessed verses.",
    ur: "تلاوت کے لیے سورہ منتخب کریں۔",
    ro: "Tilawat ke liye Surah muntakhab karein.",
    hi: "तिलावत के लिए सूरह चुनें।",
    ar: "اختر سورة لتلاوة آياتها المباركة.",
  },
  viewSurahButton: {
    en: "Read Surah",
    ur: "سورہ پڑھیں",
    ro: "Surah Parhein",
    hi: "सूरह पढ़ें",
    ar: "اقرأ السورة",
  }
};

export default function PanjsooraListPage() {
  const { language } = useLanguage();

  const currentTitle = pageTranslations.title[language] || pageTranslations.title.en;
  const currentSubtitle = pageTranslations.subtitle[language] || pageTranslations.subtitle.en;
  const currentViewSurahButton = pageTranslations.viewSurahButton[language] || pageTranslations.viewSurahButton.en;

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  const getSurahName = (surah: SurahInfo) => {
    switch (language) {
      case 'ur': return surah.urduName;
      case 'ro': return surah.romanUrduName;
      case 'hi': return surah.hindiName;
      case 'ar': return surah.arabicName;
      case 'en':
      default: return surah.englishName;
    }
  };

  return (
    <div className="space-y-8">
      <PageTitle
        title={currentTitle}
        subtitle={currentSubtitle}
        className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {surahs.map((surah) => (
          <Card key={surah.id} className="shadow-lg hover:shadow-xl transition-shadow bg-card/80 backdrop-blur-sm border-border/20 flex flex-col">
            <CardHeader className={cn("pb-3", textDir === 'rtl' && 'text-right')}>
              <h2 className={cn("text-xl md:text-2xl font-semibold flex items-center text-primary", fontClass, textDir === 'rtl' ? "justify-end flex-row-reverse" : "justify-start")}>
                <BookOpen className={cn("h-6 w-6", textDir === 'rtl' ? "ml-3" : "mr-3")} />
                {getSurahName(surah)}
                 { (language === 'ur' || language === 'ro' || language === 'hi' || language === 'en') && language !== 'ar' &&
                  <span lang="ar" dir="rtl" className={cn("font-arabic text-muted-foreground text-lg md:text-xl ml-2", textDir === 'rtl' && "mr-2 ml-0")}>({surah.arabicName})</span>
                 }
              </h2>
            </CardHeader>
            <CardContent className="flex-grow flex flex-col justify-end">
              <Link href={`/panjsoora/${surah.id}`} passHref>
                <Button className="w-full mt-auto">
                  {currentViewSurahButton} <ArrowRight className={cn("h-4 w-4", textDir === 'rtl' ? "mr-2 transform rotate-180" : "ml-2")} />
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}