import { NextResponse } from 'next/server';
import { generateReflectionPrompts, type GenerateReflectionPromptsInput } from '@/ai/flows/generate-reflection-prompts-flow';
import { z } from 'zod';

// Re-define schema here for request validation (or import if structured for sharing)
const GenerateReflectionPromptsRequestSchema = z.object({
  theme: z.string().optional(),
  sourceText: z.string().optional(),
}).refine(data => data.theme || data.sourceText, {
  message: "Either a theme or sourceText must be provided for generating reflection prompts.",
});


export async function POST(request: Request) {
  try {
    const body = await request.json();
    const validationResult = GenerateReflectionPromptsRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid input.', details: validationResult.error.flatten() }, { status: 400 });
    }

    const inputData: GenerateReflectionPromptsInput = { 
      theme: validationResult.data.theme,
      sourceText: validationResult.data.sourceText 
    };
    const result = await generateReflectionPrompts(inputData);
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating reflection prompts:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: 'Failed to generate reflection prompts.', details: errorMessage }, { status: 500 });
  }
}
