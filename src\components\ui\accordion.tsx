import * as React from "react"
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  TouchableOpacityProps,
  Animated,
} from "react-native"
import { Ionicons } from '@expo/vector-icons'

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  foreground: '#0f172a',
  border: '#e2e8f0',
  mutedForeground: '#64748b',
}

export interface AccordionProps extends ViewProps {
  type?: 'single' | 'multiple'
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
  children?: React.ReactNode
  style?: ViewStyle
}

export interface AccordionItemProps extends ViewProps {
  value: string
  children?: React.ReactNode
  style?: ViewStyle
}

export interface AccordionTriggerProps extends TouchableOpacityProps {
  children?: React.ReactNode
  style?: ViewStyle
  textStyle?: TextStyle
}

export interface AccordionContentProps extends ViewProps {
  children?: React.ReactNode
  style?: ViewStyle
}

const AccordionContext = React.createContext<{
  type: 'single' | 'multiple'
  value: string | string[]
  onValueChange: (value: string | string[]) => void
  openItems: Set<string>
}>({
  type: 'single',
  value: '',
  onValueChange: () => {},
  openItems: new Set(),
})

const AccordionItemContext = React.createContext<{
  value: string
  isOpen: boolean
  toggle: () => void
}>({
  value: '',
  isOpen: false,
  toggle: () => {},
})

const Accordion = React.forwardRef<View, AccordionProps>(
  ({ type = 'single', value, onValueChange, children, style, ...props }, ref) => {
    const [internalValue, setInternalValue] = React.useState<string | string[]>(
      type === 'single' ? '' : []
    )

    const currentValue = value !== undefined ? value : internalValue
    const handleValueChange = onValueChange || setInternalValue

    const openItems = React.useMemo(() => {
      if (type === 'single') {
        return new Set(currentValue ? [currentValue as string] : [])
      } else {
        return new Set(currentValue as string[])
      }
    }, [type, currentValue])

    return (
      <AccordionContext.Provider value={{
        type,
        value: currentValue,
        onValueChange: handleValueChange,
        openItems,
      }}>
        <View ref={ref} style={[style]} {...props}>
          {children}
        </View>
      </AccordionContext.Provider>
    )
  }
)

const AccordionItem = React.forwardRef<View, AccordionItemProps>(
  ({ value, children, style, ...props }, ref) => {
    const { type, value: accordionValue, onValueChange, openItems } = React.useContext(AccordionContext)
    const isOpen = openItems.has(value)

    const toggle = () => {
      if (type === 'single') {
        onValueChange(isOpen ? '' : value)
      } else {
        const currentArray = accordionValue as string[]
        if (isOpen) {
          onValueChange(currentArray.filter(item => item !== value))
        } else {
          onValueChange([...currentArray, value])
        }
      }
    }

    return (
      <AccordionItemContext.Provider value={{ value, isOpen, toggle }}>
        <View
          ref={ref}
          style={[styles.accordionItem, style]}
          {...props}
        >
          {children}
        </View>
      </AccordionItemContext.Provider>
    )
  }
)

const AccordionTrigger = React.forwardRef<TouchableOpacity, AccordionTriggerProps>(
  ({ children, style, textStyle, ...props }, ref) => {
    const { isOpen, toggle } = React.useContext(AccordionItemContext)
    const rotateAnim = React.useRef(new Animated.Value(isOpen ? 1 : 0)).current

    React.useEffect(() => {
      Animated.timing(rotateAnim, {
        toValue: isOpen ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }).start()
    }, [isOpen, rotateAnim])

    const rotateInterpolate = rotateAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '180deg'],
    })

    return (
      <TouchableOpacity
        ref={ref}
        style={[styles.accordionTrigger, style]}
        onPress={toggle}
        activeOpacity={0.7}
        {...props}
      >
        <Text style={[styles.accordionTriggerText, textStyle]}>
          {children}
        </Text>
        <Animated.View style={{ transform: [{ rotate: rotateInterpolate }] }}>
          <Ionicons
            name="chevron-down"
            size={16}
            color={colors.mutedForeground}
          />
        </Animated.View>
      </TouchableOpacity>
    )
  }
)

const AccordionContent = React.forwardRef<View, AccordionContentProps>(
  ({ children, style, ...props }, ref) => {
    const { isOpen } = React.useContext(AccordionItemContext)

    if (!isOpen) {
      return null
    }

    return (
      <View
        ref={ref}
        style={[styles.accordionContent, style]}
        {...props}
      >
        {children}
      </View>
    )
  }
)

Accordion.displayName = "Accordion"
AccordionItem.displayName = "AccordionItem"
AccordionTrigger.displayName = "AccordionTrigger"
AccordionContent.displayName = "AccordionContent"

const styles = StyleSheet.create({
  accordionItem: {
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  accordionTrigger: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 0,
  },
  accordionTriggerText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: colors.foreground,
  },
  accordionContent: {
    paddingBottom: 16,
    paddingTop: 0,
  },
})

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }
