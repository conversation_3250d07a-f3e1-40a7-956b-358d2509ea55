
import type {NextConfig} from 'next';
import withPWAInit from 'next-pwa';

const isDev = process.env.NODE_ENV === 'development';

const pwaConfig = {
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: isDev, // PWA features are disabled in development
  fallbacks: {
    document: '/_offline', // if you want to create a custom offline page
  }
};

// Base Next.js config
const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

// Conditionally apply withPWA only for non-development builds
const finalConfig = isDev ? nextConfig : withPWAInit(pwaConfig)(nextConfig);

export default finalConfig;
