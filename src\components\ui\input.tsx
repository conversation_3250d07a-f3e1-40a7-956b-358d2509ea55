
import * as React from "react"
import {
  TextInput,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  border: '#e2e8f0',
  primary: '#14b8a6',
  foreground: '#0f172a',
  mutedForeground: '#64748b',
}

export interface InputProps extends TextInputProps {
  style?: ViewStyle
  textStyle?: TextStyle
}

const Input = React.forwardRef<TextInput, InputProps>(
  ({ style, textStyle, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false)

    return (
      <TextInput
        ref={ref}
        style={[
          styles.input,
          isFocused && styles.inputFocused,
          props.editable === false && styles.inputDisabled,
          style,
          textStyle,
        ]}
        placeholderTextColor={colors.mutedForeground}
        onFocus={(e) => {
          setIsFocused(true)
          props.onFocus?.(e)
        }}
        onBlur={(e) => {
          setIsFocused(false)
          props.onBlur?.(e)
        }}
        {...props}
      />
    )
  }
)

Input.displayName = "Input"

const styles = StyleSheet.create({
  input: {
    height: 40,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 6,
    backgroundColor: colors.background,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: colors.foreground,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  inputFocused: {
    borderColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputDisabled: {
    opacity: 0.5,
    backgroundColor: '#f8fafc',
  },
})

export { Input }
    