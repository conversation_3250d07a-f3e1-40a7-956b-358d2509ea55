
import type { ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import MosqueSilhouetteIcon from '@/components/icons/MosqueSilhouetteIcon'; // Removed
import Image from 'next/image'; // Added
import Link from 'next/link';

type AuthFormCardProps = {
  title: string;
  description?: string;
  children: ReactNode;
  footerContent?: ReactNode;
};

export default function AuthFormCard({ title, description, children, footerContent }: AuthFormCardProps) {
  return (
    <Card className="w-full max-w-md shadow-xl">
      <CardHeader className="text-center">
        <Link href="/" className="inline-block mx-auto mb-4">
            <Image
              src="/app-icon.png" // Path to your new image
              alt="Sabiriya App Icon"
              width={48} // Adjust as needed (e.g., 48 for w-12)
              height={48} // Adjust as needed (e.g., 48 for h-12)
              data-ai-hint="app icon dome calligraphy"
            />
        </Link>
        <CardTitle className="text-2xl">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
      {footerContent && (
        <CardContent className="mt-2 text-center text-sm">
          {footerContent}
        </CardContent>
      )}
    </Card>
  );
}
