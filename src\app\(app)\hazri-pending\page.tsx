
"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import { Hourglass } from 'lucide-react';
import { motion } from 'framer-motion';

const translations = {
  pageStatusText: { // This text will now be displayed BELOW the card
    en: "Sending notification to the Khanqah...",
    ur: "خانقاہ میں خبر بھیجی جا رہی ہے۔۔۔",
    ro: "Khanqah mein khabar bheji ja rahi hai...",
    hi: "खानक़ाह में ख़बर भेजी जा रही है...",
    ar: "يتم إرسال الإشعار إلى الخانقاه...",
  },
  cardTitle: {
    en: "Please Wait",
    ur: "براہ کرم انتظار کریں",
    ro: "<PERSON><PERSON>-<PERSON>-<PERSON><PERSON> Inteza<PERSON>",
    hi: "बराए-करम इंतिज़ार करें",
    ar: "يرجى الانتظار",
  },
  message: {
    en: "Your request to mark your attendance is being sent to Hazrat at the Khanqah Shareef. As soon as Hazrat receives the request, he will mark your attendance, and you will be notified, Insha'Allah. Please wait a moment...",
    ur: "خانقاہ شریف میں حضرت کے پاس آپ کی حاضری لگانے کی درخواست کو بھیجا جا رہا ہے، جیسے ہی حضرت کو درخواست موصول ہوگی، (وہ) آپ کی حاضری لگا دیں گے اور آپ کو ان شاء الله اطلاع کر دی جائے گی۔ تھوڑا انتظار کریں۔",
    ro: "Khanqah Shareef mein Hazrat ke paas aap ki hazri lagane ki darkhaast ko bheja ja raha hai, jaise hi Hazrat ko darkhwast mausool hogi, (woh) aap ki hazri laga denge aur aap ko Insha'Allah ittela kar di jayegi. Thoda intezaar karein...",
    hi: "खानक़ाह शरीफ़ में हज़रत के पास आपकी हाज़िरी लगाने की दरख़्वास्त को भेजा जा रहा है, जैसे ही हज़रत को दरख़्वास्त मौसूल होगी, (वह) आपकी हाज़िरी लगा देंगे और आपको इंशा'अल्लाह इत्तिला कर दी जाएगी। थोड़ा इंतज़ार करें...",
    ar: "يتم إرسال طلب تسجيل حضوركم إلى حضرة الشيخ في الخانقاه الشريف. بمجرد استلام حضرة الشيخ للطلب، سيقوم بتسجيل حضوركم، وسيتم إبلاغكم بذلك إن شاء الله. يرجى الانتظار قليلاً...",
  },
  salutation: {
    en: "Assalamu Alaikum.",
    ur: "السلام علیکم۔",
    ro: "Assalamu Alaikum.",
    hi: "अस्सलामु अलैकुम।",
    ar: "السلام عليكم.",
  }
};

export default function HazriPendingPage() {
  const router = useRouter();
  const { language } = useLanguage();

  useEffect(() => {
    const minDelay = 10000; // 10 seconds
    const maxDelay = 20000; // 20 seconds
    const randomDelay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

    const timer = setTimeout(() => {
      router.replace('/hazri-confirmed');
    }, randomDelay); 

    return () => clearTimeout(timer);
  }, [router]);

  const currentSalutation = translations.salutation[language] || translations.salutation.en;
  const currentPageStatusText = translations.pageStatusText[language] || translations.pageStatusText.en;
  const currentCardTitleText = translations.cardTitle[language] || translations.cardTitle.en;
  const currentMessageText = translations.message[language] || translations.message.en;
  
  const isRtl = language === 'ar' || language === 'ur';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  return (
    <div className={cn("flex flex-col items-center justify-center min-h-[calc(100vh-10rem)] p-4 perspective-1000 transform-style-preserve-3d", fontClass)} lang={language} dir={isRtl ? "rtl" : "ltr"}>
      
      <p className={cn("text-lg md:text-xl text-foreground mb-2 text-center", fontClass)}>
        {currentSalutation}
      </p>
      
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95, rotateX: -10 }}
        animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="w-full max-w-md"
      >
        <Card className={cn("shadow-xl text-center glass-effect")}>
          <CardHeader className="items-center pb-4">
            <Hourglass className="h-16 w-16 text-primary mb-4 animate-spin_slow" />
            <CardTitle className={cn("text-2xl md:text-3xl font-semibold", fontClass)}>
              {currentCardTitleText}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className={cn("text-md md:text-lg text-foreground/90 leading-relaxed", fontClass)}>
              {currentMessageText}
            </p>
          </CardContent>
        </Card>
      </motion.div>

      <div className="text-center mt-6">
        <LoadingSpinner size={28} />
        <p className={cn("text-sm text-muted-foreground mt-2", fontClass)}>{currentPageStatusText}</p>
      </div>

      <style jsx global>{`
        @keyframes spin_slow {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
        .animate-spin_slow {
          animation: spin_slow 3s linear infinite;
        }
      `}</style>
    </div>
  );
}
