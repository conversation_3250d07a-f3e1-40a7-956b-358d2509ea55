{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_ScreenStackHeaderConfigNativeComponent", "_ScreenStackHeaderSubviewNativeComponent", "obj", "__esModule", "default", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "ScreenStackHeaderConfig", "exports", "ScreenStackHeaderConfigNativeComponent", "ScreenStackHeaderSubview", "ScreenStackHeaderSubviewNativeComponent", "ScreenStackHeaderBackButtonImage", "props", "createElement", "type", "style", "styles", "headerSubview", "Image", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderSearchBarView", "StyleSheet", "create", "position", "top", "right", "flexDirection", "alignItems", "justifyContent"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAMA,IAAAC,YAAA,GAAAD,OAAA;AAGA,IAAAE,uCAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,wCAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAAwG,SAAAD,uBAAAK,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAAA,SAAAG,SAAA,IAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,MAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,GAAA,IAAAD,MAAA,QAAAP,MAAA,CAAAS,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAC,GAAA,KAAAL,MAAA,CAAAK,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAL,MAAA,YAAAJ,QAAA,CAAAa,KAAA,OAAAP,SAAA,KAFxG;AAIO,MAAMQ,uBAA0E,GAAAC,OAAA,CAAAD,uBAAA,GACrFE,+CAA6C;AACxC,MAAMC,wBAEZ,GAAAF,OAAA,CAAAE,wBAAA,GAAGC,gDAA8C;AAE3C,MAAMC,gCAAgC,GAC3CC,KAAiB,iBAEjB7B,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAACJ,wBAAwB;EAACK,IAAI,EAAC,MAAM;EAACC,KAAK,EAAEC,MAAM,CAACC;AAAc,gBAChElC,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAAC3B,YAAA,CAAAgC,KAAK,EAAA1B,QAAA;EAAC2B,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKR,KAAK,CAAG,CAChC,CAC3B;AAACL,OAAA,CAAAI,gCAAA,GAAAA,gCAAA;AAEK,MAAMU,0BAA0B,GACrCT,KAAyC,iBAEzC7B,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAACJ,wBAAwB,EAAAjB,QAAA,KACnBoB,KAAK;EACTE,IAAI,EAAC,OAAO;EACZC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAACV,OAAA,CAAAc,0BAAA,GAAAA,0BAAA;AAEK,MAAMC,yBAAyB,GACpCV,KAAyC,iBAEzC7B,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAACJ,wBAAwB,EAAAjB,QAAA,KACnBoB,KAAK;EACTE,IAAI,EAAC,MAAM;EACXC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAACV,OAAA,CAAAe,yBAAA,GAAAA,yBAAA;AAEK,MAAMC,2BAA2B,GACtCX,KAAyC,iBAEzC7B,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAACJ,wBAAwB,EAAAjB,QAAA,KACnBoB,KAAK;EACTE,IAAI,EAAC,QAAQ;EACbC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAACV,OAAA,CAAAgB,2BAAA,GAAAA,2BAAA;AAEK,MAAMC,8BAA8B,GACzCZ,KAA8C,iBAE9C7B,MAAA,CAAAQ,OAAA,CAAAsB,aAAA,CAACJ,wBAAwB,EAAAjB,QAAA,KACnBoB,KAAK;EACTE,IAAI,EAAC,WAAW;EAChBC,KAAK,EAAEC,MAAM,CAACC;AAAc,EAC7B,CACF;AAACV,OAAA,CAAAiB,8BAAA,GAAAA,8BAAA;AAEF,MAAMR,MAAM,GAAGS,uBAAU,CAACC,MAAM,CAAC;EAC/BT,aAAa,EAAE;IACbU,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC"}