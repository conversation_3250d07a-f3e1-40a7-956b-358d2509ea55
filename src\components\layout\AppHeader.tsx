
import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Image,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from "../../context/AuthContext";
import { Button } from "../ui/button";
import { useLanguage, type Language } from "../../context/LanguageContext";

const donateButtonTranslations: Record<Language, string> = {
  en: "Donate",
  ur: "عطیہ دیں",
  ro: "Atiya Dein",
  hi: "अतिया दें",
  ar: "تبرع",
};

const appTitleTranslations: Record<Language, string> = {
  en: "Sabiriya",
  ur: "صابریہ",
  ro: "Sabiriya",
  hi: "साबिरिया",
  ar: "صابرية",
};

export function AppHeader() {
  const { user, loading } = useAuth();
  const { language } = useLanguage();

  const donateText = donateButtonTranslations[language] || donateButtonTranslations.en;
  const currentAppTitle = appTitleTranslations[language] || appTitleTranslations.en;

  const handleDonatePress = () => {
    Linking.openURL('https://sawadeazam.org/pe/');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.logoContainer}>
          <Image
            source={require('../../../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={[
            styles.appTitle,
            (language === 'ur' || language === 'ar') && styles.arabicText
          ]}>
            {currentAppTitle}
          </Text>
        </TouchableOpacity>

        <View style={styles.rightSection}>
          <TouchableOpacity style={styles.donateButton} onPress={handleDonatePress}>
            <Ionicons
              name="heart"
              size={16}
              color="#ffffff"
              style={styles.donateIcon}
            />
            <Text style={[
              styles.donateText,
              (language === 'ur' || language === 'ar') && styles.arabicText
            ]}>
              {donateText}
            </Text>
          </TouchableOpacity>

          {!loading && user ? (
            <TouchableOpacity style={styles.profileButton}>
              <Ionicons name="person-outline" size={20} color="#14b8a6" />
            </TouchableOpacity>
          ) : !loading && !user ? (
            <TouchableOpacity style={styles.signInButton}>
              <Ionicons name="log-in-outline" size={16} color="#14b8a6" />
              <Text style={styles.signInText}>Sign In</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    height: 64,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  logo: {
    width: 28,
    height: 28,
  },
  appTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#14b8a6',
  },
  arabicText: {
    fontFamily: 'System', // You can add custom Arabic font here
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  donateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#14b8a6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  donateIcon: {
    marginRight: 4,
  },
  donateText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  profileButton: {
    padding: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 6,
    backgroundColor: '#ffffff',
  },
  signInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 6,
    backgroundColor: '#ffffff',
    gap: 4,
  },
  signInText: {
    color: '#14b8a6',
    fontSize: 14,
    fontWeight: '500',
  },
});
