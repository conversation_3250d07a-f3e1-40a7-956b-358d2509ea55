import * as React from "react"
import {
  TouchableOpacity,
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacityProps,
} from "react-native"
import { Ionicons } from '@expo/vector-icons'

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  primaryForeground: '#ffffff',
  border: '#e2e8f0',
  background: '#ffffff',
}

export interface CheckboxProps extends Omit<TouchableOpacityProps, 'style'> {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  style?: ViewStyle
  disabled?: boolean
}

const Checkbox = React.forwardRef<TouchableOpacity, CheckboxProps>(
  ({ checked = false, onCheckedChange, style, disabled = false, ...props }, ref) => {
    const handlePress = () => {
      if (!disabled && onCheckedChange) {
        onCheckedChange(!checked)
      }
    }

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.checkbox,
          checked && styles.checkboxChecked,
          disabled && styles.checkboxDisabled,
          style,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.7}
        {...props}
      >
        {checked && (
          <Ionicons
            name="checkmark"
            size={12}
            color={colors.primaryForeground}
          />
        )}
      </TouchableOpacity>
    )
  }
)

Checkbox.displayName = "Checkbox"

const styles = StyleSheet.create({
  checkbox: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 3,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  checkboxDisabled: {
    opacity: 0.5,
  },
})

export { Checkbox }
