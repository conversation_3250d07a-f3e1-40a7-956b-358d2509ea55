
"use client";

import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

export type FontSize = 'sm' | 'base' | 'lg';

interface FontSizeContextType {
  fontSize: FontSize;
  setFontSize: (fontSize: FontSize) => void;
}

const FONT_SIZE_STORAGE_KEY = 'sabiriya-app-font-size';
const DEFAULT_FONT_SIZE: FontSize = 'base';

const FontSizeContext = createContext<FontSizeContextType | undefined>(undefined);

export const FontSizeProvider = ({ children }: { children: ReactNode }) => {
  const [fontSize, _setFontSize] = useState<FontSize>(DEFAULT_FONT_SIZE);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    if (typeof window !== 'undefined') {
      const storedFontSize = localStorage.getItem(FONT_SIZE_STORAGE_KEY) as FontSize | null;
      if (storedFontSize && ['sm', 'base', 'lg'].includes(storedFontSize)) {
        _setFontSize(storedFontSize);
        document.body.classList.remove('text-sm', 'text-base', 'text-lg');
        document.body.classList.add(`text-${storedFontSize}`);
      } else {
        document.body.classList.remove('text-sm', 'text-lg');
        document.body.classList.add(`text-${DEFAULT_FONT_SIZE}`);
      }
    }
  }, []);

  const setFontSize = useCallback((newSize: FontSize) => {
    _setFontSize(newSize);
    if (typeof window !== 'undefined') {
      localStorage.setItem(FONT_SIZE_STORAGE_KEY, newSize);
      document.body.classList.remove('text-sm', 'text-base', 'text-lg');
      document.body.classList.add(`text-${newSize}`);
    }
  }, []);
  
  // Apply initial font size class after mount to avoid hydration mismatch if localStorage differs from default
  useEffect(() => {
    if (isMounted && typeof window !== 'undefined') {
        const currentSize = localStorage.getItem(FONT_SIZE_STORAGE_KEY) as FontSize || DEFAULT_FONT_SIZE;
        document.body.classList.remove('text-sm', 'text-base', 'text-lg');
        document.body.classList.add(`text-${currentSize}`);
    }
  }, [isMounted]);


  if (!isMounted) {
    // Avoid rendering children until client-side hydration is complete and font size is determined
    return null;
  }

  return (
    <FontSizeContext.Provider value={{ fontSize, setFontSize }}>
      {children}
    </FontSizeContext.Provider>
  );
};

export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  if (context === undefined) {
    throw new Error('useFontSize must be used within a FontSizeProvider');
  }
  return context;
};
