
"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import PageTitle from '@/components/shared/PageTitle';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import { User, Heart, BellRing, LogOut, MessageSquare } from 'lucide-react'; // Added MessageSquare
import { Separator } from '@/components/ui/separator';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';
import Link from 'next/link'; // Added Link

type ProfilePageContent = {
  pageTitle: string;
  welcomeSubtitle: (name: string) => string;
  accountInfoTitle: string;
  emailLabel: string;
  userIdLabel: string;
  signOutButton: string;
  favoritesTitle: string;
  favoritesDescription: string;
  noFavoritesYet: string;
  remindersTitle: string;
  remindersDescription: string;
  remindersComingSoon: string;
  chatWithKhanqahButton: string; // Added
};

const translations: Record<Language, ProfilePageContent> = {
  en: {
    pageTitle: "My Profile",
    welcomeSubtitle: (name) => `Welcome, ${name}!`,
    accountInfoTitle: "Account Information",
    emailLabel: "Email:",
    userIdLabel: "User ID:",
    signOutButton: "Sign Out",
    favoritesTitle: "My Favorites",
    favoritesDescription: "Your saved Duas and Teachings.",
    noFavoritesYet: "You haven't favorited any items yet.",
    remindersTitle: "Manage Reminders",
    remindersDescription: "Set and manage your personalized spiritual reminders.",
    remindersComingSoon: "Reminder management feature is coming soon.",
    chatWithKhanqahButton: "Chat with Khanqah",
  },
  ur: {
    pageTitle: "میرا پروفائل",
    welcomeSubtitle: (name) => `خوش آمدید، ${name}!`,
    accountInfoTitle: "اکاؤنٹ کی معلومات",
    emailLabel: "ای میل:",
    userIdLabel: "صارف آئی ڈی:",
    signOutButton: "لاگ آؤٹ",
    favoritesTitle: "میری پسندیدہ",
    favoritesDescription: "آپ کی محفوظ کردہ دعائیں اور تعلیمات۔",
    noFavoritesYet: "آپ نے ابھی تک کوئی آئٹم پسند نہیں کیا۔",
    remindersTitle: "یاد دہانیاں منظم کریں",
    remindersDescription: "اپنی ذاتی روحانی یاد دہانیاں سیٹ اور منظم کریں۔",
    remindersComingSoon: "یاد دہانی کے انتظام کا فیچر جلد آرہا ہے۔",
    chatWithKhanqahButton: "خانقاہ سے بات کریں",
  },
  ro: {
    pageTitle: "My Profile",
    welcomeSubtitle: (name) => `Khush Amdeed, ${name}!`,
    accountInfoTitle: "Account ki Malumaat",
    emailLabel: "Email:",
    userIdLabel: "User ID:",
    signOutButton: "Sign Out",
    favoritesTitle: "Meri Pasandیدہ",
    favoritesDescription: "Aap ki mehfooz karda Duaein aur Ta'leemaat.",
    noFavoritesYet: "Aap ne abhi tak koi item pasand nahin kiya.",
    remindersTitle: "Yaad Dehaniyan Munazzam Karein",
    remindersDescription: "Apni zaati roohaani yaad dehaniyan set aur munazzam karein.",
    remindersComingSoon: "Yaad dehani ke intezaam ka feature jald aa raha hai.",
    chatWithKhanqahButton: "Khanqah se Baat Karein",
  },
  hi: {
    pageTitle: "मेरा प्रोफ़ाइल",
    welcomeSubtitle: (name) => `ख़ुश आमदीद, ${name}!`,
    accountInfoTitle: "अकाउंट की जानकारी",
    emailLabel: "ईमेल:",
    userIdLabel: "यूज़र आईडी:",
    signOutButton: "साइन आउट",
    favoritesTitle: "मेरी पसंदीदा",
    favoritesDescription: "आपकी सहेजी गई दुआएँ और शिक्षाएँ।",
    noFavoritesYet: "आपने अभी तक कोई आइटम पसंदीदा नहीं किया है।",
    remindersTitle: "रिमाइंडर प्रबंधित करें",
    remindersDescription: "अपने व्यक्तिगत आध्यात्मिक रिमाइंडर सेट और प्रबंधित करें।",
    remindersComingSoon: "रिमाइंडर प्रबंधन सुविधा जल्द ही आ रही है।",
    chatWithKhanqahButton: "खानक़ाह से बात करें",
  },
  ar: {
    pageTitle: "ملفي الشخصي",
    welcomeSubtitle: (name) => `مرحباً بك، ${name}!`,
    accountInfoTitle: "معلومات الحساب",
    emailLabel: "البريد الإلكتروني:",
    userIdLabel: "معرف المستخدم:",
    signOutButton: "تسجيل الخروج",
    favoritesTitle: "مفضلتي",
    favoritesDescription: "أدعيتك وتعاليمك المحفوظة.",
    noFavoritesYet: "لم تقم بتفضيل أي عناصر حتى الآن.",
    remindersTitle: "إدارة التذكيرات",
    remindersDescription: "قم بتعيين وإدارة تذكيراتك الروحية المخصصة.",
    remindersComingSoon: "ميزة إدارة التذكيرات ستتوفر قريبًا.",
    chatWithKhanqahButton: "تحدث مع الخانقاه",
  }
};


export default function UserProfilePage() {
  const { user, loading, signOutUser } = useAuth();
  const router = useRouter();
  const { language } = useLanguage();
  const content = translations[language] || translations.en;

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/login');
    }
  }, [user, loading, router]);

  if (loading || !user) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-theme(spacing.32))]">
        <LoadingSpinner size={48} />
      </div>
    );
  }

  const handleSignOut = async () => {
    await signOutUser();
  };
  
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';

  return (
    <div className={cn("space-y-8 max-w-3xl mx-auto", fontClass)} lang={language} dir={textDir}>
      <PageTitle title={content.pageTitle} subtitle={content.welcomeSubtitle(user.displayName || user.email || "User")} className={cn(textDir === 'rtl' ? 'text-right' : 'text-left')}/>

      <Card className="shadow-lg">
        <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
          <h2 className={cn("text-xl font-semibold flex items-center", textDir === 'rtl' ? 'flex-row-reverse justify-end' : '')}><User className={cn("h-6 w-6 text-primary", textDir === 'rtl' ? 'ml-2' : 'mr-2')} /> {content.accountInfoTitle}</h2>
        </CardHeader>
        <CardContent className={cn("space-y-3", textDir === 'rtl' && 'text-right')}>
          <p><strong className="text-foreground/80">{content.emailLabel}</strong> {user.email}</p>
          <p><strong className="text-foreground/80">{content.userIdLabel}</strong> {user.uid}</p>
          <Button variant="outline" onClick={handleSignOut} className="w-full sm:w-auto mt-4">
            <LogOut className={cn("h-4 w-4", textDir === 'rtl' ? 'ml-2' : 'mr-2')} />
            {content.signOutButton}
          </Button>
        </CardContent>
      </Card>

      <Separator />
        <Link href="/chat/khanqah" passHref>
          <Button variant="default" className={cn("w-full py-6 text-lg", fontClass)}>
             <MessageSquare className={cn("h-5 w-5", textDir === 'rtl' ? 'ml-2' : 'mr-2')} />
            {content.chatWithKhanqahButton}
          </Button>
        </Link>
      <Separator />

      <Card className="shadow-lg">
        <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
          <h2 className={cn("text-xl font-semibold flex items-center", textDir === 'rtl' ? 'flex-row-reverse justify-end' : '')}><Heart className={cn("h-6 w-6 text-primary", textDir === 'rtl' ? 'ml-2' : 'mr-2')} /> {content.favoritesTitle}</h2>
          <CardDescription>{content.favoritesDescription}</CardDescription>
        </CardHeader>
        <CardContent className={cn(textDir === 'rtl' && 'text-right')}>
          <p className="text-muted-foreground">{content.noFavoritesYet}</p>
        </CardContent>
      </Card>

      <Separator />

      <Card className="shadow-lg">
        <CardHeader className={cn(textDir === 'rtl' && 'text-right')}>
          <h2 className={cn("text-xl font-semibold flex items-center", textDir === 'rtl' ? 'flex-row-reverse justify-end' : '')}><BellRing className={cn("h-6 w-6 text-primary", textDir === 'rtl' ? 'ml-2' : 'mr-2')} /> {content.remindersTitle}</h2>
          <CardDescription>{content.remindersDescription}</CardDescription>
        </CardHeader>
        <CardContent className={cn(textDir === 'rtl' && 'text-right')}>
          <p className="text-muted-foreground">{content.remindersComingSoon}</p>
        </CardContent>
      </Card>
    </div>
  );
}

    