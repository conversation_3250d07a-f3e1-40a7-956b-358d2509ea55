import * as React from "react"
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  ViewProps,
  TouchableOpacityProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  border: '#e2e8f0',
  background: '#ffffff',
}

export interface RadioGroupProps extends ViewProps {
  value?: string
  onValueChange?: (value: string) => void
  children?: React.ReactNode
  style?: ViewStyle
}

export interface RadioGroupItemProps extends Omit<TouchableOpacityProps, 'style'> {
  value: string
  style?: ViewStyle
  disabled?: boolean
}

const RadioGroupContext = React.createContext<{
  value?: string
  onValueChange?: (value: string) => void
}>({})

const RadioGroup = React.forwardRef<View, RadioGroupProps>(
  ({ value, onValueChange, children, style, ...props }, ref) => {
    return (
      <RadioGroupContext.Provider value={{ value, onValueChange }}>
        <View ref={ref} style={[styles.radioGroup, style]} {...props}>
          {children}
        </View>
      </RadioGroupContext.Provider>
    )
  }
)

const RadioGroupItem = React.forwardRef<TouchableOpacity, RadioGroupItemProps>(
  ({ value, style, disabled = false, ...props }, ref) => {
    const { value: selectedValue, onValueChange } = React.useContext(RadioGroupContext)
    const isSelected = selectedValue === value

    const handlePress = () => {
      if (!disabled && onValueChange) {
        onValueChange(value)
      }
    }

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          styles.radioItem,
          isSelected && styles.radioItemSelected,
          disabled && styles.radioItemDisabled,
          style,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.7}
        {...props}
      >
        {isSelected && <View style={styles.radioIndicator} />}
      </TouchableOpacity>
    )
  }
)

RadioGroup.displayName = "RadioGroup"
RadioGroupItem.displayName = "RadioGroupItem"

const styles = StyleSheet.create({
  radioGroup: {
    gap: 8,
  },
  radioItem: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioItemSelected: {
    borderColor: colors.primary,
  },
  radioItemDisabled: {
    opacity: 0.5,
  },
  radioIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
})

export { RadioGroup, RadioGroupItem }
