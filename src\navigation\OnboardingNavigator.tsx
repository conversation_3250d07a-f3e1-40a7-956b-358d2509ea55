import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Import onboarding screens (we'll create these)
import OnboardingScreen from '@/screens/onboarding/OnboardingScreen';

export type OnboardingStackParamList = {
  Welcome: undefined;
};

const Stack = createNativeStackNavigator<OnboardingStackParamList>();

export default function OnboardingNavigator() {
  return (
    <Stack.Navigator 
      screenOptions={{ 
        headerShown: false,
        animation: 'slide_from_right'
      }}
    >
      <Stack.Screen name="Welcome" component={OnboardingScreen} />
    </Stack.Navigator>
  );
}
