
"use client"; // Mark as client component for auth and state

import { teachingsData, teachingCategories, type Teaching } from '@/data/teachings';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card'; // Removed CardTitle
import Link from 'next/link';
import { ArrowRight, Heart } from 'lucide-react';
// import { notFound } from 'next/navigation'; // notFound can only be used in Server Components
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthContext'; // For potential favorite functionality
import { Button } from '@/components/ui/button';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import PageTitle from '@/components/shared/PageTitle';
import { motion } from 'framer-motion';

// Simulate fetching teaching details by slug
const getTeachingDetails = async (categorySlug: string, teachingSlug: string): Promise<Teaching | null> => {
  const category = teachingCategories.find(c => c.slug === categorySlug);
  if (!category) {
    return null;
  }
  const teaching = teachingsData.find(t => t.slug === teachingSlug && t.categoryId === category.id);
  return teaching || null;
};

export default function TeachingDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { categorySlug, teachingSlug } = params;
  
  const [teaching, setTeaching] = useState<Teaching | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false); // Placeholder for favorite state

  const { user } = useAuth(); // Get user for favorite functionality

  useEffect(() => {
    if (typeof categorySlug === 'string' && typeof teachingSlug === 'string') {
      const fetchTeaching = async () => {
        setLoading(true);
        const fetchedTeaching = await getTeachingDetails(categorySlug, teachingSlug);
        if (!fetchedTeaching) {
          console.error("Teaching not found");
          router.replace(`/teachings/${categorySlug}`); // Or a proper 404 page
        }
        setTeaching(fetchedTeaching);
        setLoading(false);
        // Fetch favorite status: checkFirestoreFavorite(user?.uid, fetchedTeaching?.id).then(setIsFavorite);
      };
      fetchTeaching();
    } else {
      setLoading(false);
      router.replace('/teachings'); // Invalid slug
    }
  }, [categorySlug, teachingSlug, router, user]);

  if (loading) {
    return <div className="flex justify-center items-center min-h-[200px]"><LoadingSpinner size={32}/></div>;
  }

  if (!teaching) {
    return <div className="text-center py-10">Teaching not found. <Link href="/teachings" className="text-primary hover:underline">Return to Teachings</Link></div>;
  }

  const category = teachingCategories.find(c => c.id === teaching.categoryId);

  const advice_title_urdu = teaching.title;
  const advice_explanation_urdu = teaching.content;

  const toggleFavorite = async () => {
    if (!user) {
      router.push(`/login?redirect=/teachings/${categorySlug}/${teachingSlug}`);
      return;
    }
    console.log("Toggling favorite for user:", user.uid, "teaching:", teaching.id);
    setIsFavorite(!isFavorite);
    // await updateFavoriteInFirestore(user.uid, teaching.id, !isFavorite);
  };

  return (
    <div className="space-y-6">
      <div className="px-0"> {/* Replaced CardHeader with div for PageTitle */}
        <div className="flex justify-between items-start">
          <div>
            {category && (
              <Link href={`/teachings/${category.slug}`} className="text-sm text-primary hover:underline mb-2 inline-flex items-center">
                <ArrowRight className="h-4 w-4 mr-1 transform rotate-180" /> Back to {category.name}
              </Link>
            )}
            <PageTitle title={advice_title_urdu} className="mb-0" />
            {teaching.shortDescription && (
              <CardDescription className="text-lg text-muted-foreground pt-1">{teaching.shortDescription}</CardDescription>
            )}
          </div>
          {user && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleFavorite} 
              aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"} 
              className="mt-2 hover:shadow-[0_0_15px_hsl(var(--primary))]"
            >
              <motion.div
                initial={false}
                animate={{ scale: isFavorite ? 1.2 : 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 10 }}
              >
                <Heart 
                  className={`h-6 w-6 transition-colors duration-300 ${isFavorite ? "fill-red-500 text-red-500" : "text-muted-foreground"}`} 
                />
              </motion.div>
            </Button>
          )}
        </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <h2 className="text-xl font-semibold sr-only">Teaching Content</h2> {/* Added sr-only title for Card section */}
        </CardHeader>
        <CardContent className="p-6 pt-0">
          <article className="prose dark:prose-invert max-w-none text-foreground" dangerouslySetInnerHTML={{ __html: advice_explanation_urdu }} />
        </CardContent>
      </Card>
    </div>
  );
}
