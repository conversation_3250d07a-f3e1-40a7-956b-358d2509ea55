
import type {Metadata} from 'next';
import Script from 'next/script'; // Import Script component
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { Inter, Poppins } from 'next/font/google';
import { cn } from '@/lib/utils';
import { ThemeProvider } from "next-themes"; 
import { LanguageProvider } from '@/context/LanguageContext'; 
import { FontSizeProvider } from '@/context/FontSizeContext'; 

// Temporarily commented out local font loading due to file not found errors
// Ensure font files are correctly placed in src/assets/fonts/ and then uncomment
/*
import localFont from 'next/font/local';

const alMajidQuranicFont = localFont({
  src: '../assets/fonts/AlMajidQuranic.ttf',
  display: 'swap',
  variable: '--font-al-majid',
  weight: '400',
});

const jameelNooriNastaleeq = localFont({
  src: '../assets/fonts/JameelNooriNastaleeq.ttf',
  display: 'swap',
  variable: '--font-jameel-noori',
  weight: '400', // Adjust if your font file has different weights
});
*/

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'], // Include necessary weights
  variable: '--font-poppins',
});


export const metadata: Metadata = {
  title: 'Sabiriya: Spiritual Guide',
  description: 'Discover peace and spiritual guidance with the teachings of the Chishti Sabiriya Silsila.',
  manifest: '/manifest.json',
  themeColor: '#14b8a6', // Matches Serene Teal primary for light mode
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const gaTrackingId = process.env.NEXT_PUBLIC_GA_TRACKING_ID;

  return (
    <html lang="en" suppressHydrationWarning className={cn(inter.variable, poppins.variable /*, alMajidQuranicFont.variable, jameelNooriNastaleeq.variable */)}>
      <body>
        <ThemeProvider 
          attribute="theme" 
          defaultTheme="light-teal" 
          enableSystem
        >
          <LanguageProvider>
            <FontSizeProvider> 
              {gaTrackingId && (
                <>
                  <Script
                    strategy="afterInteractive"
                    src={`https://www.googletagmanager.com/gtag/js?id=${gaTrackingId}`}
                  />
                  <Script
                    id="google-analytics"
                    strategy="afterInteractive"
                    dangerouslySetInnerHTML={{
                      __html: `
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag('js', new Date());
                        gtag('config', '${gaTrackingId}', {
                          page_path: window.location.pathname,
                        });
                      `,
                    }}
                  />
                </>
              )}
              {children}
              <Toaster />
            </FontSizeProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
