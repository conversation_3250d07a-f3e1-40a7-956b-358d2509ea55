
// public/firebase-messaging-sw.js

// IMPORTANT:
// 1. THIS FILE MUST BE IN THE 'public' FOLDER OF YOUR NEXT.JS PROJECT.
// 2. YOU MUST MANUALLY REPLACE THE PLACEHOLDER `firebaseConfig` VALUES BELOW
//    WITH YOUR ACTUAL FIREBASE PROJECT'S WEB APP CONFIGURATION.
//    THESE ARE NOT AUTOMATICALLY POPULATED BY ENVIRONMENT VARIABLES HERE.

// Import the Firebase app and messaging services
// Use newer SDK versions compatible with your client-side Firebase (v11.x)
// Check Firebase CDN for latest versions: https://firebase.google.com/docs/web/setup#available-libraries
importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.12.2/firebase-messaging-compat.js');

console.log('[firebase-messaging-sw.js] Service Worker starting...');

// --- START: REPLACE WITH YOUR FIREBASE CONFIG ---
const firebaseConfig = {
  apiKey: "YOUR_API_KEY_HERE", // From Firebase console
  authDomain: "YOUR_AUTH_DOMAIN_HERE", // e.g., your-project-id.firebaseapp.com
  projectId: "YOUR_PROJECT_ID_HERE",
  storageBucket: "YOUR_STORAGE_BUCKET_HERE", // e.g., your-project-id.appspot.com
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID_HERE",
  appId: "YOUR_APP_ID_HERE",
  // measurementId: "YOUR_MEASUREMENT_ID_HERE" // Optional, if you use Analytics
};
// --- END: REPLACE WITH YOUR FIREBASE CONFIG ---

console.log('[firebase-messaging-sw.js] Firebase Config (SHOULD BE FILLED IN):', firebaseConfig.apiKey === "YOUR_API_KEY_HERE" ? "CONFIG IS STILL PLACEHOLDER!" : "Config looks present (check values).");

// Initialize the Firebase app in the service worker
// Ensure this is done only if a config is provided (though it should be)
if (firebaseConfig.apiKey && firebaseConfig.apiKey !== "YOUR_API_KEY_HERE") {
  try {
    firebase.initializeApp(firebaseConfig);
    console.log('[firebase-messaging-sw.js] Firebase App initialized in Service Worker.');

    // Retrieve an instance of Firebase Messaging so that it can handle background messages.
    const messaging = firebase.messaging();
    console.log('[firebase-messaging-sw.js] Firebase Messaging instance retrieved.');

    messaging.onBackgroundMessage((payload) => {
      console.log('[firebase-messaging-sw.js] Received background message: ', payload);

      // Customize notification here
      const notificationTitle = payload.notification?.title || 'New Message';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new notification.',
        icon: payload.notification?.icon || '/icons/icon-192x192.png', // Ensure you have this icon
        // You can add more options like badge, actions, etc.
      };

      // The `self.registration` comes from the ServiceWorkerGlobalScope
      if (self.registration) {
        return self.registration.showNotification(notificationTitle, notificationOptions);
      } else {
        console.error('[firebase-messaging-sw.js] self.registration is not available to show notification.');
      }
    });
    console.log('[firebase-messaging-sw.js] Background Message Handler Set.');

  } catch (e) {
    console.error('[firebase-messaging-sw.js] Error initializing Firebase in Service Worker:', e);
  }
} else {
  console.error('[firebase-messaging-sw.js] Firebase configuration is missing or is still the placeholder. Push notifications will not work.');
}

// Optional: Listen to other service worker events like install, activate
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service Worker installed.');
});

self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service Worker activated.');
  // If using clients.claim()
  // event.waitUntil(self.clients.claim());
});
