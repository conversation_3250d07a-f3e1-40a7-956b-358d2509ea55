{"version": 3, "names": ["React", "Freeze", "DelayedFreeze", "_ref", "freeze", "children", "freezeState", "setFreezeState", "useState", "useEffect", "id", "setImmediate", "clearImmediate", "createElement"], "sourceRoot": "../../../../src", "sources": ["components/helpers/DelayedFreeze.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,cAAc;AAOrC;AACA;AACA,SAASC,aAAaA,CAAAC,IAAA,EAA2C;EAAA,IAA1C;IAAEC,MAAM;IAAEC;EAA6B,CAAC,GAAAF,IAAA;EAC7D;EACA,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAAC,KAAK,CAAC;EAE3DR,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,MAAMC,EAAE,GAAGC,YAAY,CAAC,MAAM;MAC5BJ,cAAc,CAACH,MAAM,CAAC;IACxB,CAAC,CAAC;IACF,OAAO,MAAM;MACXQ,cAAc,CAACF,EAAE,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EAEZ,oBAAOJ,KAAA,CAAAa,aAAA,CAACZ,MAAM;IAACG,MAAM,EAAEA,MAAM,GAAGE,WAAW,GAAG;EAAM,GAAED,QAAiB,CAAC;AAC1E;AAEA,eAAeH,aAAa"}