import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, SafeAreaView } from 'react-native';

// Import the converted UI components
import { <PERSON><PERSON> } from '../components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { Switch } from '../components/ui/switch';
import { Label } from '../components/ui/label';
import { Checkbox } from '../components/ui/checkbox';
import { Textarea } from '../components/ui/textarea';
import { Progress } from '../components/ui/progress';
import { Skeleton } from '../components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../components/ui/tabs';
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from '../components/ui/alert';
import { Avatar, AvatarImage, AvatarFallback } from '../components/ui/avatar';

export default function UITestScreen() {
  const [inputValue, setInputValue] = useState('');
  const [switchValue, setSwitchValue] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [textareaValue, setTextareaValue] = useState('');
  const [progressValue, setProgressValue] = useState(65);
  const [tabValue, setTabValue] = useState('tab1');

  const handleButtonPress = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        
        {/* Header */}
        <Card style={styles.headerCard}>
          <CardHeader>
            <CardTitle>UI Components Test</CardTitle>
            <CardDescription>
              Testing the converted React Native UI components from Next.js
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Button Variants */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Button Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonGrid}>
              <Button onPress={handleButtonPress} loading={loading}>
                Default Button
              </Button>
              <Button variant="secondary" onPress={handleButtonPress}>
                Secondary
              </Button>
              <Button variant="outline" onPress={handleButtonPress}>
                Outline
              </Button>
              <Button variant="destructive" onPress={handleButtonPress}>
                Destructive
              </Button>
              <Button variant="ghost" onPress={handleButtonPress}>
                Ghost
              </Button>
              <Button variant="link" onPress={handleButtonPress}>
                Link Button
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Button Sizes */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Button Sizes</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonGrid}>
              <Button size="sm" onPress={handleButtonPress}>
                Small
              </Button>
              <Button size="default" onPress={handleButtonPress}>
                Default
              </Button>
              <Button size="lg" onPress={handleButtonPress}>
                Large
              </Button>
              <Button size="icon" onPress={handleButtonPress}>
                ⚙️
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Input Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Input Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              placeholder="Enter some text..."
              value={inputValue}
              onChangeText={setInputValue}
              style={styles.input}
            />
            <Input
              placeholder="Disabled input"
              editable={false}
              style={styles.input}
            />
          </CardContent>
        </Card>

        {/* Badge Variants */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Badge Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.badgeGrid}>
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="outline">Outline</Badge>
            </View>
          </CardContent>
        </Card>

        {/* Switch Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Switch Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.switchContainer}>
              <Switch
                value={switchValue}
                onValueChange={setSwitchValue}
              />
            </View>
          </CardContent>
        </Card>

        {/* Separator */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Separator Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.separatorDemo}>
              <Button variant="outline">Above</Button>
              <Separator style={styles.separator} />
              <Button variant="outline">Below</Button>
            </View>
          </CardContent>
        </Card>

        {/* Label and Checkbox */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Label & Checkbox</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.checkboxContainer}>
              <Checkbox
                checked={checkboxValue}
                onCheckedChange={setCheckboxValue}
              />
              <Label style={styles.checkboxLabel}>
                Accept terms and conditions
              </Label>
            </View>
          </CardContent>
        </Card>

        {/* Textarea Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Textarea Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Label>Message</Label>
            <Textarea
              placeholder="Enter your message here..."
              value={textareaValue}
              onChangeText={setTextareaValue}
              style={styles.textarea}
            />
          </CardContent>
        </Card>

        {/* Progress Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Progress Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Progress value={progressValue} style={styles.progress} />
            <View style={styles.progressButtons}>
              <Button
                variant="outline"
                size="sm"
                onPress={() => setProgressValue(Math.max(0, progressValue - 10))}
              >
                -10%
              </Button>
              <Button
                variant="outline"
                size="sm"
                onPress={() => setProgressValue(Math.min(100, progressValue + 10))}
              >
                +10%
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Skeleton Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Skeleton Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.skeletonDemo}>
              <Skeleton style={styles.skeletonLine} />
              <Skeleton style={styles.skeletonLine} />
              <Skeleton style={[styles.skeletonLine, { width: '60%' }]} />
            </View>
          </CardContent>
        </Card>

        {/* Tabs Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Tabs Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={tabValue} onValueChange={setTabValue}>
              <TabsList>
                <TabsTrigger value="tab1">Tab 1</TabsTrigger>
                <TabsTrigger value="tab2">Tab 2</TabsTrigger>
                <TabsTrigger value="tab3">Tab 3</TabsTrigger>
              </TabsList>
              <TabsContent value="tab1">
                <Label>Content for Tab 1</Label>
              </TabsContent>
              <TabsContent value="tab2">
                <Label>Content for Tab 2</Label>
              </TabsContent>
              <TabsContent value="tab3">
                <Label>Content for Tab 3</Label>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Alert Components */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Alert Components</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert style={styles.alert}>
              <AlertTitle>Default Alert</AlertTitle>
              <AlertDescription>
                This is a default alert with some information.
              </AlertDescription>
            </Alert>
            <Alert variant="destructive" style={styles.alert}>
              <AlertTitle>Destructive Alert</AlertTitle>
              <AlertDescription>
                This is a destructive alert indicating an error.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Avatar Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Avatar Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.avatarDemo}>
              <Avatar size={40}>
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <Avatar size={50}>
                <AvatarFallback>AB</AvatarFallback>
              </Avatar>
              <Avatar size={60}>
                <AvatarFallback>XY</AvatarFallback>
              </Avatar>
            </View>
          </CardContent>
        </Card>

        {/* Card with Footer */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Card with Footer</CardTitle>
            <CardDescription>
              This card demonstrates the footer component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Input
              placeholder="Card content input"
              style={styles.input}
            />
          </CardContent>
          <CardFooter>
            <Button variant="outline" style={styles.footerButton}>
              Cancel
            </Button>
            <Button style={styles.footerButton}>
              Save
            </Button>
          </CardFooter>
        </Card>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
  },
  buttonGrid: {
    gap: 12,
  },
  badgeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  input: {
    marginBottom: 12,
  },
  switchContainer: {
    alignItems: 'flex-start',
  },
  separatorDemo: {
    gap: 16,
  },
  separator: {
    marginVertical: 8,
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  checkboxLabel: {
    marginBottom: 0,
  },
  textarea: {
    marginTop: 8,
  },
  progress: {
    marginBottom: 16,
  },
  progressButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  skeletonDemo: {
    gap: 8,
  },
  skeletonLine: {
    height: 16,
    width: '100%',
  },
  alert: {
    marginBottom: 12,
  },
  avatarDemo: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
});
