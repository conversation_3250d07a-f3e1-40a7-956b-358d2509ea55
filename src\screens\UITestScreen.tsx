import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, SafeAreaView } from 'react-native';

// Import the converted UI components
import { Button } from '../components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { Switch } from '../components/ui/switch';

export default function UITestScreen() {
  const [inputValue, setInputValue] = useState('');
  const [switchValue, setSwitchValue] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleButtonPress = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        
        {/* Header */}
        <Card style={styles.headerCard}>
          <CardHeader>
            <CardTitle>UI Components Test</CardTitle>
            <CardDescription>
              Testing the converted React Native UI components from Next.js
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Button Variants */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Button Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonGrid}>
              <Button onPress={handleButtonPress} loading={loading}>
                Default Button
              </Button>
              <Button variant="secondary" onPress={handleButtonPress}>
                Secondary
              </Button>
              <Button variant="outline" onPress={handleButtonPress}>
                Outline
              </Button>
              <Button variant="destructive" onPress={handleButtonPress}>
                Destructive
              </Button>
              <Button variant="ghost" onPress={handleButtonPress}>
                Ghost
              </Button>
              <Button variant="link" onPress={handleButtonPress}>
                Link Button
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Button Sizes */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Button Sizes</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.buttonGrid}>
              <Button size="sm" onPress={handleButtonPress}>
                Small
              </Button>
              <Button size="default" onPress={handleButtonPress}>
                Default
              </Button>
              <Button size="lg" onPress={handleButtonPress}>
                Large
              </Button>
              <Button size="icon" onPress={handleButtonPress}>
                ⚙️
              </Button>
            </View>
          </CardContent>
        </Card>

        {/* Input Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Input Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Input
              placeholder="Enter some text..."
              value={inputValue}
              onChangeText={setInputValue}
              style={styles.input}
            />
            <Input
              placeholder="Disabled input"
              editable={false}
              style={styles.input}
            />
          </CardContent>
        </Card>

        {/* Badge Variants */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Badge Variants</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.badgeGrid}>
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="outline">Outline</Badge>
            </View>
          </CardContent>
        </Card>

        {/* Switch Component */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Switch Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.switchContainer}>
              <Switch
                value={switchValue}
                onValueChange={setSwitchValue}
              />
            </View>
          </CardContent>
        </Card>

        {/* Separator */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Separator Component</CardTitle>
          </CardHeader>
          <CardContent>
            <View style={styles.separatorDemo}>
              <Button variant="outline">Above</Button>
              <Separator style={styles.separator} />
              <Button variant="outline">Below</Button>
            </View>
          </CardContent>
        </Card>

        {/* Card with Footer */}
        <Card style={styles.card}>
          <CardHeader>
            <CardTitle>Card with Footer</CardTitle>
            <CardDescription>
              This card demonstrates the footer component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Input
              placeholder="Card content input"
              style={styles.input}
            />
          </CardContent>
          <CardFooter>
            <Button variant="outline" style={styles.footerButton}>
              Cancel
            </Button>
            <Button style={styles.footerButton}>
              Save
            </Button>
          </CardFooter>
        </Card>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
  },
  buttonGrid: {
    gap: 12,
  },
  badgeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  input: {
    marginBottom: 12,
  },
  switchContainer: {
    alignItems: 'flex-start',
  },
  separatorDemo: {
    gap: 16,
  },
  separator: {
    marginVertical: 8,
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});
