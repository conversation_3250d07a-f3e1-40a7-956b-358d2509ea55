import { NextResponse } from 'next/server';
import { getDailySpiritualInsight } from '@/ai/flows/daily-insight-flow';

export async function GET() {
  try {
    const result = await getDailySpiritualInsight();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching daily insight:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
    return NextResponse.json({ error: 'Failed to fetch daily insight.', details: errorMessage }, { status: 500 });
  }
}
