
import * as React from 'react';
import {
  TextInput,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';

// Define color constants for React Native
const colors = {
  background: '#ffffff',
  border: '#e2e8f0',
  primary: '#14b8a6',
  foreground: '#0f172a',
  mutedForeground: '#64748b',
};

export interface TextareaProps extends TextInputProps {
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Textarea = React.forwardRef<TextInput, TextareaProps>(
  ({ style, textStyle, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);

    return (
      <TextInput
        ref={ref}
        style={[
          styles.textarea,
          isFocused && styles.textareaFocused,
          props.editable === false && styles.textareaDisabled,
          style,
          textStyle,
        ]}
        placeholderTextColor={colors.mutedForeground}
        multiline={true}
        numberOfLines={4}
        textAlignVertical="top"
        onFocus={(e) => {
          setIsFocused(true);
          props.onFocus?.(e);
        }}
        onBlur={(e) => {
          setIsFocused(false);
          props.onBlur?.(e);
        }}
        {...props}
      />
    );
  }
);

Textarea.displayName = 'Textarea';

const styles = StyleSheet.create({
  textarea: {
    minHeight: 80,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 6,
    backgroundColor: colors.background,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: colors.foreground,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  textareaFocused: {
    borderColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  textareaDisabled: {
    opacity: 0.5,
    backgroundColor: '#f8fafc',
  },
});

export { Textarea };
    