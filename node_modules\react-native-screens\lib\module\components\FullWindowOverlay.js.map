{"version": 3, "names": ["React", "Platform", "View", "FullWindowOverlayNativeComponent", "NativeFullWindowOverlay", "FullWindowOverlay", "props", "OS", "console", "warn", "createElement", "style", "position", "width", "height", "children"], "sourceRoot": "../../../src", "sources": ["components/FullWindowOverlay.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;AAC3D,SAASC,QAAQ,EAAaC,IAAI,QAAmB,cAAc;;AAEnE;AACA,OAAOC,gCAAgC,MAAM,4CAA4C;AACzF,MAAMC,uBAIL,GAAGD,gCAAuC;AAE3C,SAASE,iBAAiBA,CAACC,KAA8B,EAAE;EACzD,IAAIL,QAAQ,CAACM,EAAE,KAAK,KAAK,EAAE;IACzBC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACrE,oBAAOT,KAAA,CAAAU,aAAA,CAACR,IAAI,EAAKI,KAAQ,CAAC;EAC5B;EACA,oBACEN,KAAA,CAAAU,aAAA,CAACN,uBAAuB;IACtBO,KAAK,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE,GAC9DR,KAAK,CAACS,QACgB,CAAC;AAE9B;AAEA,eAAeV,iBAAiB"}