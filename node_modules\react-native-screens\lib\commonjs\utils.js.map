{"version": 3, "names": ["_reactNative", "require", "isSearchBarAvailableForCurrentPlatform", "exports", "includes", "Platform", "OS", "executeNativeBackPress", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "isNewBackTitleImplementation"], "sourceRoot": "../../src", "sources": ["utils.ts"], "mappings": ";;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,sCAAsC,GAAAC,OAAA,CAAAD,sCAAA,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACE,QAAQ,CAACC,qBAAQ,CAACC,EAAE,CAAC;AAEhB,SAASC,sBAAsBA,CAAA,EAAG;EACvC;EACAC,wBAAW,CAACC,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACO,MAAMC,4BAA4B,GAAAP,OAAA,CAAAO,4BAAA,GAAG,IAAI"}