
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base HSL values for reuse */
:root {
  /* Neutrals for light mode (default 'light-teal' and other light themes) */
  --light-bg-hsl: 0 0% 100%; /* Pure White */
  --light-fg-hsl: 220 25% 25%; /* Sophisticated Dark Gray/Blue */
  --light-card-hsl: 0 0% 98%; /* Slightly Off-White */
  --light-popover-hsl: 0 0% 98%;
  --light-secondary-hsl: 210 60% 97%; /* Lighter blue for subtle backgrounds */
  --light-muted-hsl: 210 50% 93%; /* Muted light blue */
  --light-border-hsl: 210 30% 88%;
  --light-input-hsl: 210 30% 94%;

  /* Neutrals for dark mode (default 'dark-teal' and other dark themes) */
  --dark-bg-hsl: 210 20% 10%; /* Dark Slate Blue */
  --dark-fg-hsl: 210 20% 85%; /* Light Grayish Blue */
  --dark-card-hsl: 210 20% 12%;
  --dark-popover-hsl: 210 20% 12%;
  --dark-secondary-hsl: 210 15% 25%;
  --dark-muted-hsl: 210 15% 25%;
  --dark-border-hsl: 210 15% 30%;
  --dark-input-hsl: 210 15% 20%;

  /* Accent - kept consistent for now, could also be themed */
  --accent-base-hsl: 94 52% 75%; /* #BCE29E - Light Green */
  --accent-fg-base-hsl: 90 25% 25%; /* Dark green */
  
  /* Destructive (remains constant) */
  --destructive-hsl: 0 84.2% 60.2%;
  --destructive-fg-hsl: 0 0% 98%;

  /* Chart colors (can remain constant or be themed if desired) */
  --chart-1-hsl: 12 76% 61%;
  --chart-2-hsl: 173 58% 39%;
  --chart-3-hsl: 197 37% 24%;
  --chart-4-hsl: 43 74% 66%;
  --chart-5-hsl: 27 87% 67%;

  --radius: 0.5rem;

  /* Sabiriya Blue Theme Colors */
  --primary-sabiriya-light-hsl: 203 76% 65%;
  --primary-sabiriya-light-fg-hsl: 210 25% 15%;
  --primary-sabiriya-dark-hsl: 203 60% 55%;
  --primary-sabiriya-dark-fg-hsl: 210 20% 95%;

  /* Crystal Blue Theme Colors */
  --primary-crystal-light-hsl: 205 90% 70%; /* A bit brighter light blue */
  --primary-crystal-light-fg-hsl: 205 20% 10%; /* Darker fg for better contrast */
  --primary-crystal-dark-hsl: 205 75% 60%; /* More vibrant dark blue */
  --primary-crystal-dark-fg-hsl: 205 10% 95%; /* Lighter fg for dark mode */

  /* Serene Teal Theme Colors (NEW DEFAULT - matching call button) */
  --primary-teal-light-hsl: 175 84% 31%; /* Tailwind teal-600 #0d9488 */
  --primary-teal-light-fg-hsl: 175 20% 95%; /* Light text for dark teal */
  --primary-teal-dark-hsl: 175 80% 40%;   /* Tailwind teal-500 #14b8a6 */
  --primary-teal-dark-fg-hsl: 175 20% 95%;    /* Light text for dark teal */

  /* Warm Amethyst Theme Colors */
  --primary-amethyst-light-hsl: 270 50% 70%;
  --primary-amethyst-light-fg-hsl: 270 10% 15%;
  --primary-amethyst-dark-hsl: 270 40% 55%;
  --primary-amethyst-dark-fg-hsl: 270 10% 90%;

  /* Sidebar variables - more neutral to fit various themes */
  --sidebar-light-bg-hsl: 210 60% 98%; /* Very light gray-blue */
  --sidebar-light-fg-hsl: 220 20% 35%;
  --sidebar-light-border-hsl: 210 40% 90%;
  
  --sidebar-dark-bg-hsl: 210 20% 14%; /* Slightly lighter than main dark bg */
  --sidebar-dark-fg-hsl: 210 20% 80%;
  --sidebar-dark-border-hsl: 210 15% 25%;
}

@layer base {
  :root, html[theme="light"], html[theme="light-teal"] { /* Default to Serene Teal for light mode */
    --background: var(--light-bg-hsl);
    --foreground: var(--light-fg-hsl);
    --card: var(--light-card-hsl);
    --card-foreground: var(--light-fg-hsl);
    --popover: var(--light-popover-hsl);
    --popover-foreground: var(--light-fg-hsl);
    --primary: var(--primary-teal-light-hsl);
    --primary-foreground: var(--primary-teal-light-fg-hsl);
    --secondary: var(--light-secondary-hsl);
    --secondary-foreground: var(--light-fg-hsl);
    --muted: var(--light-muted-hsl);
    --muted-foreground: hsl(220 15% 55%);
    --accent: var(--accent-base-hsl);
    --accent-foreground: var(--accent-fg-base-hsl);
    --destructive: var(--destructive-hsl);
    --destructive-foreground: var(--destructive-fg-hsl);
    --border: var(--light-border-hsl);
    --input: var(--light-input-hsl);
    --ring: var(--primary-teal-light-hsl);
    --chart-1: var(--chart-1-hsl);
    --chart-2: var(--chart-2-hsl);
    --chart-3: var(--chart-3-hsl);
    --chart-4: var(--chart-4-hsl);
    --chart-5: var(--chart-5-hsl);

    --sidebar-background: var(--sidebar-light-bg-hsl);
    --sidebar-foreground: var(--sidebar-light-fg-hsl);
    --sidebar-primary: var(--primary-teal-light-hsl); 
    --sidebar-primary-foreground: var(--primary-teal-light-fg-hsl);
    --sidebar-accent: hsl(var(--primary-teal-light-hsl) / 0.15);
    --sidebar-accent-foreground: var(--primary-teal-light-hsl);
    --sidebar-border: var(--sidebar-light-border-hsl);
    --sidebar-ring: var(--primary-teal-light-hsl);
  }

  html[theme="dark"], html[theme="dark-teal"] { /* Default to Serene Teal for dark mode */
    --background: var(--dark-bg-hsl);
    --foreground: var(--dark-fg-hsl);
    --card: var(--dark-card-hsl);
    --card-foreground: var(--dark-fg-hsl);
    --popover: var(--dark-popover-hsl);
    --popover-foreground: var(--dark-fg-hsl);
    --primary: var(--primary-teal-dark-hsl);
    --primary-foreground: var(--primary-teal-dark-fg-hsl);
    --secondary: var(--dark-secondary-hsl);
    --secondary-foreground: var(--dark-fg-hsl);
    --muted: var(--dark-muted-hsl);
    --muted-foreground: hsl(210 15% 65%);
    --accent: var(--accent-base-hsl); 
    --accent-foreground: var(--accent-fg-base-hsl);
    --destructive: var(--destructive-hsl);
    --destructive-foreground: var(--destructive-fg-hsl);
    --border: var(--dark-border-hsl);
    --input: var(--dark-input-hsl);
    --ring: var(--primary-teal-dark-hsl);
    --chart-1: var(--chart-1-hsl);
    --chart-2: var(--chart-2-hsl);
    --chart-3: var(--chart-3-hsl);
    --chart-4: var(--chart-4-hsl);
    --chart-5: var(--chart-5-hsl);

    --sidebar-background: var(--sidebar-dark-bg-hsl);
    --sidebar-foreground: var(--sidebar-dark-fg-hsl);
    --sidebar-primary: var(--primary-teal-dark-hsl);
    --sidebar-primary-foreground: var(--primary-teal-dark-fg-hsl);
    --sidebar-accent: hsl(var(--primary-teal-dark-hsl) / 0.2);
    --sidebar-accent-foreground: var(--primary-teal-dark-hsl);
    --sidebar-border: var(--sidebar-dark-border-hsl);
    --sidebar-ring: var(--primary-teal-dark-hsl);
  }
  
  /* Sabiriya Blue Theme */
   html[theme="light-sabiriya"] {
    --primary: var(--primary-sabiriya-light-hsl);
    --primary-foreground: var(--primary-sabiriya-light-fg-hsl);
    --ring: var(--primary-sabiriya-light-hsl);
    --sidebar-primary: var(--primary-sabiriya-light-hsl);
    --sidebar-primary-foreground: var(--primary-sabiriya-light-fg-hsl);
    --sidebar-accent: hsl(var(--primary-sabiriya-light-hsl) / 0.15);
    --sidebar-accent-foreground: var(--primary-sabiriya-light-hsl);
    --sidebar-ring: var(--primary-sabiriya-light-hsl);
  }
  html[theme="dark-sabiriya"] {
    --primary: var(--primary-sabiriya-dark-hsl);
    --primary-foreground: var(--primary-sabiriya-dark-fg-hsl);
    --ring: var(--primary-sabiriya-dark-hsl);
    --sidebar-primary: var(--primary-sabiriya-dark-hsl);
    --sidebar-primary-foreground: var(--primary-sabiriya-dark-fg-hsl);
    --sidebar-accent: hsl(var(--primary-sabiriya-dark-hsl) / 0.2);
    --sidebar-accent-foreground: var(--primary-sabiriya-dark-hsl);
    --sidebar-ring: var(--primary-sabiriya-dark-hsl);
  }

  /* Crystal Blue Theme */
  html[theme="light-crystal"] {
    --primary: var(--primary-crystal-light-hsl);
    --primary-foreground: var(--primary-crystal-light-fg-hsl);
    --ring: var(--primary-crystal-light-hsl);
    --sidebar-primary: var(--primary-crystal-light-hsl);
    --sidebar-primary-foreground: var(--primary-crystal-light-fg-hsl);
    --sidebar-accent: hsl(var(--primary-crystal-light-hsl) / 0.15);
    --sidebar-accent-foreground: var(--primary-crystal-light-hsl);
    --sidebar-ring: var(--primary-crystal-light-hsl);
  }
  html[theme="dark-crystal"] {
    --primary: var(--primary-crystal-dark-hsl);
    --primary-foreground: var(--primary-crystal-dark-fg-hsl);
    --ring: var(--primary-crystal-dark-hsl);
    --sidebar-primary: var(--primary-crystal-dark-hsl);
    --sidebar-primary-foreground: var(--primary-crystal-dark-fg-hsl);
    --sidebar-accent: hsl(var(--primary-crystal-dark-hsl) / 0.2);
    --sidebar-accent-foreground: var(--primary-crystal-dark-hsl);
    --sidebar-ring: var(--primary-crystal-dark-hsl);
  }

  /* Warm Amethyst Theme */
  html[theme="light-amethyst"] {
    --primary: var(--primary-amethyst-light-hsl);
    --primary-foreground: var(--primary-amethyst-light-fg-hsl);
    --ring: var(--primary-amethyst-light-hsl);
    --sidebar-primary: var(--primary-amethyst-light-hsl);
    --sidebar-primary-foreground: var(--primary-amethyst-light-fg-hsl);
    --sidebar-accent: hsl(var(--primary-amethyst-light-hsl) / 0.15);
    --sidebar-accent-foreground: var(--primary-amethyst-light-hsl);
    --sidebar-ring: var(--primary-amethyst-light-hsl);
  }
  html[theme="dark-amethyst"] {
    --primary: var(--primary-amethyst-dark-hsl);
    --primary-foreground: var(--primary-amethyst-dark-fg-hsl);
    --ring: var(--primary-amethyst-dark-hsl);
    --sidebar-primary: var(--primary-amethyst-dark-hsl);
    --sidebar-primary-foreground: var(--primary-amethyst-dark-fg-hsl);
    --sidebar-accent: hsl(var(--primary-amethyst-dark-hsl) / 0.2);
    --sidebar-accent-foreground: var(--primary-amethyst-dark-hsl);
    --sidebar-ring: var(--primary-amethyst-dark-hsl);
  }

  /* Apply common variables for all themes within a mode */
  html[theme^="light-"] {
    --background: var(--light-bg-hsl);
    --foreground: var(--light-fg-hsl);
    --card: var(--light-card-hsl);
    --card-foreground: var(--light-fg-hsl);
    --popover: var(--light-popover-hsl);
    --popover-foreground: var(--light-fg-hsl);
    --secondary: var(--light-secondary-hsl);
    --secondary-foreground: var(--light-fg-hsl);
    --muted: var(--light-muted-hsl);
    --muted-foreground: hsl(220 15% 55%);
    --accent: var(--accent-base-hsl);
    --accent-foreground: var(--accent-fg-base-hsl);
    --destructive: var(--destructive-hsl);
    --destructive-foreground: var(--destructive-fg-hsl);
    --border: var(--light-border-hsl);
    --input: var(--light-input-hsl);
    --chart-1: var(--chart-1-hsl);
    --chart-2: var(--chart-2-hsl);
    --chart-3: var(--chart-3-hsl);
    --chart-4: var(--chart-4-hsl);
    --chart-5: var(--chart-5-hsl);
    --sidebar-background: var(--sidebar-light-bg-hsl);
    --sidebar-foreground: var(--sidebar-light-fg-hsl);
    --sidebar-border: var(--sidebar-light-border-hsl);
  }
  html[theme^="dark-"] {
    --background: var(--dark-bg-hsl);
    --foreground: var(--dark-fg-hsl);
    --card: var(--dark-card-hsl);
    --card-foreground: var(--dark-fg-hsl);
    --popover: var(--dark-popover-hsl);
    --popover-foreground: var(--dark-fg-hsl);
    --secondary: var(--dark-secondary-hsl);
    --secondary-foreground: var(--dark-fg-hsl);
    --muted: var(--dark-muted-hsl);
    --muted-foreground: hsl(210 15% 65%);
    --accent: var(--accent-base-hsl); 
    --accent-foreground: var(--accent-fg-base-hsl);
    --destructive: var(--destructive-hsl);
    --destructive-foreground: var(--destructive-fg-hsl);
    --border: var(--dark-border-hsl);
    --input: var(--dark-input-hsl);
    --chart-1: var(--chart-1-hsl);
    --chart-2: var(--chart-2-hsl);
    --chart-3: var(--chart-3-hsl);
    --chart-4: var(--chart-4-hsl);
    --chart-5: var(--chart-5-hsl);
    --sidebar-background: var(--sidebar-dark-bg-hsl);
    --sidebar-foreground: var(--sidebar-dark-fg-hsl);
    --sidebar-border: var(--sidebar-dark-border-hsl);
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans; /* Apply Inter by default */
    line-height: 1.7; /* Enhanced line-height for body text */
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display text-foreground; /* Apply Poppins to headings */
    letter-spacing: theme('letterSpacing.tight');
    line-height: theme('lineHeight.tight');
  }

  .font-arabic {
    font-family: 'Noto Naskh Arabic', 'Noto Nastaliq Urdu', system-ui, sans-serif;
    /* If local fonts are set up, uncomment: */
    /* font-family: var(--font-al-majid, 'Noto Naskh Arabic'), var(--font-jameel-noori, 'Noto Nastaliq Urdu'), system-ui, sans-serif; */
    line-height: 1.9; 
  }
}


@layer utilities {
  .glass-card-light {
    @apply bg-white/70 backdrop-blur-lg border border-white/30 shadow-xl;
  }
  .glass-card-dark {
    @apply bg-slate-800/70 backdrop-blur-lg border border-slate-700/50 shadow-xl;
  }
  /* Generic glass-card that adapts to theme */
  .glass-card {
    @apply bg-card/70 backdrop-blur-md border-border/30 shadow-lg;
  }

  html[theme^="light-"] .glass-effect {
    background-color: hsla(var(--light-card-hsl), 0.75); /* Slightly more opacity */
    backdrop-filter: blur(10px) saturate(120%); /* Softer blur */
    -webkit-backdrop-filter: blur(10px) saturate(120%);
    border: 1px solid hsla(var(--light-border-hsl), 0.25);
  }
  html[theme^="dark-"] .glass-effect {
    background-color: hsla(var(--dark-card-hsl), 0.75); /* Slightly more opacity */
    backdrop-filter: blur(10px) saturate(120%); /* Softer blur */
    -webkit-backdrop-filter: blur(10px) saturate(120%);
    border: 1px solid hsla(var(--dark-border-hsl), 0.25);
  }

  /* Custom 3D transform utilities */
  .perspective-1000 {
    perspective: 1000px;
  }
  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  /* Book page style for Arabic text */
  .book-page-arabic {
    @apply bg-amber-50 dark:bg-neutral-800 p-6 md:p-8 shadow-lg border border-black/10 dark:border-white/10 rounded-md;
  }
}

    
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Specific adjustments for PageTitle component compatibility if needed */
/* Ensure this selector is correct and doesn't cause parsing errors */
.text-3xl.font-bold.tracking-tight.sm\:text-4xl { /* Targeting PageTitle's h1 */
  font-family: var(--font-poppins), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.file\:border-0::file-selector-button {
  border-width: 0;
}
.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}
.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
  font-weight: 500;
}
.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}
