import * as React from "react"
import {
  Switch as RNSwitch,
  SwitchProps as R<PERSON>witchP<PERSON>,
} from "react-native"

// Define color constants for React Native
const colors = {
  primary: '#14b8a6',
  input: '#e2e8f0',
  background: '#ffffff',
}

export interface SwitchProps extends RNSwitchProps {
  value?: boolean
  onValueChange?: (value: boolean) => void
}

const Switch = React.forwardRef<RNSwitch, SwitchProps>(
  ({ value, onValueChange, ...props }, ref) => {
    return (
      <RNSwitch
        ref={ref}
        value={value}
        onValueChange={onValueChange}
        trackColor={{
          false: colors.input,
          true: colors.primary
        }}
        thumbColor={colors.background}
        ios_backgroundColor={colors.input}
        {...props}
      />
    )
  }
)

Switch.displayName = "Switch"

export { Switch }
