
"use client";

import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import MosqueSilhouetteIcon from '@/components/icons/MosqueSilhouetteIcon';
import { ArrowRight, CheckCircle, BookOpen, Settings } from 'lucide-react'; // Added BookOpen, Settings
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';

type OnboardingStepContent = {
  title: string;
  description: string;
  icon: JSX.Element;
};

const onboardingStepDetails: Record<Language, OnboardingStepContent[]> = {
  en: [
    {
      title: 'Welcome to Sabiriya: Your Spiritual Guide',
      description: 'Embark on a journey of peace and enlightenment with authentic teachings, Duas, and practices from the Chishtiya Sabiriya Silsila.',
      icon: <MosqueSilhouetteIcon className="w-20 h-20 text-primary mb-6" />,
    },
    {
      title: 'Sacred Knowledge & Practices',
      description: 'Access Shajra Shareef, Panjsoora, daily <PERSON><PERSON> & Wazaif, and transformative spiritual practices like <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>hasa<PERSON>.',
      icon: <BookOpen className="w-16 h-16 text-primary mb-6" />,
    },
    {
      title: 'Your Personalized Companion',
      description: 'Customize your app with language selection, themes, and font sizes. Engage with unique features like "Hazri Lagwayen" attendance.',
      icon: <Settings className="w-16 h-16 text-primary mb-6" />,
    },
  ],
  ur: [
    {
      title: 'صابریہ میں خوش آمدید: آپ کا روحانی رہنما',
      description: 'سلسلہ چشتیہ صابریہ کی مستند تعلیمات، دعاؤں اور روحانی اعمال کے ساتھ امن اور روحانی ترقی کے سفر کا آغاز کریں۔',
      icon: <MosqueSilhouetteIcon className="w-20 h-20 text-primary mb-6" />,
    },
    {
      title: 'مقدس علم اور روحانی اعمال',
      description: 'شجرہ شریف، پنجسورہ، روزانہ کی دعائیں و وظائف، اور مراقبہ، ذکر، اور محاسبہ جیسے تبدیلی لانے والے روحانی اعمال تک رسائی حاصل کریں۔',
      icon: <BookOpen className="w-16 h-16 text-primary mb-6" />,
    },
    {
      title: 'آپ کا ذاتی روحانی معاون',
      description: 'زبان کے انتخاب، تھیمز، اور فونٹ سائز کے ساتھ اپنی ایپ کو ترتیب دیں۔ "حاضری لگوائیں" جیسے منفرد فیچرز سے مستفید ہوں۔',
      icon: <Settings className="w-16 h-16 text-primary mb-6" />,
    },
  ],
  ro: [
    {
      title: 'Sabiriya Mein Khush Amdeed: Aap Ka Roohani Rehnuma',
      description: 'Silsila Chishtiya Sabiriya ki mustanad ta\'leemaat, duao\'n aur roohani a\'maal ke saath aman aur roohani taraqqi ke safar ka aaghaaz karein.',
      icon: <MosqueSilhouetteIcon className="w-20 h-20 text-primary mb-6" />,
    },
    {
      title: 'Muqaddas Ilm aur Roohani A\'maal',
      description: 'Shajra Shareef, Panjsoora, rozana ki duaein wa wazaif, aur Muraqaba, Zikr, aur Muhasaba jaise tabdeeli laane waale roohani a\'maal tak rasaai haasil karein.',
      icon: <BookOpen className="w-16 h-16 text-primary mb-6" />,
    },
    {
      title: 'Aap Ka Zaati Roohani Mu\'awin',
      description: 'Zabaan ke intekhaab, themes, aur font size ke saath apni app ko tarteeb dein. "Hazri Lagwayen" jaise munfarid features se mustafeed hon.',
      icon: <Settings className="w-16 h-16 text-primary mb-6" />,
    },
  ],
  hi: [
    {
      title: 'साबिरिया में खुश आमदीद: आपका रूहानी रहनुमा',
      description: 'सिलसिला चिश्तिया साबिरिया की मुस्तनद तालीमात, दुआओं और रूहानी आमाल के साथ अमन और रूहानी तरक़्क़ी के सफ़र का आग़ाज़ करें।',
      icon: <MosqueSilhouetteIcon className="w-20 h-20 text-primary mb-6" />,
    },
    {
      title: 'मुक़द्दस इल्म और रूहानी आमाल',
      description: 'शजरह शरीफ़, पंजसूरा, रोज़ाना की दुआएँ व वज़ाइफ़, और मुराक़बा, ज़िक्र, और मुहासबा जैसे तब्दीली लाने वाले रूहानी आमाल तक रसाई हासिल करें।',
      icon: <BookOpen className="w-16 h-16 text-primary mb-6" />,
    },
    {
      title: 'आपका ज़ाती रूहानी मुआविन',
      description: 'ज़बान के इंतिख़ाब, थीम्स, और फ़ॉन्ट साइज़ के साथ अपनी ऐप को तरतीब दें। "हाज़िरी लगवाएँ" जैसे मुनफ़रिद फ़ीचर्स से मुस्तफ़ीद हों।',
      icon: <Settings className="w-16 h-16 text-primary mb-6" />,
    },
  ],
  ar: [
    {
      title: 'أهلاً بك في صابرية: دليلك الروحي',
      description: 'انطلق في رحلة من السكينة والتنوير مع التعاليم الأصيلة، الأدعية، والممارسات الروحية من السلسلة الجشتية الصابرية.',
      icon: <MosqueSilhouetteIcon className="w-20 h-20 text-primary mb-6" />,
    },
    {
      title: 'المعرفة المقدسة والممارسات الروحية',
      description: 'احصل على الشجرة الشريفة، بنجسورة، الأدعية والأوراد اليومية، والممارسات الروحية التحويلية مثل المراقبة، الذكر، والمحاسبة.',
      icon: <BookOpen className="w-16 h-16 text-primary mb-6" />,
    },
    {
      title: 'رفيقك الروحي المخصص',
      description: 'خصص تطبيقك باختيار اللغة، السمات، وأحجام الخطوط. تفاعل مع ميزات فريدة مثل نظام الحضور "حاضري لغواين".',
      icon: <Settings className="w-16 h-16 text-primary mb-6" />,
    },
  ],
};

const buttonTexts: Record<Language, { next: string; getStarted: string }> = {
  en: { next: 'Next', getStarted: 'Get Started' },
  ur: { next: 'آگے', getStarted: 'شروع کریں' },
  ro: { next: 'Aage', getStarted: 'Shuru Karein' },
  hi: { next: 'आगे', getStarted: 'शुरू करें' },
  ar: { next: 'التالي', getStarted: 'ابدأ الآن' },
};

export default function OnboardingPage() {
  const router = useRouter();
  const params = useParams();
  const { language } = useLanguage();
  const step = parseInt(params.step as string, 10);

  const currentLanguageSteps = onboardingStepDetails[language] || onboardingStepDetails.en;
  const currentButtonTexts = buttonTexts[language] || buttonTexts.en;

  if (isNaN(step) || step < 1 || step > currentLanguageSteps.length) {
    // Instead of redirecting to /home, redirect to language selection if step is invalid.
    // The language selection page will then handle redirection to onboarding or home.
    router.replace('/select-language');
    return null;
  }

  const currentStepData = currentLanguageSteps[step - 1];

  const handleNext = () => {
    if (step < currentLanguageSteps.length) {
      router.push(`/onboarding/${step + 1}`);
    } else {
      localStorage.setItem('sabiriyaOnboardingComplete', 'true');
      // After onboarding is complete, the user has already selected a language in the previous step.
      // So, redirect to home.
      router.replace('/home'); 
    }
  };

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  return (
    <div className={cn("flex flex-col items-center justify-center text-center max-w-md mx-auto p-6 rounded-lg shadow-xl bg-card", fontClass)}
         lang={language}
         dir={isRtl ? 'rtl' : 'ltr'}
    >
      {currentStepData.icon}
      <h1 className={cn("text-2xl md:text-3xl font-bold mb-4 text-card-foreground", fontClass)}>{currentStepData.title}</h1>
      <p className={cn("text-md md:text-lg text-muted-foreground mb-8", fontClass)}>{currentStepData.description}</p>
      <Button onClick={handleNext} className={cn("w-full sm:w-auto px-8 py-3 text-lg", fontClass)}>
        {step < currentLanguageSteps.length ? currentButtonTexts.next : currentButtonTexts.getStarted} 
        <ArrowRight className={cn("h-5 w-5", isRtl ? "mr-2 transform rotate-180" : "ml-2")} />
      </Button>
      <div className="flex justify-center mt-8">
        {currentLanguageSteps.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full mx-1 ${
              index === step - 1 ? 'bg-primary' : 'bg-muted'
            }`}
          ></div>
        ))}
      </div>
    </div>
  );
}
