{"version": 3, "names": ["enableScreens", "enableFreeze", "screensEnabled", "freezeEnabled", "shouldUseActivityState", "default", "Screen", "NativeScreen", "InnerScreen", "ScreenContext", "ScreenContainer", "NativeScreenContainer", "NativeScreenNavigationContainer", "ScreenStack", "ScreenStackHeaderConfig", "ScreenStackHeaderSubview", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "ScreenStackHeaderRightView", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderSearchBarView", "SearchBar", "NativeSearchBar", "NativeSearchBarCommands", "FullWindowOverlay", "NativeScreensModule", "GHContext", "isSearchBarAvailableForCurrentPlatform", "isNewBackTitleImplementation", "executeNativeBackPress", "useTransitionProgress"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA,cAAc,SAAS;;AAEvB;AACA;AACA;AACA,SACEA,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,sBAAsB,QACjB,QAAQ;;AAEf;AACA;AACA;AACA,SACEC,OAAO,IAAIC,MAAM,EACjBC,YAAY,EACZC,WAAW,EACXC,aAAa,QACR,qBAAqB;AAE5B,SACEJ,OAAO,IAAIK,eAAe,EAC1BC,qBAAqB,EACrBC,+BAA+B,QAC1B,8BAA8B;AAErC,SAASP,OAAO,IAAIQ,WAAW,QAAQ,0BAA0B;AAEjE,SACEC,uBAAuB,EACvBC,wBAAwB,EACxBC,yBAAyB,EACzBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,8BAA8B,QACzB,sCAAsC;AAE7C,SACEf,OAAO,IAAIgB,SAAS,EACpBC,eAAe,EACfC,uBAAuB,QAClB,wBAAwB;AAE/B,SAASlB,OAAO,IAAImB,iBAAiB,QAAQ,gCAAgC;;AAE7E;AACA;AACA;AACA,SAASnB,OAAO,IAAIoB,mBAAmB,QAAQ,8BAA8B;;AAE7E;AACA;AACA;AACA,SAASC,SAAS,QAAQ,mCAAmC;;AAE7D;AACA;AACA;AACA,SACEC,sCAAsC,EACtCC,4BAA4B,EAC5BC,sBAAsB,QACjB,SAAS;;AAEhB;AACA;AACA;AACA,SAASxB,OAAO,IAAIyB,qBAAqB,QAAQ,yBAAyB"}