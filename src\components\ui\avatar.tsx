import * as React from "react"
import {
  View,
  Text,
  Image,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ViewProps,
  ImageProps,
  TextProps,
} from "react-native"

// Define color constants for React Native
const colors = {
  muted: '#f1f5f9',
  foreground: '#0f172a',
}

export interface AvatarProps extends ViewProps {
  size?: number
  style?: ViewStyle
  children?: React.ReactNode
}

export interface AvatarImageProps extends ImageProps {
  style?: ViewStyle
}

export interface AvatarFallbackProps extends TextProps {
  style?: TextStyle
  children?: React.ReactNode
}

const Avatar = React.forwardRef<View, AvatarProps>(
  ({ size = 40, style, children, ...props }, ref) => (
    <View
      ref={ref}
      style={[
        styles.avatar,
        { width: size, height: size, borderRadius: size / 2 },
        style,
      ]}
      {...props}
    >
      {children}
    </View>
  )
)

const AvatarImage = React.forwardRef<Image, AvatarImageProps>(
  ({ style, ...props }, ref) => (
    <Image
      ref={ref}
      style={[styles.avatarImage, style]}
      {...props}
    />
  )
)

const AvatarFallback = React.forwardRef<Text, AvatarFallbackProps>(
  ({ style, children, ...props }, ref) => (
    <Text
      ref={ref}
      style={[styles.avatarFallback, style]}
      {...props}
    >
      {children}
    </Text>
  )
)

Avatar.displayName = "Avatar"
AvatarImage.displayName = "AvatarImage"
AvatarFallback.displayName = "AvatarFallback"

const styles = StyleSheet.create({
  avatar: {
    position: 'relative',
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.muted,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
  },
  avatarFallback: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.foreground,
    textAlign: 'center',
  },
})

export { Avatar, AvatarImage, AvatarFallback }
