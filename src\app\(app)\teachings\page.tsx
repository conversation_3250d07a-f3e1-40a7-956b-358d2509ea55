
"use client";

import { useState, useEffect, useMemo } from 'react';
import { teachingCategories as mockTeachingCategories, type TeachingCategory as MockTeachingCategory } from '@/data/teachings';
import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Search } from 'lucide-react';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { Input } from '@/components/ui/input';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { cn } from '@/lib/utils';

type TeachingCategoryUrdu = {
  id: string;
  slug: string;
  category_urdu: string;
  description: string;
};

const getTeachingCategories = async (): Promise<TeachingCategoryUrdu[]> => {
  return mockTeachingCategories.map(cat => ({
    id: cat.id,
    slug: cat.slug,
    category_urdu: cat.name,
    description: cat.description,
  }));
};

const pageTranslations: Record<Language, {
  pageTitle: string;
  pageSubtitle: string;
  searchPlaceholder: string;
  viewTeachingsButton: string;
  noCategoriesFound: string;
  noCategoriesFoundSearch: string;
}> = {
  en: {
    pageTitle: "Spiritual Teachings",
    pageSubtitle: "Explore the wisdom and guidance from the Silsila Aaliya Chishtiya Sabiriya.",
    searchPlaceholder: "Search categories...",
    viewTeachingsButton: "View Teachings",
    noCategoriesFound: "No teaching categories found.",
    noCategoriesFoundSearch: "No categories found matching your search.",
  },
  ur: {
    pageTitle: "روحانی تعلیمات",
    pageSubtitle: "سلسلہ عالیہ چشتیہ صابریہ کی حکمت و رہنمائی دریافت کریں۔",
    searchPlaceholder: "زمرے تلاش کریں...",
    viewTeachingsButton: "تعلیمات دیکھیں",
    noCategoriesFound: "کوئی تدریسی زمرہ نہیں ملا۔",
    noCategoriesFoundSearch: "آپ کی تلاش کے مطابق کوئی زمرہ نہیں ملا۔",
  },
  ro: {
    pageTitle: "Roohani Ta'leemaat",
    pageSubtitle: "Silsila Aaliya Chishtiya Sabiriya ki hikmat o rehnumaee daryaaft karein.",
    searchPlaceholder: "Categories talaash karein...",
    viewTeachingsButton: "Ta'leemaat Dekhein",
    noCategoriesFound: "Koi tadreesi category nahin mili.",
    noCategoriesFoundSearch: "Aapki talaash ke mutaabiq koi category nahin mili.",
  },
  hi: {
    pageTitle: "रूहानी ता'लीमात",
    pageSubtitle: "सिलसिला आलिया चिश्तिया साबिरीया की हिकमत ओ रहनुमाई दरयाफ़्त करें।",
    searchPlaceholder: "श्रेणियाँ खोजें...",
    viewTeachingsButton: "ता'लीमात देखें",
    noCategoriesFound: "कोई ता'लीमी श्रेणी नहीं मिली।",
    noCategoriesFoundSearch: "आपकी खोज के मुताबिक कोई श्रेणी नहीं मिली।",
  },
  ar: {
    pageTitle: "التعاليم الروحية",
    pageSubtitle: "اكتشف الحكمة والإرشاد من السلسلة العلية الجشتية الصابرية.",
    searchPlaceholder: "ابحث في الفئات...",
    viewTeachingsButton: "عرض التعاليم",
    noCategoriesFound: "لم يتم العثور على فئات تعليمية.",
    noCategoriesFoundSearch: "لم يتم العثور على فئات تطابق بحثك.",
  },
};

const categoryTitleTranslations: Record<string, Record<Language, string>> = {
  'saat-naseehatain': {
    en: "Seven Advices",
    ur: "سات نصیحتیں",
    ro: "Saat Naseehatain",
    hi: "सात नसीहतें",
    ar: "النصائح السبع",
  },
  'spirituality-basics': {
    en: "Spirituality Basics",
    ur: "روحانیت کی بنیادی باتیں",
    ro: "Roohaniyat ki Buniyadi Baatein",
    hi: "आध्यात्मिकता की मूल बातें",
    ar: "أساسيات الروحانية",
  },
  'zikr-and-meditation': {
    en: "Zikr and Meditation",
    ur: "ذکر و مراقبہ",
    ro: "Zikr o Muraqaba",
    hi: "ज़िक्र और मुराक़बा",
    ar: "الذكر والمراقبة",
  },
  'basic-guidelines': {
    en: "Basic Guidelines",
    ur: "بنیادی ہدایات",
    ro: "Buniyadi Hidayaat",
    hi: "बुनियादी हिदायात",
    ar: "إرشادات أساسية",
  },
};

const categoryDescriptionTranslations: Record<string, Record<Language, string>> = {
  'saat-naseehatain': {
    en: "Core advices from the Chishti Sabiri Silsila for spiritual development.",
    ur: "روحانی ترقی کے لیے سلسلہ چشتیہ صابریہ کی بنیادی نصیحتیں۔",
    ro: "Roohani taraqqi ke liye Silsila Chishtiya Sabiriya ki buniyadi naseehtein.",
    hi: "आध्यात्मिक विकास के लिए सिलसिला चिश्तिया साबिरीया की मूल सलाह।",
    ar: "نصائح أساسية من السلسلة الجشتية الصابرية للتطور الروحي.",
  },
  'spirituality-basics': {
    en: "Fundamental concepts of Islamic spirituality and the Sufi path.",
    ur: "اسلامی روحانیت اور صوفی راہ کے بنیادی تصورات۔",
    ro: "Islami roohaniyat aur Sufi raah ke buniyadi tasawwuraat.",
    hi: "इस्लामी आध्यात्मिकता और सूफी मार्ग की मूलभूत अवधारणाएँ।",
    ar: "مفاهيم أساسية في الروحانية الإسلامية والطريق الصوفي.",
  },
  'zikr-and-meditation': {
    en: "Guidance on the practices of Divine remembrance and contemplation.",
    ur: "ذکرِ الٰہی اور مراقبہ کی مشقوں پر رہنمائی۔",
    ro: "Zikr-e-Ilaahi aur muraqaba ki mashqon par rehnumaee.",
    hi: "ईश्वरीय स्मरण और चिंतन के अभ्यासों पर मार्गदर्शन।",
    ar: "إرشادات حول ممارسات الذكر والتأمل الإلهي.",
  },
  'basic-guidelines': {
    en: "Initial instructions and foundational principles for seekers.",
    ur: "سالکین کے لیے ابتدائی ہدایات اور بنیادی اصول۔",
    ro: "Saalikeen ke liye ibtidaai hidayaat aur buniyadi usool.",
    hi: "साधकों के लिए प्रारंभिक निर्देश और मूलभूत सिद्धांत।",
    ar: "تعليمات أولية ومبادئ أساسية للسائرين في الطريق.",
  },
};


export default function TeachingsPage() {
  const { language } = useLanguage();
  const [categories, setCategories] = useState<TeachingCategoryUrdu[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const content = pageTranslations[language] || pageTranslations.en;
  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';

  useEffect(() => {
    const loadCategories = async () => {
      setIsLoading(true);
      setFetchError(null);
      try {
        const fetchedCategories = await getTeachingCategories();
        setCategories(fetchedCategories);
      } catch (error) {
        console.error("Error fetching teaching categories:", error);
        setFetchError("Failed to load teaching categories. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };
    loadCategories();
  }, []);

  const filteredCategories = useMemo(() => {
    if (!searchTerm) {
      return categories;
    }
    return categories.filter(category => {
      const localizedTitle = categoryTitleTranslations[category.slug]?.[language] || category.category_urdu;
      const localizedDescription = categoryDescriptionTranslations[category.slug]?.[language] || category.description;
      const searchTermLower = searchTerm.toLowerCase();
      return (
        localizedTitle.toLowerCase().includes(searchTermLower) ||
        category.slug.toLowerCase().includes(searchTermLower) ||
        localizedDescription.toLowerCase().includes(searchTermLower)
      );
    });
  }, [categories, searchTerm, language]);
  
  return (
    <div className={cn("space-y-8", fontClass)} lang={language} dir={isRtl ? 'rtl' : 'ltr'}>
      <PageTitle 
        title={content.pageTitle} 
        subtitle={content.pageSubtitle} 
        className={cn(isRtl ? 'text-right' : 'text-left')} 
      />

      <div className="relative w-full max-w-md mb-6">
        <Search className={cn("absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground", isRtl ? "right-3" : "left-3")} />
        <Input
          type="search"
          aria-label="Search teaching categories"
          placeholder={content.searchPlaceholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className={cn("w-full", fontClass, isRtl ? "pr-10" : "pl-10")}
        />
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-10">
          <LoadingSpinner size={48} />
        </div>
      )}

      {fetchError && !isLoading && <ErrorMessage message={fetchError} />}

      {!isLoading && !fetchError && filteredCategories.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCategories.map((category: TeachingCategoryUrdu) => {
            const localizedCategoryTitle = categoryTitleTranslations[category.slug]?.[language] || category.category_urdu;
            const localizedCategoryDescription = categoryDescriptionTranslations[category.slug]?.[language] || category.description;

            return (
              <Card key={category.id} className="flex flex-col shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className={cn(isRtl && "text-right")}>
                  <h2 className={cn("text-xl font-semibold", fontClass)}>
                      {localizedCategoryTitle}
                  </h2>
                  {localizedCategoryDescription && (
                       <CardDescription className={cn(fontClass)}>{localizedCategoryDescription}</CardDescription>
                  )}
                </CardHeader>
                <CardContent className="flex-grow flex flex-col justify-end">
                  <Link href={`/teachings/${category.slug}`} passHref>
                    <Button className={cn("w-full mt-4", fontClass)}>
                      {content.viewTeachingsButton} <ArrowRight className={cn("h-4 w-4", isRtl ? "mr-2 transform rotate-180" : "ml-2")} />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
      {!isLoading && !fetchError && filteredCategories.length === 0 && (
         <p className={cn("text-center text-muted-foreground py-10", fontClass)}>
           {searchTerm ? content.noCategoriesFoundSearch : content.noCategoriesFound}
        </p>
      )}
    </div>
  );
}

