{"name": "sabiriya-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "eject": "expo eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "expo": "~51.0.28", "expo-constants": "~16.0.2", "expo-font": "~12.0.9", "expo-linking": "~6.3.1", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "firebase": "^10.12.0", "react": "18.2.0", "react-native": "0.74.5", "react-native-gesture-handler": "~2.16.1", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-hook-form": "^7.54.2", "date-fns": "^3.6.0", "clsx": "^2.1.1", "zod": "^3.24.2", "@react-native-async-storage/async-storage": "1.23.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-expo": "^7.0.0", "typescript": "~5.3.3"}, "private": true}