{"name": "sabiriya-app", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "eject": "expo eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/drawer": "^6.6.6", "expo": "~50.0.0", "expo-constants": "~15.4.0", "expo-device": "~5.9.0", "expo-font": "~11.10.0", "expo-linking": "~6.2.0", "expo-location": "~16.5.0", "expo-notifications": "~0.27.0", "expo-sensors": "~12.8.0", "expo-splash-screen": "~0.26.0", "expo-status-bar": "~1.11.0", "expo-system-ui": "~2.9.0", "expo-web-browser": "~12.8.0", "firebase": "^10.7.0", "react": "18.2.0", "react-native": "0.73.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-hook-form": "^7.54.2", "date-fns": "^3.6.0", "clsx": "^2.1.1", "zod": "^3.24.2", "class-variance-authority": "^0.7.1", "react-native-vector-icons": "^10.0.3", "react-native-paper": "^5.12.3", "react-native-elements": "^3.4.3", "nativewind": "^2.0.11", "tailwindcss": "3.3.0", "@react-native-async-storage/async-storage": "1.21.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-expo": "^7.0.0", "typescript": "^5.1.3"}, "private": true}