{"name": "sabiriya-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "eject": "expo eject", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "expo": "~49.0.0", "expo-constants": "~14.4.2", "expo-font": "~11.4.0", "expo-linking": "~5.0.2", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^10.7.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-hook-form": "^7.54.2", "date-fns": "^3.6.0", "clsx": "^2.1.1", "zod": "^3.24.2", "@react-native-async-storage/async-storage": "1.18.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-expo": "^7.0.0", "typescript": "^5.1.3"}, "private": true}