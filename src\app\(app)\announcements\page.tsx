
"use client";

import { useEffect, useState, useMemo } from 'react';
import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { useLanguage, type Language } from '@/context/LanguageContext';
import { format } from 'date-fns';
import { enUS } from 'date-fns/locale/en-US';
import { arSA } from 'date-fns/locale/ar-SA';
// import { urPK } from 'date-fns/locale/ur-PK'; // Removed problematic import
import { hi } from 'date-fns/locale/hi'; 
import { cn } from '@/lib/utils';
import { Megaphone, MessageSquare } from 'lucide-react';
import { db } from '@/lib/firebaseClient'; // Import Firestore instance
import { collection, query, orderBy, limit, onSnapshot, Timestamp, type DocumentData } from 'firebase/firestore';
import { Button } from '@/components/ui/button'; // Added Button
import Link from 'next/link'; // Added Link

export type AdminAnnouncement = {
  id: string;
  messageText: string;
  timestamp: Timestamp; // Firestore Timestamp
  sentBy?: string;
  title?: string;
};

const translations: Record<Language, {
  pageTitle: string;
  pageSubtitle: string;
  postedOn: string;
  byAdmin: string;
  noAnnouncements: string;
  errorLoading: string;
  chatWithKhanqahButton: string; // Added
}> = {
  en: {
    pageTitle: "Announcements",
    pageSubtitle: "Latest updates and messages from the Khanqah.",
    postedOn: "Posted on",
    byAdmin: "by Admin",
    noAnnouncements: "No announcements available at the moment.",
    errorLoading: "Failed to load announcements. Please try again later.",
    chatWithKhanqahButton: "Chat with Khanqah",
  },
  ur: {
    pageTitle: "اعلانات",
    pageSubtitle: "خانقاہ کی جانب سے تازہ ترین اپ ڈیٹس اور پیغامات۔",
    postedOn: "تاریخ اشاعت",
    byAdmin: "انتظامیہ کی جانب سے",
    noAnnouncements: "فی الحال کوئی اعلانات دستیاب نہیں ہیں۔",
    errorLoading: "اعلانات لوڈ کرنے میں ناکامی۔ براہ کرم کچھ دیر بعد دوبارہ کوشش کریں۔",
    chatWithKhanqahButton: "خانقاہ سے بات کریں",
  },
  ro: {
    pageTitle: "Aelanaat",
    pageSubtitle: "Khanqah ki jaanib se taaza tareen updates aur paighamaat.",
    postedOn: "Tareekh-e-Asha'at",
    byAdmin: "Intezaamia ki jaanib se",
    noAnnouncements: "Filhaal koi aelanaat dastiyaab nahin hain.",
    errorLoading: "Aelanaat load karne mein nakaami. Baraah-e-karam kuch der baad dobaara koshish karein.",
    chatWithKhanqahButton: "Khanqah se Baat Karein",
  },
  hi: {
    pageTitle: "ऐलानात",
    pageSubtitle: "खानक़ाह की जानिब से ताज़ा तरीन अपडेट्स और पैग़ामात।",
    postedOn: "प्रकाशन तिथि",
    byAdmin: "इंतज़ामिया की जानिब से",
    noAnnouncements: "फ़िलहाल कोई ऐलानात उपलब्ध नहीं हैं।",
    errorLoading: "ऐलानात लोड करने में विफलता। कृपया कुछ देर बाद दोबारा प्रयास करें।",
    chatWithKhanqahButton: "खानक़ाह से बात करें",
  },
  ar: {
    pageTitle: "الإعلانات",
    pageSubtitle: "آخر التحديثات والرسائل من الخانقاه.",
    postedOn: "نشر في",
    byAdmin: "من الإدارة",
    noAnnouncements: "لا توجد إعلانات متاحة في الوقت الحالي.",
    errorLoading: "فشل تحميل الإعلانات. يرجى المحاولة مرة أخرى في وقت لاحق.",
    chatWithKhanqahButton: "تحدث مع الخانقاه",
  },
};

export default function AnnouncementsPage() {
  const { language } = useLanguage();
  const [announcements, setAnnouncements] = useState<AdminAnnouncement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);

  const content = useMemo(() => translations[language] || translations.en, [language]);
  const isRtl = useMemo(() => language === 'ar' || language === 'ur', [language]);
  const fontClass = useMemo(() => isRtl ? 'font-arabic' : 'font-sans', [isRtl]);

  useEffect(() => {
    setIsLoading(true);
    setFetchError(null);

    const announcementsRef = collection(db, 'admin_broadcasts');
    const q = query(announcementsRef, orderBy('timestamp', 'desc'), limit(5));

    const unsubscribe = onSnapshot(q, 
      (querySnapshot) => {
        const fetchedAnnouncements: AdminAnnouncement[] = [];
        querySnapshot.forEach((doc: DocumentData) => {
          const data = doc.data();
          fetchedAnnouncements.push({
            id: doc.id,
            messageText: data.messageText,
            timestamp: data.timestamp, // Assuming timestamp is a Firestore Timestamp
            sentBy: data.sentBy,
            title: data.title,
          });
        });
        setAnnouncements(fetchedAnnouncements);
        setIsLoading(false);
      }, 
      (error) => {
        console.error("Error fetching announcements in real-time:", error);
        setFetchError(content.errorLoading);
        setIsLoading(false);
      }
    );

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [content.errorLoading, language]); // Added language to dependency array

  const getDateLocale = () => {
    switch (language) {
      case 'ar': return arSA;
      case 'ur': 
        console.warn("Urdu locale for date-fns not found, falling back to English for date formatting.");
        return enUS; 
      case 'hi': return hi;
      default: return enUS;
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-10rem)]">
        <LoadingSpinner size={48} />
        <p className={cn("mt-4 text-muted-foreground", fontClass)}>{translations[language]?.pageTitle || "Loading Announcements"}...</p>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="space-y-8">
        <PageTitle title={content.pageTitle} subtitle={content.pageSubtitle} className={cn(fontClass, isRtl ? 'text-right' : 'text-left')} />
        <ErrorMessage message={fetchError} />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <PageTitle title={content.pageTitle} subtitle={content.pageSubtitle} className={cn(fontClass, isRtl ? 'text-right' : 'text-left')} />
      
      <div className="my-6">
        <Link href="/chat/khanqah" passHref>
            <Button variant="outline" className={cn("w-full sm:w-auto py-3 text-base", fontClass)}>
                <MessageSquare className={cn("h-5 w-5", isRtl ? "ml-2" : "mr-2")} />
                {content.chatWithKhanqahButton}
            </Button>
        </Link>
      </div>

      {announcements.length === 0 ? (
        <Card className="text-center py-10">
          <CardContent>
            <Megaphone className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <p className={cn("text-muted-foreground", fontClass)}>{content.noAnnouncements}</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {announcements.map((announcement) => (
            <Card key={announcement.id} className={cn("shadow-lg hover:shadow-xl transition-shadow duration-300", "glass-effect")}>
              <CardHeader className={cn(isRtl ? 'text-right' : 'text-left')}>
                <div className={cn("flex items-center", isRtl && "justify-end flex-row-reverse")}>
                  <MessageSquare className={cn("h-6 w-6 text-primary", isRtl ? "ml-3" : "mr-3")} />
                  <h2 className={cn("text-xl font-semibold text-foreground", fontClass)}>
                    {announcement.title || content.pageTitle} {/* Fallback to page title if announcement title is missing */}
                  </h2>
                </div>
                <CardDescription className={cn("text-xs text-muted-foreground pt-1", fontClass)}>
                  {content.postedOn} {announcement.timestamp ? format(announcement.timestamp.toDate(), 'PPPp', { locale: getDateLocale() }) : 'N/A'} {announcement.sentBy && `${content.byAdmin}`}
                </CardDescription>
              </CardHeader>
              <CardContent className={cn("text-foreground/90", fontClass, isRtl ? 'text-right' : 'text-left')}>
                <p className="whitespace-pre-wrap">{announcement.messageText}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
    

    
