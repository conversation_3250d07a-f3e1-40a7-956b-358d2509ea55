
"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import PageTitle from '@/components/shared/PageTitle';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, BookOpen } from 'lucide-react';
import { useLanguage, type Language } from '@/context/LanguageContext';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import ErrorMessage from '@/components/shared/ErrorMessage';
import { cn } from '@/lib/utils';

type SurahInfo = {
  id: string; // slug
  arabicName: string;
  englishName: string;
  urduName: string;
  romanUrduName: string;
  hindiName: string;
};

const surahsList: SurahInfo[] = [
  { id: 'yaseen', arabicName: 'سورة يس', englishName: 'Surah Yaseen', urduName: 'سورہ یٰسین', romanUrduName: 'Surah Yaseen', hindiName: 'सूरह यासीन' },
  { id: 'al-mulk', arabicName: 'سورة الملك', englishName: 'Surah Al-Mulk', urduName: 'سورہ الملک', romanUrduName: 'Surah Al-Mulk', hindiName: 'सूरह अल-मुल्क' },
  { id: 'ar-rahman', arabicName: 'سورة الرحمن', englishName: 'Surah Ar-Rahman', urduName: 'سورہ الرحمٰن', romanUrduName: 'Surah Ar-Rahman', hindiName: 'सूरह अर-रहमान' },
  { id: 'al-waqiah', arabicName: 'سورة الواقعة', englishName: "Surah Al-Waqi'ah", urduName: 'سورہ الواقعہ', romanUrduName: "Surah Al-Waqi'ah", hindiName: 'सूरह अल-वाक़िआ' },
  { id: 'al-muzzammil', arabicName: 'سورة المزمل', englishName: 'Surah Al-Muzzammil', urduName: 'سورہ المزمل', romanUrduName: 'Surah Al-Muzzammil', hindiName: 'सूरह अल-मुज़्ज़म्मिल' },
];

// Full text of Surah Yaseen
const surahYaseenArabicFullText: string = `
اَعُوْذُ بِاللّٰہِ مِنَ الشَّیْطٰنِ الرَّجِیْمِ

بِسْمِ اللّٰهِ الرَّحْمٰنِ الرَّحِیْمِ

 یٰسٓ ۚ﴿۱﴾ وَ الْقُرْاٰنِ الْحَکِیۡمِ ۙ﴿۲﴾ اِنَّکَ لَمِنَ الْمُرْسَلِیۡنَ ۙ﴿۳﴾ عَلٰی صِرٰطٍ مُّسْتَقِیۡمٍ ؕ﴿۴﴾ تَنۡزِیۡلَ الْعَزِیۡزِ الرَّحِیۡمِ ۙ﴿۵﴾ لِتُنۡذِرَ قَوْمًا مَّاۤ اُنۡذِرَ اٰبَآؤُہُمْ فَہُمْ غٰفِلُوۡنَ ﴿۶﴾ لَقَدْ حَقَّ الْقَوْلُ عَلٰۤی اَکْثَرِہِمْ فَہُمْ لَا یُؤْمِنُوۡنَ ﴿۷﴾ اِنَّا جَعَلْنَا فِیۡۤ اَعْنٰقِہِمْ اَغْلٰلًا فَہِیَ اِلَی الۡاَذْقَانِ فَہُمۡ مُّقْمَحُوۡنَ ﴿۸﴾ وَ جَعَلْنَا مِنۡۢ بَیۡنِ اَیۡدِیۡہِمْ سَدًّا وَّ مِنْ خَلْفِہِمْ سَدًّا فَاَغْشَیۡنٰہُمْ فَہُمْ لَا یُبْصِرُوۡنَ ﴿۹﴾ وَ سَوَآءٌ عَلَیۡہِمْ ءَاَنۡذَرْتَہُمْ اَمْ لَمْ تُنۡذِرْہُمْ لَا یُؤْمِنُوۡنَ ﴿۱۰﴾ اِنَّمَا تُنۡذِرُ مَنِ اتَّبَعَ الذِّکْرَ وَ خَشِیَ الرَّحْمٰنَ بِالْغَیۡبِ ۚ فَبَشِّرْہُ بِمَغْفِرَۃٍ وَّ اَجْرٍ کَرِیۡمٍ ﴿۱۱﴾ اِنَّا نَحْنُ نُحْیِ الْمَوْتٰی وَ نَکْتُبُ مَا قَدَّمُوۡا وَ اٰثَارَہُمْ ؕ وَکُلَّ شَیۡءٍ اَحْصَیۡنٰہُ فِیۡۤ اِمَامٍ مُّبِیۡنٍ ﴿۱۲﴾ وَاضْرِبْ لَہُمۡ مَّثَلًا اَصْحٰبَ الْقَرْیَۃِ ۘ اِذْ جَآءَہَا الْمُرْسَلُوۡنَ ﴿۱۳﴾ اِذْ اَرْسَلْنَاۤ اِلَیۡہِمُ اثْنَیۡنِ فَکَذَّبُوۡہُمَا فَعَزَّزْنَا بِثَالِثٍ فَقَالُوۡۤا اِنَّاۤ اِلَیۡکُمۡ مُّرْسَلُوۡنَ ﴿۱۴﴾ قَالُوۡا مَاۤ اَنۡتُمْ اِلَّا بَشَرٌ مِّثْلُنَا ۙ وَ مَاۤ اَنۡزَلَ الرَّحْمٰنُ مِنۡ شَیۡءٍ ۙ اِنْ اَنۡتُمْ اِلَّا تَکْذِبُوۡنَ ﴿۱۵﴾ قَالُوۡا رَبُّنَا یَعْلَمُ اِنَّاۤ اِلَیۡکُمْ لَمُرْسَلُوۡنَ ﴿۱۶﴾ وَمَا عَلَیۡنَاۤ اِلَّا الْبَلٰغُ الْمُبِیۡنُ ﴿۱۷﴾ قَالُوۡۤا اِنَّا تَطَیَّرْنَا بِکُمْ ۚ لَئِنۡ لَّمْ تَنۡتَہُوۡا لَنَرْجُمَنَّکُمْ وَ لَیَمَسَّنَّکُمۡ مِّنَّا عَذَابٌ اَلِـیۡمٌ ﴿۱۸﴾ قَالُوۡا طٰٓئِرُکُمْ مَّعَکُمْ ؕ اَئِنۡ ذُکِّرْتُمۡ ؕ بَلْ اَنۡتُمْ قَوْمٌ مُّسْرِفُوۡنَ ﴿۱۹﴾ وَ جَآءَ مِنْ اَقْصَا الْمَدِیۡنَۃِ رَجُلٌ یَّسْعٰی۫ قَالَ یٰقَوْمِ اتَّبِعُوا الْمُرْسَلِیۡنَ ﴿۲۰﴾ اتَّبِعُوۡا مَنۡ لَّا یَسْـَٔلُکُمْ اَجْرًا وَّ ہُمۡ مُّہۡتَدُوۡنَ ﴿۲۱﴾  وَمَا لِیَ لَاۤ اَعْبُدُ الَّذِیۡ فَطَرَنِیۡ وَ اِلَیۡہِ تُرْجَعُوۡنَ ﴿۲۲﴾ ءَاَتَّخِذُ مِنۡ دُوۡنِہٖۤ اٰلِہَۃً اِنۡ یُّرِدْنِ الرَّحْمٰنُ بِضُرٍّ لَّا تُغْنِ عَنِّیۡ شَفٰعَتُہُمْ شَیْـًٔا وَّلَا یُنۡقِذُوۡنِ ﴿۲۳﴾ اِنِّیۡۤ اِذًا لَّفِیۡ ضَلٰلٍ مُّبِیۡنٍ ﴿۲۴﴾ اِنِّیۡۤ اٰمَنۡتُ بِرَبِّکُمْ فَاسْمَعُوۡنِ ﴿۲۵﴾ قِیۡلَ ادْخُلِ الْجَنَّۃَ ؕ قَالَ یٰلَیۡتَ قَوْمِیۡ یَعْلَمُوۡنَ ﴿۲۶﴾ بِمَا غَفَرَ لِیۡ رَبِّیۡ وَ جَعَلَنِیۡ مِنَ الْمُکْرَمِیۡنَ ﴿۲۷﴾ وَمَاۤ اَنۡزَلْنَا عَلٰی قَوْمِہٖ مِنۡۢ بَعْدِہٖ مِنْ جُنۡدٍ مِّنَ السَّمَآءِ وَمَا کُنَّا مُنۡزِلِیۡنَ ﴿۲۸﴾ اِنۡ کَانَتْ اِلَّا صَیۡحَۃً وّٰحِدَۃً فَاِذَا ہُمْ خٰمِدُوۡنَ ﴿۲۹﴾ یٰحَسْرَۃً عَلَی الْعِبَادِۚ مَا یَاۡتِیۡہِمۡ مِّنۡ رَّسُوۡلٍ اِلَّا کَانُوۡا بِہٖ یَسْتَہۡزِءُوۡنَ ﴿۳۰﴾ اَلَمْ یَرَوْا کَمْ اَہۡلَکْنَا قَبْلَہُمۡ مِّنَ الْقُرُوۡنِ اَنَّہُمْ اِلَیۡہِمْ لَا یَرْجِعُوۡنَ ﴿۳۱﴾ وَ اِنۡ کُلٌّ لَّمَّا جَمِیۡعٌ لَّدَیۡنَا مُحْضَرُوۡنَ ﴿۳۲﴾ وَ اٰیَۃٌ لَّہُمُ الْاَرْضُ الْمَیۡتَۃُ ۚۖ اَحْیَیۡنٰہَا وَ اَخْرَجْنَا مِنْہَا حَبًّا فَمِنْہُ یَاۡکُلُوۡنَ ﴿۳۳﴾ وَ جَعَلْنَا فِیۡہَا جَنّٰتٍ مِّنۡ نَّخِیۡلٍ وَّ اَعْنٰبٍ وَّ فَجَّرْنَا فِیۡہَا مِنَ الْعُیُوۡنِ ﴿۳۴﴾ لِیَاۡ کُلُوۡا مِنۡ ثَمَرِہٖ ۙ وَ مَا عَمِلَتْہُ اَیۡدِیۡہِمْ ؕ اَفَلَا یَشْکُرُوۡنَ ﴿۳۵﴾ سُبْحٰنَ الَّذِیۡ خَلَقَ الْاَزْوٰجَ کُلَّہَا مِمَّا تُنۡۢبِتُ الْاَرْضُ وَ مِنْ اَنۡفُسِہِمْ وَ مِمَّا لَا یَعْلَمُوۡنَ ﴿۳۶﴾ وَ اٰیَۃٌ لَّہُمُ الَّیۡلُ ۚۖ نَسْلَخُ مِنْہُ النَّہَارَ فَاِذَا ہُمۡ مُّظْلِمُوۡنَ ﴿۳۷﴾ وَ الشَّمْسُ تَجْرِیۡ لِمُسْتَقَرٍّ لَّہَا ؕ ذٰلِکَ تَقْدِیۡرُ الْعَزِیۡزِ الْعَلِیۡمِ ﴿۳۸﴾ وَ الْقَمَرَ قَدَّرْنٰہُ مَنَازِلَ حَتّٰی عَادَ کَالْعُرْجُوۡنِ الْقَدِیۡمِ ﴿۳۹﴾ لَا الشَّمْسُ یَنۡۢبَغِیۡ لَہَاۤ اَنۡ تُدْرِکَ الْقَمَرَ وَ لَا الَّیۡلُ سَابِقُ النَّہَارِ ؕ وَکُلٌّ فِیۡ فَلَکٍ یَّسْبَحُوۡنَ ﴿۴۰﴾  وَ اٰیَۃٌ لَّہُمْ اَنَّا حَمَلْنَا ذُرِّیَّتَہُمْ فِی الْفُلْکِ الْمَشْحُوۡنِ ﴿۴۱﴾ وَ خَلَقْنَا لَہُمۡ مِّنۡ مِّثْلِہٖ مَا یَرْکَبُوۡنَ ﴿۴۲﴾ وَ اِنۡ نَّشَاۡ نُغْرِقْہُمْ فَلَا صَرِیۡخَ لَہُمْ وَلَا ہُمْ یُنۡقَذُوۡنَ ﴿۴۳﴾ اِلَّا رَحْمَۃً مِّنَّا وَ مَتٰعًا اِلٰی حِیۡنٍ ﴿۴۴﴾ وَ اِذَا قِیۡلَ لَہُمُ اتَّقُوۡا مَا بَیۡنَ اَیۡدِیۡکُمْ وَمَا خَلْفَکُمْ لَعَلَّکُمْ تُرْحَمُوۡنَ ﴿۴۵﴾ وَمَا تَاۡتِیۡہِمۡ مِّنْ اٰیَۃٍ مِّنْ اٰیٰتِ رَبِّہِمْ اِلَّا کَانُوۡا عَنْہَا مُعْرِضِیۡنَ ﴿۴۶﴾ وَ اِذَا قِیۡلَ لَہُمْ اَنۡفِقُوۡا مِمَّا رَزَقَکُمُ اللہُ ۙ قَالَ الَّذِیۡنَ کَفَرُوۡا لِلَّذِیۡنَ اٰمَنُوۡۤا اَنُطْعِمُ مَنۡ لَّوْ یَشَآءُ اللہُ اَطْعَمَہٗۤ ٭ۖ اِنْ اَنۡتُمْ اِلَّا فِیۡ ضَلٰلٍ مُّبِیۡنٍ ﴿۴۷﴾ وَ یَقُوۡلُوۡنَ مَتٰی ہٰذَا الْوَعْدُ اِنۡ کُنۡتُمْ صٰدِقِیۡنَ ﴿۴۸﴾ مَا یَنۡظُرُوۡنَ اِلَّا صَیۡحَۃً وّٰحِدَۃً تَاۡخُذُہُمْ وَ ہُمْ یَخِصِّمُوۡنَ ﴿۴۹﴾ فَلَا یَسْتَطِیۡعُوۡنَ تَوْصِیَۃً وَّ لَاۤ اِلٰۤی اَہۡلِہِمْ یَرْجِعُوۡنَ ﴿۵۰﴾ وَ نُفِخَ فِی الصُّوۡرِ فَاِذَا ہُمۡ مِّنَ الْاَجْدَاثِ اِلٰی رَبِّہِمْ یَنۡسِلُوۡنَ ﴿۵۱﴾ قَالُوۡا یٰوَیۡلَنَا مَنۡۢ بَعَثَنَا مِنۡ مَّرْقَدِنَا ٜۘؐ ہٰذَا مَا وَعَدَ الرَّحْمٰنُ وَ صَدَقَ الْمُرْسَلُوۡنَ ﴿۵۲﴾ اِنۡ کَانَتْ اِلَّا صَیۡحَۃً وّٰحِدَۃً فَاِذَا ہُمْ جَمِیۡعٌ لَّدَیۡنَا مُحْضَرُوۡنَ ﴿۵۳﴾ فَالْیَوْمَ لَا تُظْلَمُ نَفْسٌ شَیْـًٔا وَّ لَا تُجْزَوْنَ اِلَّا مَا کُنۡتُمْ تَعْمَلُوۡنَ ﴿۵۴﴾  اِنَّ اَصْحٰبَ الْجَنَّۃِ الْیَوْمَ فِیۡ شُغُلٍ فٰکِہُوۡنَ ﴿۵۵﴾ ہُمْ وَ اَزْوٰجُہُمْ فِیۡ ظِلٰلٍ عَلَی الْاَرَآئِکِ مُتَّکِــُٔوۡنَ ﴿۵۶﴾ لَہُمْ فِیۡہَا فٰکِہَۃٌ وَّلَہُمْ مَّا یَدَّعُوۡنَ ﴿۵۷﴾ سَلٰمٌ ۟ قَوْلًا مِّنۡ رَّبٍّ رَّحِیۡمٍ ﴿۵۸﴾ وَامْتٰزُوا الْیَوْمَ اَیُّہَا الْمُجْرِمُوۡنَ ﴿۵۹﴾ اَلَمْ اَعْہَدْ اِلَیۡکُمْ یٰبَنِیۡۤ اٰدَمَ اَنۡ لَّا تَعْبُدُوا الشَّیۡطٰنَ ۚ اِنَّہٗ لَکُمْ عَدُوٌّ مُّبِیۡنٌ ﴿۶۰﴾ وَّ اَنِ اعْبُدُوۡنِیۡ ؕؔ ہٰذَا صِرٰطٌ مُّسْتَقِیۡمٌ ﴿۶۱﴾ وَلَقَدۡ اَضَلَّ مِنۡکُمۡ جِبِلًّا کَثِیۡرًا ؕ اَفَلَمۡ تَکُوۡنُوۡا تَعۡقِلُوۡنَ ﴿۶۲﴾ ہٰذِہٖ جَہَنَّمُ الَّتِیۡ کُنۡتُمۡ تُوۡعَدُوۡنَ ﴿۶۳﴾ اِصْلَوْہَا الْیَوْمَ بِمَا کُنۡتُمْ تَکْفُرُوۡنَ ﴿۶۴﴾ اَلْیَوْمَ نَخْتِمُ عَلٰۤی اَفْوٰہِہِمْ وَ تُکَلِّمُنَاۤ اَیۡدِیۡہِمْ وَ تَشْہَدُ اَرْجُلُہُمْ بِمَا کَانُوۡا یَکْسِبُوۡنَ ﴿۶۵﴾ وَ لَوْ نَشَآءُ لَطَمَسْنَا عَلٰۤی اَعْیُنِہِمْ فَاسْتَبَقُوا الصِّرٰطَ فَاَنّٰی یُبْصِرُوۡنَ ﴿۶۶﴾ وَلَوْ نَشَآءُ لَمَسَخْنٰہُمْ عَلٰی مَکَانَتِہِمْ فَمَا اسْتَطٰعُوۡا مُضِیًّا وَّلَا یَرْجِعُوۡنَ ﴿۶۷﴾ وَمَنْ نُّعَمِّرْہُ نُنَکِّسْہُ فِی الْخَلْقِ ؕ اَفَلَا یَعْقِلُوۡنَ ﴿۶۸﴾ وَمَا عَلَّمْنٰہُ الشِّعْرَ وَمَا یَنۡۢبَغِیۡ لَہٗ ؕ اِنْ ہُوَ اِلَّا ذِکْرٌ وَّ قُرْاٰنٌ مُّبِیۡنٌ ﴿۶۹﴾ لِّیُنۡذِرَ مَنۡ کَانَ حَیًّا وَّ یَحِقَّ الْقَوْلُ عَلَی الْکٰفِرِیۡنَ ﴿۷۰﴾  اَوَلَمْ یَرَوْا اَنَّا خَلَقْنَا لَہُمْ مِّمَّا عَمِلَتْ اَیۡدِیۡنَاۤ اَنْعٰمًا فَہُمْ لَہَا مٰلِکُوۡنَ ﴿۷۱﴾ وَ ذَلَّلْنٰہَا لَہُمْ فَمِنْہَا رَکُوۡبُہُمْ وَ مِنْہَا یَاۡکُلُوۡنَ ﴿۷۲﴾ وَ لَہُمْ فِیۡہَا مَنٰفِعُ وَ مَشَارِبُ ؕ اَفَلَا یَشْکُرُوۡنَ ﴿۷۳﴾ وَ اتَّخَذُوۡا مِنۡ دُوۡنِ اللہِ اٰلِہَۃً لَّعَلَّہُمْ یُنۡصَرُوۡنَ ﴿۷۴﴾ لَا یَسْتَطِیۡعُوۡنَ نَصْرَہُمْ ۙ وَہُمْ لَہُمْ جُنۡدٌ مُّحْضَرُوۡنَ ﴿۷۵﴾ فَلَا یَحْزُنۡکَ قَوْلُہُمْ ۘ اِنَّا نَعْلَمُ مَا یُسِرُّوۡنَ وَمَا یُعْلِنُوۡنَ ﴿۷۶﴾ اَوَلَمْ یَرَ الْاِنۡسٰنُ اَنَّا خَلَقْنٰہُ مِنۡ نُّطْفَۃٍ فَاِذَا ہُوَ خَصِیۡمٌ مُّبِیۡنٌ ﴿۷۷﴾ وَ ضَرَبَ لَنَا مَثَلًا وَّنَسِیَ خَلْقَہٗ ؕ قَالَ مَنْ یُّحْیِ الْعِظٰمَ وَ ہِیَ رَمِیۡمٌ ﴿۷۸﴾ قُلْ یُحْیِیۡہَا الَّذِیۡۤ اَنۡشَاَہَاۤ اَوَّلَ مَرَّۃٍ ؕ وَ ہُوَ بِکُلِّ خَلْقٍ عَلِیۡمُۨ  ﴿۷۹﴾ الَّذِیۡ جَعَلَ لَکُمۡ مِّنَ الشَّجَرِ الْاَخْضَرِ نَارًا فَاِذَاۤ اَنۡتُمۡ مِّنْہُ تُوۡقِدُوۡنَ ﴿۸۰﴾ اَوَ لَیۡسَ الَّذِیۡ خَلَقَ السَّمٰوٰتِ وَ الْاَرْضَ بِقٰدِرٍ عَلٰۤی اَنْ یَّخْلُقَ مِثْلَہُمۡ ؕؔ بَلٰی ٭ وَ ہُوَ الْخَلّٰقُ الْعَلِیۡمُ ﴿۸۱﴾ اِنَّمَاۤ اَمْرُہٗۤ اِذَاۤ اَرَادَ شَیْـًٔا اَنْ یَّقُوۡلَ لَہٗ کُنْ فَیَکُوۡنُ ﴿۸۲﴾ فَسُبْحٰنَ الَّذِیۡ بِیَدِہٖ مَلَکُوۡتُ کُلِّ شَیۡءٍ وَّ اِلَیۡہِ تُرْجَعُوۡنَ ﴿۸۳﴾
`;

const pageTranslations = {
  backButton: {
    en: "Back to Panj Soorah",
    ur: "پنج سورہ پر واپس",
    ro: "Panj Soorah Par Wapas",
    hi: "पंज सूरह पर वापस",
    ar: "العودة إلى السور الخمس",
  },
  contentComingSoon: {
    en: "Full text and translations for this Surah will appear here. Please use a verified Quranic source for accurate text.",
    ur: "اس سورہ کا مکمل عربی متن اور اردو ترجمہ جلد ہی یہاں ظاہر ہوگا۔ براہ کرم مستند قرآنی ماخذ سے رجوع کریں۔",
    ro: "Is Surah ka mukammal Arabi matan aur tarjuma/transliteration jald hi yahan zahir hoga. Barah-e-karam mustanad Qurani maakhaz se rujoo karein.",
    hi: "इस सूरह का पूरा अरबी पाठ और अनुवाद/लिप्यंतरण जल्द ही यहाँ प्रदर्शित होगा। कृपया सटीक पाठ के लिए एक सत्यापित कुरानिक स्रोत का उपयोग करें।",
    ar: "سيظهر النص العربي الكامل لهذه السورة وترجمتها قريبًا هنا. يرجى استخدام مصدر قرآني موثوق للنص الدقيق.",
  },
  arabicTextHeading: {
    en: "Arabic Text",
    ur: "متن عربی",
    ro: "Arabi Matan",
    hi: "अरबी पाठ",
    ar: "النص العربي",
  },
  kanzulImanUrduHeading: {
    en: "Kanzul Iman Urdu Translation",
    ur: "کنز الایمان ترجمہ",
    ro: "Kanzul Iman Tarjuma",
    hi: "कंजुल ईमान अनुवाद",
    ar: "ترجمة كنز الإيمان (الأردية)",
  },
  englishTranslationHeading: {
    en: "English Translation",
    ur: "انگریزی ترجمہ",
    ro: "English Tarjuma",
    hi: "अंग्रेजी अनुवाद",
    ar: "الترجمة الإنجليزية",
  },
  hindiTransliterationHeading: {
    en: "Hindi Transliteration/Translation",
    ur: "ہندی نقل حرفی/ترجمہ",
    ro: "Hindi Transliteration/Tarjuma",
    hi: "हिन्दी लिप्यंतरण/अनुवाद",
    ar: "النقل الحرفي/الترجمة الهندية",
  },
  romanUrduTransliterationHeading: {
    en: "Roman Urdu Transliteration/Translation",
    ur: "رومن اردو نقل حرفی/ترجمہ",
    ro: "Roman Urdu Transliteration/Tarjuma",
    hi: "रोमन उर्दू लिप्यंतरण/अनुवाद",
    ar: "النقل الحرفي/الترجمة الرومانية الأردية",
  }
};

export default function SurahDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { language } = useLanguage();
  const { surahSlug } = params;

  const [currentSurah, setCurrentSurah] = useState<SurahInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setIsLoading(true);
    if (typeof surahSlug === 'string') {
      const foundSurah = surahsList.find(s => s.id === surahSlug);
      if (foundSurah) {
        setCurrentSurah(foundSurah);
      } else {
        setError("Surah not found.");
        // router.replace('/panjsoora'); // Optionally redirect
      }
    } else {
      setError("Invalid Surah identifier.");
      // router.replace('/panjsoora'); // Optionally redirect
    }
    setIsLoading(false);
  }, [surahSlug, router]);

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-[calc(100vh-10rem)]"><LoadingSpinner size={48} /></div>;
  }

  if (error || !currentSurah) {
    return (
      <div className="space-y-8">
        <PageTitle title="Error" subtitle={error || "Could not load Surah."} />
        <ErrorMessage message={error || "The requested Surah could not be found or loaded."} />
        <Link href="/panjsoora" passHref>
          <Button variant="outline">
            <ArrowRight className={cn("h-4 w-4 transform", language === 'ur' || language === 'ar' ? 'ml-1.5 rotate-0' : 'mr-1.5 rotate-180')} />
            {pageTranslations.backButton[language] || pageTranslations.backButton.en}
          </Button>
        </Link>
      </div>
    );
  }

  const getSurahName = (surah: SurahInfo) => {
    switch (language) {
      case 'ur': return surah.urduName;
      case 'ro': return surah.romanUrduName;
      case 'hi': return surah.hindiName;
      case 'ar': return surah.arabicName;
      case 'en':
      default: return surah.englishName;
    }
  };

  const isRtl = language === 'ur' || language === 'ar';
  const fontClass = isRtl ? 'font-arabic' : 'font-sans';
  const textDir = isRtl ? 'rtl' : 'ltr';
  
  const currentBackButtonText = pageTranslations.backButton[language] || pageTranslations.backButton.en;
  const currentPlaceholderText = pageTranslations.contentComingSoon[language] || pageTranslations.contentComingSoon.en;
  
  const getPlaceholderTextForLang = (lang: Language, surahId: string) => {
    const surahInfo = surahsList.find(s => s.id === surahId);
    if (!surahInfo) return currentPlaceholderText;

    const surahDisplayName = getSurahName(surahInfo);

    switch(lang) {
      case 'en': return `English translation of ${surahDisplayName} will be added here soon.`;
      case 'ur': return `${surahDisplayName} کا کنز الایمان اردو ترجمہ جلد ہی یہاں شامل کیا جائے گا۔`;
      case 'ro': return `${surahDisplayName} ka Roman Urdu transliteration/tarjuma jald hi yahan shaamil kiya jayega.`;
      case 'hi': return `${surahDisplayName} का हिन्दी लिप्यंतरण/अनुवाद जल्द ही यहाँ शामिल किया जाएगा।`;
      case 'ar': return `سيتم إضافة ترجمات ${surahDisplayName} إلى اللغات الأخرى قريبًا.`;
      default: return currentPlaceholderText;
    }
  };

  return (
    <div className="space-y-6">
       <div className="px-0">
        {/* "Back to Panj Soorah" link removed as per user request */}
        <PageTitle 
          title={getSurahName(currentSurah)} 
          className={cn(fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')} 
        />
      </div>

      <Card className="shadow-xl bg-card">
        <CardHeader className={cn("items-center", textDir === 'rtl' && 'text-right')}>
           <BookOpen className={cn("h-8 w-8 text-primary mb-2", isRtl && "ml-3")} />
        </CardHeader>
        <CardContent
          lang={language} 
          dir={textDir} 
          className={cn("space-y-6 text-foreground/90 text-base md:text-lg leading-relaxed", fontClass)}
        >
          {currentSurah.id === 'yaseen' ? (
            <>
              {/* Arabic Text Section (Always shown for Yaseen) */}
              <section aria-labelledby={`${currentSurah.id}-arabic-heading`}>
                <h3 id={`${currentSurah.id}-arabic-heading`} className="text-lg font-semibold mt-2 mb-2 text-primary font-arabic text-right sr-only" lang="ar" dir="rtl">
                  {pageTranslations.arabicTextHeading.ur} {/* Using Urdu label for Arabic text as it's common, hidden for screen readers as text is obvious */}
                </h3>
                 <div 
                    lang="ar" 
                    dir="rtl" 
                    className={cn("font-arabic text-xl md:text-2xl leading-loose whitespace-pre-line", "book-page-arabic")}
                  >
                    <p className="text-center font-semibold mb-2">{surahYaseenArabicFullText.split('\n')[1].trim()}</p> {/* Ta'awwuz */}
                    <p className="text-center font-semibold mb-4">{surahYaseenArabicFullText.split('\n')[3].trim()}</p> {/* Bismillah (index 3 due to empty line) */}
                    {surahYaseenArabicFullText.split('\n').slice(5).join('\n').trim()} {/* Main Surah text */}
                  </div>
              </section>
              
              {/* Language-Specific Placeholders for Translations/Transliterations */}
              {language === 'ur' && (
                <section aria-labelledby={`${currentSurah.id}-kanzuliman-heading-ur`} className="mt-6">
                  <h3 id={`${currentSurah.id}-kanzuliman-heading-ur`} className="text-lg font-semibold mt-4 mb-2 text-primary font-arabic text-right" lang="ur" dir="rtl">
                    {pageTranslations.kanzulImanUrduHeading.ur}
                  </h3>
                  <div lang="ur" dir="rtl" className="font-arabic text-lg md:text-xl leading-relaxed p-3 bg-muted/40 rounded-md shadow-inner">
                     <p className="italic text-muted-foreground text-center p-2">
                      {getPlaceholderTextForLang('ur', currentSurah.id)}
                    </p>
                  </div>
                </section>
              )}
              {(language === 'en' || language === 'ro' || language === 'hi') && (
                <section aria-labelledby={`${currentSurah.id}-${language}-heading`} className="mt-6">
                    <h3 id={`${currentSurah.id}-${language}-heading`} className={cn("text-lg font-semibold mt-4 mb-2 text-primary", fontClass, textDir === 'rtl' ? 'text-right' : 'text-left')}>
                        { language === 'en' ? pageTranslations.englishTranslationHeading[language] : 
                          language === 'ro' ? pageTranslations.romanUrduTransliterationHeading[language] : 
                          pageTranslations.hindiTransliterationHeading[language] }
                    </h3>
                    <p className="italic text-muted-foreground text-center p-4 border border-dashed border-border rounded-md mt-4">
                      {getPlaceholderTextForLang(language, currentSurah.id)}
                    </p>
                </section>
              )}
            </>
          ) : ( // For Surahs other than Yaseen
            <p className="italic text-muted-foreground text-center p-4 border border-dashed border-border rounded-md">
              {getPlaceholderTextForLang(language, currentSurah.id)}
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
