
"use client";

import type { FC } from 'react';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext'; // To determine current user

export interface ChatMessageProps {
  messageId: string;
  text: string;
  timestamp: string | Date; // ISO string or Date object
  senderId: string;
  senderName?: string; // Display name of the sender
  senderAvatar?: string; // URL for sender's avatar
  isCurrentUser: boolean; // Explicitly pass if the message is from the current user
}

const ChatMessage: FC<ChatMessageProps> = ({
  text,
  timestamp,
  senderName,
  senderAvatar,
  isCurrentUser,
}) => {
  const formattedTimestamp = typeof timestamp === 'string' 
    ? formatDistanceToNow(parseISO(timestamp), { addSuffix: true })
    : formatDistanceToNow(timestamp, { addSuffix: true });

  const getInitials = (name?: string) => {
    if (!name) return 'U';
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  return (
    <div
      className={cn(
        "flex items-end gap-2 sm:gap-3 w-full max-w-[85%] sm:max-w-[75%] my-2",
        isCurrentUser ? "ml-auto flex-row-reverse" : "mr-auto"
      )}
    >
      {!isCurrentUser && (
        <Avatar className="h-8 w-8 sm:h-9 sm:w-9">
          <AvatarImage src={senderAvatar} alt={senderName || 'User Avatar'} />
          <AvatarFallback>{getInitials(senderName)}</AvatarFallback>
        </Avatar>
      )}
      <div
        className={cn(
          "flex flex-col rounded-xl px-3 py-2 text-sm sm:text-base shadow-md",
          isCurrentUser
            ? "bg-primary text-primary-foreground rounded-br-none"
            : "bg-card text-card-foreground rounded-bl-none border border-border"
        )}
      >
        {!isCurrentUser && senderName && (
          <span className="text-xs font-semibold mb-0.5 text-muted-foreground">
            {senderName}
          </span>
        )}
        <p className="whitespace-pre-wrap break-words">{text}</p>
        <span
          className={cn(
            "mt-1 text-xs",
            isCurrentUser ? "text-primary-foreground/70 text-right" : "text-muted-foreground text-left"
          )}
        >
          {formattedTimestamp}
        </span>
      </div>
    </div>
  );
};

export default ChatMessage;
