
import type { ReactNode } from 'react';

type PageTitleProps = {
  title: string;
  subtitle?: string | ReactNode;
  className?: string;
};

export default function PageTitle({ title, subtitle, className }: PageTitleProps) {
  return (
    <div className={`mb-6 ${className}`}>
      <h1 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
        {title}
      </h1>
      {subtitle && (
        <p className="mt-2 text-lg text-muted-foreground">
          {subtitle}
        </p>
      )}
    </div>
  );
}
