import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import contexts
import { LanguageProvider } from './src/context/LanguageContext';
import { FontSizeProvider } from './src/context/FontSizeContext';
import { AuthProvider } from './src/context/AuthContext';

// Import navigation
import RootNavigator from './src/navigation/RootNavigator';

export default function App() {
  return (
    <SafeAreaProvider>
      <LanguageProvider>
        <FontSizeProvider>
          <AuthProvider>
            <NavigationContainer>
              <RootNavigator />
            </NavigationContainer>
            <StatusBar style="auto" />
          </AuthProvider>
        </FontSizeProvider>
      </LanguageProvider>
    </SafeAreaProvider>
  );
}
