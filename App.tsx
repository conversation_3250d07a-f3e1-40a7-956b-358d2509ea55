import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import contexts
import { LanguageProvider } from './src/context/LanguageContext';
import { FontSizeProvider } from './src/context/FontSizeContext';
import { AuthProvider } from './src/context/AuthContext';

// Import screens
import SimpleHomeScreen from './src/screens/SimpleHomeScreen';

export default function App() {
  return (
    <SafeAreaProvider>
      <LanguageProvider>
        <FontSizeProvider>
          <AuthProvider>
            <SimpleHomeScreen />
            <StatusBar style="auto" />
          </AuthProvider>
        </FontSizeProvider>
      </LanguageProvider>
    </SafeAreaProvider>
  );
}
