import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import contexts
import { LanguageProvider } from './src/context/LanguageContext';
import { FontSizeProvider } from './src/context/FontSizeContext';
import { AuthProvider } from './src/context/AuthContext';

// Import main app navigator directly
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <SafeAreaProvider>
      <LanguageProvider>
        <FontSizeProvider>
          <AuthProvider>
            <AppNavigator />
            <StatusBar style="auto" />
          </AuthProvider>
        </FontSizeProvider>
      </LanguageProvider>
    </SafeAreaProvider>
  );
}
