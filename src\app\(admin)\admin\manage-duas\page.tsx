
"use client";

import { useState, useEffect } from 'react';
import PageTitle from '@/components/shared/PageTitle';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'; // DialogFooter, DialogClose removed as DialogContent handles close
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { duasData as initialDuasData, type Dua } from '@/data/duas'; // Using mock data
import DuaF<PERSON>, { type DuaFormData } from '@/components/admin/DuaForm';
import LoadingSpinner from '@/components/shared/LoadingSpinner';
import { useToast } from "@/hooks/use-toast";
import { cn } from '@/lib/utils';
import { AnimatePresence } from 'framer-motion'; // Import AnimatePresence

export default function ManageDuasPage() {
  const [duas, setDuas] = useState<Dua[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingDua, setEditingDua] = useState<Dua | null>(null);
  const [duaToDelete, setDuaToDelete] = useState<Dua | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    setDuas(initialDuasData);
    setLoading(false);
  }, []);

  const handleFormSubmit = (data: DuaFormData, originalDuaId?: string) => {
    if (editingDua && originalDuaId) {
      setDuas(prevDuas =>
        prevDuas.map(dua =>
          dua.id === originalDuaId ? { ...dua, ...data, id: originalDuaId } : dua
        )
      );
      toast({ title: "Dua Updated", description: `"${data.title}" has been successfully updated.` });
    } else {
      const newDua: Dua = {
        ...data,
        id: String(Date.now()), 
      };
      setDuas(prevDuas => [newDua, ...prevDuas]);
      toast({ title: "Dua Created", description: `"${newDua.title}" has been successfully created.` });
    }
    setIsFormOpen(false);
    setEditingDua(null);
  };

  const handleAddNewDua = () => {
    setEditingDua(null);
    setIsFormOpen(true);
  };

  const handleEditDua = (dua: Dua) => {
    setEditingDua(dua);
    setIsFormOpen(true);
  };

  const handleDeleteDua = () => {
    if (duaToDelete) {
      setDuas(prevDuas => prevDuas.filter(d => d.id !== duaToDelete.id));
      toast({ title: "Dua Deleted", description: `"${duaToDelete.title}" has been successfully deleted.`, variant: "destructive" });
      setDuaToDelete(null);
    }
  };


  if (loading) {
    return (
      <div className="flex justify-center items-center py-10">
        <LoadingSpinner size={32} /> <span className="ml-2">Loading Duas...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageTitle title="Manage Duas & Wazaif" subtitle="Add, edit, or remove duas." />
        {/* DialogTrigger is outside AnimatePresence, Dialog and DialogContent are inside */}
        <DialogTrigger asChild>
          <Button onClick={handleAddNewDua}>
            <PlusCircle className="h-4 w-4 mr-2" /> Add New Dua
          </Button>
        </DialogTrigger>
      </div>
      
      <AnimatePresence mode="wait">
        {isFormOpen && (
          <Dialog open={isFormOpen} onOpenChange={(isOpen) => {
              if (!isOpen) { // Only trigger on close intent
                setIsFormOpen(false);
                // Add a slight delay to allow exit animation to complete before resetting state
                setTimeout(() => setEditingDua(null), 300); 
              } else {
                setIsFormOpen(true); // Handle explicit open
              }
          }}>
            <DialogContent className={cn("sm:max-w-[600px]", "glass-effect")}>
              <DialogHeader>
                <DialogTitle>{editingDua ? 'Edit Dua' : 'Create New Dua'}</DialogTitle>
                <DialogDescription>
                  {editingDua ? 'Modify the details of the existing dua.' : 'Fill in the details for the new dua.'}
                </DialogDescription>
              </DialogHeader>
              <DuaForm
                onSubmit={handleFormSubmit}
                onCancel={() => {
                  setIsFormOpen(false);
                   setTimeout(() => setEditingDua(null), 300);
                }}
                initialData={editingDua}
                isEditing={!!editingDua}
              />
            </DialogContent>
          </Dialog>
        )}
      </AnimatePresence>


      {duas.length === 0 ? (
        <p className="text-muted-foreground text-center py-10">No duas found. Add a new dua to get started.</p>
      ) : (
        <div className="border rounded-lg shadow-sm">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">ID</TableHead>
                <TableHead>Title (Urdu/English)</TableHead>
                <TableHead>Slug</TableHead>
                <TableHead>Recitation Time</TableHead>
                <TableHead className="text-right w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {duas.map(dua => (
                <TableRow key={dua.id}>
                  <TableCell className="font-medium">{dua.id}</TableCell>
                  <TableCell>{dua.title}</TableCell>
                  <TableCell>{dua.slug}</TableCell>
                  <TableCell>{dua.recitationTime || 'N/A'}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon" onClick={() => handleEditDua(dua)} className="mr-2">
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={() => setDuaToDelete(dua)}>
                          <Trash2 className="h-4 w-4 text-destructive" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the dua titled
                            <strong className="mx-1">"{duaToDelete?.title}"</strong>.
                          </DialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel onClick={() => setDuaToDelete(null)}>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={handleDeleteDua} className="bg-destructive hover:bg-destructive/90">
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}
