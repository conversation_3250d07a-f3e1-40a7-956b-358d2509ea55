{"version": 3, "names": ["useTheme", "React", "Platform", "ScreenStackHeaderBackButtonImage", "ScreenStackHeaderCenterView", "ScreenStackHeaderConfig", "ScreenStackHeaderLeftView", "ScreenStackHeaderRightView", "ScreenStackHeaderSearchBarView", "SearchBar", "isSearchBarAvailableForCurrentPlatform", "executeNativeBackPress", "useBackPressSubscription", "processFonts", "warnOnce", "HeaderConfig", "_ref", "backButtonImage", "backButtonInCustomView", "direction", "disableBackButtonMenu", "headerBackTitle", "headerBackTitleStyle", "headerBackTitleVisible", "headerCenter", "headerHideBackButton", "headerHideShadow", "headerLargeStyle", "headerLargeTitle", "headerLargeTitleHideShadow", "headerLargeTitleStyle", "headerLeft", "headerRight", "headerShown", "headerStyle", "headerTintColor", "headerTitle", "headerTitleStyle", "headerTopInsetEnabled", "headerTranslucent", "route", "searchBar", "title", "colors", "tintColor", "primary", "handleAttached", "handleDetached", "clearSubscription", "createSubscription", "onBackPress", "isDisabled", "disableBackButtonOverride", "backTitleFontFamily", "largeTitleFontFamily", "titleFontFamily", "fontFamily", "useEffect", "processedSearchBarOptions", "useMemo", "OS", "onFocus", "_len", "arguments", "length", "args", "Array", "_key", "onClose", "_len2", "_key2", "isVisionOS", "isVision", "color", "undefined", "createElement", "backgroundColor", "card", "backTitle", "backTitleFontSize", "fontSize", "backTitleVisible", "blurEffect", "hidden", "hideBackButton", "hideShadow", "largeTitle", "largeTitleBackgroundColor", "largeTitleColor", "largeTitleFontSize", "largeTitleFontWeight", "fontWeight", "largeTitleHideShadow", "name", "titleColor", "text", "titleFontSize", "titleFontWeight", "topInsetEnabled", "translucent", "onAttached", "onDetached", "key", "source"], "sourceRoot": "../../../../src", "sources": ["native-stack/views/HeaderConfig.tsx"], "mappings": "AAAA,SAAgBA,QAAQ,QAAQ,0BAA0B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,cAAc;AACvC,SACEC,gCAAgC,EAChCC,2BAA2B,EAC3BC,uBAAuB,EACvBC,yBAAyB,EACzBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,SAAS,EAETC,sCAAsC,EACtCC,sBAAsB,QACjB,sBAAsB;AAE7B,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,QAAQ,MAAM,WAAW;AAMhC,eAAe,SAASC,YAAYA,CAAAC,IAAA,EA2Bb;EAAA,IA3Bc;IACnCC,eAAe;IACfC,sBAAsB;IACtBC,SAAS;IACTC,qBAAqB;IACrBC,eAAe;IACfC,oBAAoB,GAAG,CAAC,CAAC;IACzBC,sBAAsB,GAAG,IAAI;IAC7BC,YAAY;IACZC,oBAAoB;IACpBC,gBAAgB;IAChBC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,gBAAgB;IAChBC,0BAA0B;IAC1BC,qBAAqB,GAAG,CAAC,CAAC;IAC1BC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,WAAW,GAAG,CAAC,CAAC;IAChBC,eAAe;IACfC,WAAW;IACXC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,qBAAqB,GAAG,IAAI;IAC5BC,iBAAiB;IACjBC,KAAK;IACLC,SAAS;IACTC;EACK,CAAC,GAAA1B,IAAA;EACN,MAAM;IAAE2B;EAAO,CAAC,GAAG3C,QAAQ,CAAC,CAAC;EAC7B,MAAM4C,SAAS,GAAGT,eAAe,IAAIQ,MAAM,CAACE,OAAO;;EAEnD;EACA;EACA;EACA,MAAM;IACJC,cAAc;IACdC,cAAc;IACdC,iBAAiB;IACjBC;EACF,CAAC,GAAGrC,wBAAwB,CAAC;IAC3BsC,WAAW,EAAEvC,sBAAsB;IACnCwC,UAAU,EAAE,CAACV,SAAS,IAAI,CAAC,CAACA,SAAS,CAACW;EACxC,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,CAAC,GAChE1C,YAAY,CAAC,CACXS,oBAAoB,CAACkC,UAAU,EAC/B1B,qBAAqB,CAAC0B,UAAU,EAChCnB,gBAAgB,CAACmB,UAAU,CAC5B,CAAC;;EAEJ;EACA;EACAvD,KAAK,CAACwD,SAAS,CAAC,MAAMT,iBAAiB,EAAE,CAACP,SAAS,CAAC,CAAC;EAErD,MAAMiB,yBAAyB,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IACpD,IACEzD,QAAQ,CAAC0D,EAAE,KAAK,SAAS,IACzBnB,SAAS,IACT,CAACA,SAAS,CAACW,yBAAyB,EACpC;MACA,MAAMS,OAAkC,GAAG,SAAAA,CAAA,EAAa;QACtDZ,kBAAkB,CAAC,CAAC;QAAC,SAAAa,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADwBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;UAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;QAAA;QAEjD1B,SAAS,CAACoB,OAAO,GAAG,GAAGI,IAAI,CAAC;MAC9B,CAAC;MACD,MAAMG,OAAkC,GAAG,SAAAA,CAAA,EAAa;QACtDpB,iBAAiB,CAAC,CAAC;QAAC,SAAAqB,KAAA,GAAAN,SAAA,CAAAC,MAAA,EADyBC,IAAI,OAAAC,KAAA,CAAAG,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAAJL,IAAI,CAAAK,KAAA,IAAAP,SAAA,CAAAO,KAAA;QAAA;QAEjD7B,SAAS,CAAC2B,OAAO,GAAG,GAAGH,IAAI,CAAC;MAC9B,CAAC;MAED,OAAO;QAAE,GAAGxB,SAAS;QAAEoB,OAAO;QAAEO;MAAQ,CAAC;IAC3C;IACA,OAAO3B,SAAS;EAClB,CAAC,EAAE,CAACA,SAAS,EAAEQ,kBAAkB,EAAED,iBAAiB,CAAC,CAAC;;EAEtD;EACA,MAAMuB,UAAU,GAAGrE,QAAQ,EAAEsE,QAAQ;EAErC1D,QAAQ,CACNyD,UAAU,KACPlC,gBAAgB,CAACoC,KAAK,KAAKC,SAAS,IAAIvC,eAAe,KAAKuC,SAAS,CAAC,EACzE,2EACF,CAAC;EAED,oBACEzE,KAAA,CAAA0E,aAAA,CAACtE,uBAAuB;IACtBa,sBAAsB,EAAEA,sBAAuB;IAC/C0D,eAAe,EACb1C,WAAW,CAAC0C,eAAe,GAAG1C,WAAW,CAAC0C,eAAe,GAAGjC,MAAM,CAACkC,IACpE;IACDC,SAAS,EAAEzD,eAAgB;IAC3BgC,mBAAmB,EAAEA,mBAAoB;IACzC0B,iBAAiB,EAAEzD,oBAAoB,CAAC0D,QAAS;IACjDC,gBAAgB,EAAE1D,sBAAuB;IACzC2D,UAAU,EAAEhD,WAAW,CAACgD,UAAW;IACnCT,KAAK,EAAE7B,SAAU;IACjBzB,SAAS,EAAEA,SAAU;IACrBC,qBAAqB,EAAEA,qBAAsB;IAC7C+D,MAAM,EAAElD,WAAW,KAAK,KAAM;IAC9BmD,cAAc,EAAE3D,oBAAqB;IACrC4D,UAAU,EAAE3D,gBAAiB;IAC7B4D,UAAU,EAAE1D,gBAAiB;IAC7B2D,yBAAyB,EAAE5D,gBAAgB,CAACiD,eAAgB;IAC5DY,eAAe,EAAE1D,qBAAqB,CAAC2C,KAAM;IAC7CnB,oBAAoB,EAAEA,oBAAqB;IAC3CmC,kBAAkB,EAAE3D,qBAAqB,CAACkD,QAAS;IACnDU,oBAAoB,EAAE5D,qBAAqB,CAAC6D,UAAW;IACvDC,oBAAoB,EAAE/D,0BAA2B;IACjDa,KAAK,EACHN,WAAW,KAAKsC,SAAS,GACrBtC,WAAW,GACXM,KAAK,KAAKgC,SAAS,GACnBhC,KAAK,GACLF,KAAK,CAACqD,IACX;IACDC,UAAU,EACRzD,gBAAgB,CAACoC,KAAK,KAAKC,SAAS,GAChCrC,gBAAgB,CAACoC,KAAK,GACtBtC,eAAe,KAAKuC,SAAS,GAC7BvC,eAAe,GACfQ,MAAM,CAACoD,IACZ;IACDxC,eAAe,EAAEA,eAAgB;IACjCyC,aAAa,EAAE3D,gBAAgB,CAAC2C,QAAS;IACzCiB,eAAe,EAAE5D,gBAAgB,CAACsD,UAAW;IAC7CO,eAAe,EAAE5D,qBAAsB;IACvC6D,WAAW,EAAE5D,iBAAiB,KAAK,IAAK;IACxC6D,UAAU,EAAEtD,cAAe;IAC3BuD,UAAU,EAAEtD;EAAe,GAC1Bf,WAAW,KAAK0C,SAAS,gBACxBzE,KAAA,CAAA0E,aAAA,CAACpE,0BAA0B,QACxByB,WAAW,CAAC;IAAEY;EAAU,CAAC,CACA,CAAC,GAC3B,IAAI,EACP3B,eAAe,KAAKyD,SAAS,gBAC5BzE,KAAA,CAAA0E,aAAA,CAACxE,gCAAgC;IAC/BmG,GAAG,EAAC,WAAW;IACfC,MAAM,EAAEtF;EAAgB,CACzB,CAAC,GACA,IAAI,EACPc,UAAU,KAAK2C,SAAS,gBACvBzE,KAAA,CAAA0E,aAAA,CAACrE,yBAAyB,QACvByB,UAAU,CAAC;IAAEa;EAAU,CAAC,CACA,CAAC,GAC1B,IAAI,EACPpB,YAAY,KAAKkD,SAAS,gBACzBzE,KAAA,CAAA0E,aAAA,CAACvE,2BAA2B,QACzBoB,YAAY,CAAC;IAAEoB;EAAU,CAAC,CACA,CAAC,GAC5B,IAAI,EACPlC,sCAAsC,IACvCgD,yBAAyB,KAAKgB,SAAS,gBACrCzE,KAAA,CAAA0E,aAAA,CAACnE,8BAA8B,qBAE7BP,KAAA,CAAA0E,aAAA,CAAClE,SAAS,EAAKiD,yBAA4B,CACb,CAAC,GAC/B,IACmB,CAAC;AAE9B"}