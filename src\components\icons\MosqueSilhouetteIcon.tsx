import type { SVGProps } from 'react';

export default function MosqueSilhouetteIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" {...props}>
      {/* Main dome-like structure */}
      <path
        d="M15 90 H85 V65 C85 40 70 30 50 15 C30 30 15 40 15 65 V90 Z"
        fill="currentColor"
      />
      {/* Small circle on top, representing a finial or crescent moon */}
      <path
        d="M50 5 A5 5 0 1 1 49.9 15 A5 5 0 1 1 50 5 Z" /* Adjusted to be slightly separated */
        fill="currentColor"
      />
    </svg>
  );
}
