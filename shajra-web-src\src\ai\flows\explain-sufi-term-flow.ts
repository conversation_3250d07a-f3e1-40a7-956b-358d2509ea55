'use server';
/**
 * @fileOverview Explains a Sufi term.
 *
 * - explainSufiTerm - A function that explains a given Sufi term.
 * - ExplainSufiTermInput - The input type for the explainSufiTerm function.
 * - ExplainSufiTermOutput - The return type for the explainSufiTerm function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ExplainSufiTermInputSchema = z.object({
  term: z.string().min(1, {message: "Term cannot be empty."}).describe('The Sufi term to be explained.'),
});
export type ExplainSufiTermInput = z.infer<typeof ExplainSufiTermInputSchema>;

const ExplainSufiTermOutputSchema = z.object({
  explanation: z.string().describe('A simple explanation of the Sufi term suitable for a general audience.'),
});
export type ExplainSufiTermOutput = z.infer<typeof ExplainSufiTermOutputSchema>;

export async function explainSufiTerm(input: ExplainSufiTermInput): Promise<ExplainSufiTermOutput> {
  return explainSufiTermFlow(input);
}

const prompt = ai.definePrompt({
  name: 'explainSufiTermPrompt',
  input: { schema: ExplainSufiTermInputSchema },
  output: { schema: ExplainSufiTermOutputSchema },
  prompt: `You are a knowledgeable guide on Sufism.
Explain the Sufi term "{{{term}}}" in simple, clear, and concise language (around 2-4 sentences) suitable for a general audience unfamiliar with specialized terminology.
Focus on its core meaning and significance in the context of Sufi teachings.
Avoid overly academic or obscure language.
For example, if the term is "Fana", explain it as the spiritual concept of annihilation of the self in God.
If the term is "Murshid", explain it as a spiritual guide or teacher in the Sufi path.
Term to explain: {{{term}}}`,
});

const explainSufiTermFlow = ai.defineFlow(
  {
    name: 'explainSufiTermFlow',
    inputSchema: ExplainSufiTermInputSchema,
    outputSchema: ExplainSufiTermOutputSchema,
  },
  async (input) => {
    const {output} = await prompt(input);
     if (!output) {
        throw new Error(`Failed to explain the term: ${input.term}`);
    }
    return output;
  }
);
