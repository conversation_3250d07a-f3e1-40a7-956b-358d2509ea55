{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNativeScreens", "_reactNative", "_SearchBarNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "obj", "_extends", "assign", "bind", "target", "arguments", "length", "source", "key", "apply", "NativeSearchBar", "exports", "SearchBarNativeComponent", "NativeSearchBarCommands", "SearchBarNativeCommands", "SearchBar", "React", "Component", "constructor", "props", "nativeSearchBarRef", "createRef", "_callMethodWithRef", "method", "ref", "current", "console", "warn", "blur", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "render", "isSearchBarAvailableForCurrentPlatform", "View", "createElement", "_default"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AAKA,IAAAE,YAAA,GAAAF,OAAA;AAGA,IAAAG,yBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAE4C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAA2B,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAhB,UAAA,GAAAgB,GAAA,KAAAf,OAAA,EAAAe,GAAA;AAAA,SAAAC,SAAA,IAAAA,QAAA,GAAAV,MAAA,CAAAW,MAAA,GAAAX,MAAA,CAAAW,MAAA,CAAAC,IAAA,eAAAC,MAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAO,SAAA,CAAAC,MAAA,EAAAR,CAAA,UAAAS,MAAA,GAAAF,SAAA,CAAAP,CAAA,YAAAU,GAAA,IAAAD,MAAA,QAAAhB,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAU,MAAA,EAAAC,GAAA,KAAAJ,MAAA,CAAAI,GAAA,IAAAD,MAAA,CAAAC,GAAA,gBAAAJ,MAAA,YAAAH,QAAA,CAAAQ,KAAA,OAAAJ,SAAA,KAH5C;AAKO,MAAMK,eACmB,GAAAC,OAAA,CAAAD,eAAA,GAAGE,iCAA+B;AAC3D,MAAMC,uBAA8C,GAAAF,OAAA,CAAAE,uBAAA,GACzDC,kCAA8B;AAiBhC,MAAMC,SAAS,SAASC,cAAK,CAACC,SAAS,CAAiB;EAGtDC,WAAWA,CAACC,KAAqB,EAAE;IACjC,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,kBAAkB,gBAAGJ,cAAK,CAACK,SAAS,CAAC,CAAC;EAC7C;EAEAC,kBAAkBA,CAACC,MAAwC,EAAE;IAC3D,MAAMC,GAAG,GAAG,IAAI,CAACJ,kBAAkB,CAACK,OAAO;IAC3C,IAAID,GAAG,EAAE;MACPD,MAAM,CAACC,GAAG,CAAC;IACb,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,IAAI,CAACN,kBAAkB,CAACE,GAAG,IAAIX,uBAAuB,CAACe,IAAI,CAACJ,GAAG,CAAC,CAAC;EACnE;EAEAK,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,kBAAkB,CAACE,GAAG,IAAIX,uBAAuB,CAACgB,KAAK,CAACL,GAAG,CAAC,CAAC;EACpE;EAEAM,kBAAkBA,CAACC,IAAa,EAAE;IAChC,IAAI,CAACT,kBAAkB,CAACE,GAAG,IACzBX,uBAAuB,CAACiB,kBAAkB,CAACN,GAAG,EAAEO,IAAI,CACtD,CAAC;EACH;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACV,kBAAkB,CAACE,GAAG,IAAIX,uBAAuB,CAACmB,SAAS,CAACR,GAAG,CAAC,CAAC;EACxE;EAEAS,OAAOA,CAACC,IAAY,EAAE;IACpB,IAAI,CAACZ,kBAAkB,CAACE,GAAG,IAAIX,uBAAuB,CAACoB,OAAO,CAACT,GAAG,EAAEU,IAAI,CAAC,CAAC;EAC5E;EAEAC,YAAYA,CAAA,EAAG;IACb,IAAI,CAACb,kBAAkB,CAACE,GAAG,IAAIX,uBAAuB,CAACsB,YAAY,CAACX,GAAG,CAAC,CAAC;EAC3E;EAEAY,MAAMA,CAAA,EAAG;IACP,IAAI,CAACC,0DAAsC,EAAE;MAC3CX,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;MACD,OAAOW,iBAAI;IACb;IAEA,oBAAOlE,MAAA,CAAAa,OAAA,CAAAsD,aAAA,CAAC7B,eAAe,EAAAT,QAAA,KAAK,IAAI,CAACkB,KAAK;MAAEK,GAAG,EAAE,IAAI,CAACJ;IAAmB,EAAE,CAAC;EAC1E;AACF;AAAC,IAAAoB,QAAA,GAAA7B,OAAA,CAAA1B,OAAA,GAEc8B,SAAS"}